'use strict';


var admin = require("firebase-admin");
var serviceAccountOld = require("./oldlikepoint.json");

const dotenv = require('dotenv');
dotenv.config();
const express = require('express')
const app = express()
const bodyParser = require('body-parser');
var session = require('express-session')
var cryptico = require('cryptico');
var generator = require('generate-password');
var Promise = require('promise');
var await = require('await')``
var moment = require('moment-timezone');
// var moment = require('moment');
var elasticsearch = require('elasticsearch');
var await = require('await');
var request = require('request');
var bip39 = require('bip39')
var btcLib = require('bitcoinjs-lib');
var bs58check = require('bs58check');
const twilio = require('twilio');
var jwt = require('jsonwebtoken');
var crypto = require('crypto'),
  algorithm = 'aes-256-gcm',
  password = process.env.ENCRYPTION_KEY;

  var algorithmAdmin = 'aes-256-ctr';
  var passwordAdmin = 'A$127855sd';
  //ipfs
const ipfsClient = require('ipfs-http-client')
const ipfsCluster = require('ipfs-cluster-api-remove-name')
const fs = require('fs')
//yep
const algorithmToFront = 'aes-256-cbc';
var algorithmToFrontGCM = 'aes-256-gcm';
var serviceAccount = require("./"+process.env.FIREBASE);
const passwordToFront = process.env.passwordToFront;
const ivToFront = process.env.ivToFront;
// const passwordToFront = process.env.ENCRYPTION_KEY_FRONT;
// const ivToFront = process.env.IV_FRONT; 
const $log = require('log');
const bcrypt = require('bcrypt');
const IV_LENGTH = 16;
const ethers = require('ethers');
const accountSid = process.env.TWILIO_KEY;
const authToken = process.env.TWILIO_SECRET;
const clientTwilio = require('twilio')(accountSid, authToken);
const secrets = require('secret-sharing.js');
const uuidv4 = require('uuid/v4');
const randomstring = require("randomstring");
const uuidAPIKey = require('uuid-apikey');
const aws = require('aws-sdk');
const web3 = require('web3');
const ed25519 = require('ed25519-hd-key')
const mysql      = require('mysql');
// import { rateLimiterUsingThirdParty } from './middlewares';
var rateLimiterUsingThirdParty = require('./middlewares');


statement();
async function statement(){
  var con = mysql.createConnection({
      host     : process.env.RDS_HOST,
      user     : process.env.RDS_USERNAME,
      password : process.env.RDS_PASSWORD,
      database : process.env.RDS_DATABASE
    });
     
    con.connect();

    const sql = "SELECT amount,toAddress,fromAddress FROM logTransaction WHERE toAddress = ? OR fromAddress = ? ORDER BY blockNumber";

    con.query(sql, ['0xce5080f601656670ea06c92eba9632506fe54d14', '0xce5080f601656670ea06c92eba9632506fe54d14'], function (err, result, fields) {
      if (err) {
        con.end();
        console.log(err);
        $log('selectMysql : '+ err);
        // res.json({statusCode:404, result:result});
      }
      console.log(result.length);
      let balance = 0;
      for(let i=0;i<result.length;i++){
      	if(i==0){
      		console.log(result[i].amount);
      		balance = parseInt(result[i].amount);
      	}else{
      		if(result[i].toAddress == '0xce5080f601656670ea06c92eba9632506fe54d14' && result[i] == '0xce5080f601656670ea06c92eba9632506fe54d14'){
    			
      		}else{
 	      		if(result[i].toAddress == '0xce5080f601656670ea06c92eba9632506fe54d14'){
	      			balance += parseInt(result[i].amount);
	      		}else{
	      			balance -= parseInt(result[i].amount);
	      		}       			
      		}

      	}
      }

      console.log('current balance '+ balance/10e17);
       // res.json({statusCode:200, result:result});
  });	
}



 

