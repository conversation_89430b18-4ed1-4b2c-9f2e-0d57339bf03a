{"AWSTemplateFormatVersion": "2010-09-09", "Description": "The AWS CloudFormation template for this Serverless application", "Resources": {"ServerlessDeploymentBucket": {"Type": "AWS::S3::<PERSON><PERSON>", "Properties": {"BucketEncryption": {"ServerSideEncryptionConfiguration": [{"ServerSideEncryptionByDefault": {"SSEAlgorithm": "AES256"}}]}}}, "AppLogGroup": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/lambda/newlikewallet-dev-app"}}, "IamRoleLambdaExecution": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}, "Policies": [{"PolicyName": {"Fn::Join": ["-", ["dev", "newlikewallet", "lambda"]]}, "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogStream"], "Resource": [{"Fn::Sub": "arn:${AWS::Partition}:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/newlikewallet-dev*:*"}]}, {"Effect": "Allow", "Action": ["logs:PutLogEvents"], "Resource": [{"Fn::Sub": "arn:${AWS::Partition}:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/newlikewallet-dev*:*:*"}]}]}}], "Path": "/", "RoleName": {"Fn::Join": ["-", ["newlikewallet", "dev", {"Ref": "AWS::Region"}, "lambdaRole"]]}}}, "AppLambdaFunction": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": {"Ref": "ServerlessDeploymentBucket"}, "S3Key": "serverless/newlikewallet/dev/*************-2019-11-04T06:39:25.280Z/newlikewallet.zip"}, "FunctionName": "newlikewallet-dev-app", "Handler": "app/app.handler", "MemorySize": 256, "Role": {"Fn::GetAtt": ["IamRoleLambdaExecution", "<PERSON><PERSON>"]}, "Runtime": "nodejs10.x", "Timeout": 6}, "DependsOn": ["AppLogGroup", "IamRoleLambdaExecution"]}, "AppLambdaVersionjDroE1pcJop3GcnCOPVbYfhVVwB91gnzEygT0EEFCE": {"Type": "AWS::Lambda::Version", "DeletionPolicy": "<PERSON><PERSON>", "Properties": {"FunctionName": {"Ref": "AppLambdaFunction"}, "CodeSha256": "7PNvqnftQA1XDcz0f8mt2Z0eGjL5pXGgxd2uzMyVEHk="}}, "ApiGatewayRestApi": {"Type": "AWS::ApiGateway::RestApi", "Properties": {"Name": "dev-new<PERSON><PERSON>et", "EndpointConfiguration": {"Types": ["EDGE"]}}}, "ApiGatewayResourceProxyVar": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Fn::GetAtt": ["ApiGatewayRestApi", "RootResourceId"]}, "PathPart": "{proxy+}", "RestApiId": {"Ref": "ApiGatewayRestApi"}}}, "ApiGatewayMethodOptions": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "MethodResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Credentials": true}, "ResponseModels": {}}], "RequestParameters": {}, "Integration": {"Type": "MOCK", "RequestTemplates": {"application/json": "{statusCode:200}"}, "ContentHandling": "CONVERT_TO_TEXT", "IntegrationResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,DELETE,GET,HEAD,PATCH,POST,PUT'", "method.response.header.Access-Control-Allow-Credentials": "'false'"}, "ResponseTemplates": {"application/json": "#set($origin = $input.params(\"Origin\"))\n#if($origin == \"\") #set($origin = $input.params(\"origin\")) #end\n#if($origin.matches(\".*\")) #set($context.responseOverride.header.Access-Control-Allow-Origin = $origin) #end"}}]}, "ResourceId": {"Fn::GetAtt": ["ApiGatewayRestApi", "RootResourceId"]}, "RestApiId": {"Ref": "ApiGatewayRestApi"}}}, "ApiGatewayMethodProxyVarOptions": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "MethodResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Credentials": true}, "ResponseModels": {}}], "RequestParameters": {}, "Integration": {"Type": "MOCK", "RequestTemplates": {"application/json": "{statusCode:200}"}, "ContentHandling": "CONVERT_TO_TEXT", "IntegrationResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,DELETE,GET,HEAD,PATCH,POST,PUT'", "method.response.header.Access-Control-Allow-Credentials": "'false'"}, "ResponseTemplates": {"application/json": "#set($origin = $input.params(\"Origin\"))\n#if($origin == \"\") #set($origin = $input.params(\"origin\")) #end\n#if($origin.matches(\".*\")) #set($context.responseOverride.header.Access-Control-Allow-Origin = $origin) #end"}}]}, "ResourceId": {"Ref": "ApiGatewayResourceProxyVar"}, "RestApiId": {"Ref": "ApiGatewayRestApi"}}}, "ApiGatewayMethodAny": {"Type": "AWS::ApiGateway::Method", "Properties": {"HttpMethod": "ANY", "RequestParameters": {}, "ResourceId": {"Fn::GetAtt": ["ApiGatewayRestApi", "RootResourceId"]}, "RestApiId": {"Ref": "ApiGatewayRestApi"}, "ApiKeyRequired": false, "AuthorizationType": "NONE", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["AppLambdaFunction", "<PERSON><PERSON>"]}, "/invocations"]]}}, "MethodResponses": []}}, "ApiGatewayMethodProxyVarAny": {"Type": "AWS::ApiGateway::Method", "Properties": {"HttpMethod": "ANY", "RequestParameters": {}, "ResourceId": {"Ref": "ApiGatewayResourceProxyVar"}, "RestApiId": {"Ref": "ApiGatewayRestApi"}, "ApiKeyRequired": false, "AuthorizationType": "NONE", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["AppLambdaFunction", "<PERSON><PERSON>"]}, "/invocations"]]}}, "MethodResponses": []}}, "ApiGatewayDeployment1572849517017": {"Type": "AWS::ApiGateway::Deployment", "Properties": {"RestApiId": {"Ref": "ApiGatewayRestApi"}, "StageName": "dev"}, "DependsOn": ["ApiGatewayMethodOptions", "ApiGatewayMethodProxyVarOptions", "ApiGatewayMethodAny", "ApiGatewayMethodProxyVarAny"]}, "AppLambdaPermissionApiGateway": {"Type": "AWS::Lambda::Permission", "Properties": {"FunctionName": {"Fn::GetAtt": ["AppLambdaFunction", "<PERSON><PERSON>"]}, "Action": "lambda:InvokeFunction", "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "ApiGatewayRestApi"}, "/*/*"]]}}}}, "Outputs": {"ServerlessDeploymentBucketName": {"Value": {"Ref": "ServerlessDeploymentBucket"}}, "AppLambdaFunctionQualifiedArn": {"Description": "Current Lambda function version", "Value": {"Ref": "AppLambdaVersionjDroE1pcJop3GcnCOPVbYfhVVwB91gnzEygT0EEFCE"}}, "ServiceEndpoint": {"Description": "URL of the service endpoint", "Value": {"Fn::Join": ["", ["https://", {"Ref": "ApiGatewayRestApi"}, ".execute-api.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/dev"]]}}}}