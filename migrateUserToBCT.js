

const request = require('request');
var admin = require("firebase-admin");
var utils = require("./utils.js");
var mysql = require("mysql");

const hostRDS = "************";
const userRDS = "likecoinweb";
const passRDS = "2JtVdfMCC9MnBEwS";
const databaseLikecoin = "likecoin";

const databaseRDSConfig = {
    host: hostRDS,
    user: userRDS,
    password: passRDS,
    database: databaseLikecoin
};
const databaseRDS21CTConfig = {
      host     : process.env.RDS_HOST,
      user     : process.env.RDS_USERNAME,
      password : process.env.RDS_PASSWORD,
      database : process.env.RDS_DATABASE
}

// var serviceAccount = require("./"+process.env.FIREBASE);
// try{
// 	admin.initializeApp({
// 	  credential: admin.credential.cert(serviceAccount)
// 	});
// }catch(e) {

// }

let encryptSeed = async function(seed) {
	return new Promise((resolve, reject) =>{
		request.post({ url: 'https://checkairdrop.prachakij.com/en', form: { key: seed } }, function (err, httpResponse, body) {
			// console.log('pass decrypt !!');
			let toJson = JSON.parse(body);
			var seedEncrypt = toJson.result.data.result;
			resolve(seedEncrypt);
		});	
	});
}


let decryptMnemonicByUid = async function(uid) {
	return new Promise((resolve, reject) =>{
		const db = admin.firestore();
			db.collection('users').doc(uid).get().then(snapshotLike =>{
				utils.recoverySeedNoSecret(uid, 'no' + snapshotLike.data()._token.concatPassword, snapshotLike.data()._token.keyFirst, snapshotLike.data()._token.keyEncrypt, snapshotLike.data()._token.tag, 'no_number').then(seedNewLike => {
					resolve(seedNewLike);
				});
			});
	})
}
let decryptPrivateKeyUid = async function(uid) {
	return new Promise((resolve, reject) =>{
		const db = admin.firestore();
			db.collection('users').doc(uid).get().then(snapshotLike =>{
				utils.recoverySeedNoSecret(uid, 'no' + snapshotLike.data()._token.concatPassword, snapshotLike.data()._token.keyFirst, snapshotLike.data()._token.keyEncrypt, snapshotLike.data()._token.tag, 'no_number').then(seedNewLike => {
					utils.RetrievePKED2519(seedNewLike).then(pkNewLike =>{
						resolve(pkNewLike);
					})
				});
			});
	})
}


let insertNewAdminKeyBU = async function(BU_group, seedEncrypt, addressETH, fullname, type_bu, description, wallet_type) {
	return new Promise((resolve, reject) =>{
			var dates = new Date();
			var sql = "INSERT INTO adminBCT (BU_group ,create_time, update_time, update_user, privk, address, BU, type_bu, flag, description, wallet_type) VALUES ?";
			var values = [
			    [BU_group, dates.toISOString().substring(0, 10), dates.toISOString().substring(0, 10), 'insertNewAdminKeyBU', seedEncrypt, addressETH, fullname, type_bu, 1, description, wallet_type]
			];      
			var con21CT = mysql.createConnection(databaseRDS21CTConfig);
			var conRDS = mysql.createConnection(databaseRDSConfig);
			con21CT.query(sql, [values], function (err, result) {
				if (err) throw err;
					conRDS.query(sql, [values], function (err, result) {
						if (err) throw err;
							con21CT.end();
							conRDS.end();
							resolve(true);
					})
			});     
	})           
}
// encryptSeed('test').then(data =>{
// 	console.log(data);
// });

// decryptMnemonicByUid('FAXwspplwcSLYdk5UJEh9pE2u2h1').then(xx=>{
// console.log(xx);
// });
// decryptPrivateKeyUid('FAXwspplwcSLYdk5UJEh9pE2u2h1').then(xx=>{

// });

let migrateUserToBCT = function(uid, BU_group, fullname, type_bu, description, wallet_type) {
	return new Promise((resolve, reject) =>{
		decryptMnemonicByUid(uid).then(seed=>{
			utils.RetrievePKED2519(seed).then(pk =>{
				const addressETH = utils.retrieveAddress(pk);
					encryptSeed(seed).then(data =>{
					insertNewAdminKeyBU(BU_group, data, addressETH.toLowerCase(), fullname, type_bu, description, wallet_type).then(result =>{
						if(result) {
							resolve(true);
						}
					});
				});		
			})	
		});
	});
}


module.exports = {
	migrateUserToBCT,
	decryptPrivateKeyUid,
	decryptMnemonicByUid
}

