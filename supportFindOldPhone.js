'use strict';


var admin = require("firebase-admin");
var serviceAccountOld = require("./oldlikepoint.json");

const dotenv = require('dotenv');
dotenv.config();
const express = require('express')
const app = express()
const bodyParser = require('body-parser');
var session = require('express-session')
var cryptico = require('cryptico');
var generator = require('generate-password');
var Promise = require('promise');
var await = require('await')``
var moment = require('moment-timezone');
// var moment = require('moment');
var elasticsearch = require('elasticsearch');
var await = require('await');
var request = require('request');
var bip39 = require('bip39')
var btcLib = require('bitcoinjs-lib');
var bs58check = require('bs58check');
const twilio = require('twilio');
var jwt = require('jsonwebtoken');
var crypto = require('crypto'),
  algorithm = 'aes-256-gcm',
  password = process.env.ENCRYPTION_KEY;

  //ipfs
const ipfsClient = require('ipfs-http-client')
const ipfsCluster = require('ipfs-cluster-api-remove-name')
const fs = require('fs')
//yep
const algorithmToFront = 'aes-256-cbc';
var algorithmToFrontGCM = 'aes-256-gcm';
var serviceAccount = require("./"+process.env.FIREBASE);
const passwordToFront = process.env.passwordToFront;
const ivToFront = process.env.ivToFront;
// const passwordToFront = process.env.ENCRYPTION_KEY_FRONT;
// const ivToFront = process.env.IV_FRONT; 
const $log = require('log');
const bcrypt = require('bcrypt');
const IV_LENGTH = 16;
const ethers = require('ethers');
const accountSid = process.env.TWILIO_KEY;
const authToken = process.env.TWILIO_SECRET;
const clientTwilio = require('twilio')(accountSid, authToken);
const secrets = require('secret-sharing.js');
const uuidv4 = require('uuid/v4');
const randomstring = require("randomstring");
const uuidAPIKey = require('uuid-apikey');
const aws = require('aws-sdk');
const web3 = require('web3');
const ed25519 = require('ed25519-hd-key')
const mysql      = require('mysql');
const kms = new aws.KMS({
    accessKeyId: '********************', //credentials for your IAM user
    secretAccessKey: 'XtBvGx6pX+UssPhxbCc1xSrITpNf9IPQZuTIn/2a', //credentials for your IAM user
    region: 'ap-southeast-1'
});

// import { rateLimiterUsingThirdParty } from './middlewares';
var rateLimiterUsingThirdParty = require('./middlewares');
//console.log(JSON.stringify());

const auth = require('./middlewares/auth.js');

const contractAddressSmartContract = process.env.CONTRACT_LIKE;
const abiSmartContract = process.env.ABI_LIKE;

//delay function
let sleep = (time) => new Promise((resolve) => setTimeout(resolve, time));


admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

// admin.auth().createCustomToken('').then(token =>{
//     console.log(token);
// })





// const x= Buffer.from('', 'base64').toString('ascii')
// console.log(x);


const db = admin.firestore();


// db.collection('users').doc('').get().then(data=>{
// 	console.log(data.data().isTwoStep);

// 	if(data.data().isTwoStep == 'unddefined') {
// 		console.log('no data');
// 	}
// })

// db.collection('disabledUsers')
// 		.where('phone_number', '==', '+***********')
// 		.get()
// 		.then(snapshot =>{
// 			var i=0;
// 			snapshot.forEach(value =>{
// 				console.log(value.id);
// 				console.log(value.data());
// 			})
// 		})


// db.collection('ipfs')
// 	.where('uid', '==', 'mNTuxulIPlUCy2FP1T1eJzZdOqn1')
// 	.get()
// 	.then(snapshot =>{
// 		var i=0;
// 		snapshot.forEach(value =>{
// 			console.log(value.id);
// 			console.log(value.data());
// 		})
// 	})

// db.collection('users').where('phone_number', '==', '+***********')
// 		.get()
// 		.then(snapshot =>{
// 			var i=0;
// 			snapshot.forEach(value =>{
// 				console.log(value.id);
// 				console.log(value.data());
// 			})
// 		})

// findAddress();

// async function findAddress() {

// let i = 0;
// let data = [];
// let address = '0xce5080f601656670ea06c92eba9632506fe54d14';
// let toAddress = '0xce5080f601656670ea06c92eba9632506fe54d14';
// let fromAddress = '0xa965a4d6047266122281ff6dca0dcf3ca1e0296e';
// let name = '';
// if(toAddress == address) {
//   let snapshot = await db.collection('addressDNS').where('address', '==', fromAddress)
// 	.get();
// 	// console.log(snapshot);
// 	snapshot.forEach(value =>{
// 		// console.log(value.id);
// 		// console.log(value.data());
// 		name = value.data().name == undefined ? 'no' : value.data().name;
// 		console.log(name);
// 	})	
// }else{
//   		let snapshot = await db.collection('addressDNS').where('address', '==', fromAddress)
// 	.get();
// 	// console.log(snapshot);
// 	snapshot.forEach(value =>{
// 		// console.log(value.id);
// 		// console.log(value.data());
// 		name = value.data().name == undefined ? 'no' : value.data().name;
// console.log(name);
// 	})	
// }

// }


