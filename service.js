// ******************************************
'use strict';


var admin = require("firebase-admin");
var serviceAccountOld = require("./oldlikepoint.json");

const dotenv = require('dotenv');
dotenv.config();
const express = require('express')
const app = express()
const bodyParser = require('body-parser');
var session = require('express-session')
var cryptico = require('cryptico');
var generator = require('generate-password');
var Promise = require('promise');
var await = require('await')``
var moment = require('moment-timezone');
// var moment = require('moment');
var elasticsearch = require('elasticsearch');
var await = require('await');
var request = require('request');
var bip39 = require('bip39')
var btcLib = require('bitcoinjs-lib');
var bs58check = require('bs58check');
const twilio = require('twilio');
var jwt = require('jsonwebtoken');
var crypto = require('crypto'),
  algorithm = 'aes-256-gcm',
  password = process.env.ENCRYPTION_KEY;

  //ipfs
const ipfsClient = require('ipfs-http-client')
const ipfsCluster = require('ipfs-cluster-api-remove-name')
const fs = require('fs')
//yep
const algorithmToFront = 'aes-256-cbc';
var algorithmToFrontGCM = 'aes-256-gcm';
var serviceAccount = require("./"+process.env.FIREBASE);
const passwordToFront = process.env.passwordToFront;
const ivToFront = process.env.ivToFront;
// const passwordToFront = process.env.ENCRYPTION_KEY_FRONT;
// const ivToFront = process.env.IV_FRONT; 
const $log = require('log');
const bcrypt = require('bcryptjs');
const IV_LENGTH = 16;
const ethers = require('ethers');
const accountSid = process.env.TWILIO_KEY;
const authToken = process.env.TWILIO_SECRET;
const clientTwilio = require('twilio')(accountSid, authToken);
const secrets = require('secret-sharing.js');
const uuidv4 = require('uuid/v4');
const randomstring = require("randomstring");
const uuidAPIKey = require('uuid-apikey');
const aws = require('aws-sdk');
const web3 = require('web3');
// const ed25519 = require('ed25519-hd-key')
// const ed25519 = require('@noble/ed25519') // Changed to dynamic import
const mysql      = require('mysql');
const kms = new aws.KMS({
    accessKeyId: '********************', //credentials for your IAM user
    secretAccessKey: 'XtBvGx6pX+UssPhxbCc1xSrITpNf9IPQZuTIn/2a', //credentials for your IAM user
    region: 'ap-southeast-1'
});

// import { rateLimiterUsingThirdParty } from './middlewares';
var rateLimiterUsingThirdParty = require('./middlewares');
//console.log(JSON.stringify());

const auth = require('./middlewares/auth.js');

const contractAddressSmartContract = process.env.CONTRACT_LIKE;
const abiSmartContract = process.env.ABI_LIKE;




let getBalanceOracle = function getBalanceOracle() {
	return new Promise((resolve, reject) => {
	 	request({
		      method: 'POST',
		      uri: 'https://api.likepoint.io/getBalance',
		      headers: {
		          'Content-Type': 'application/x-www-form-urlencoded'
		      },
		      form: {
		          address: '******************************************'
		      }
		  }, (err, httpResponse, body) => {
		      if (err) {
		          console.log(err);
		      } else {
		   
		      	let newBalance = JSON.parse(body);
	
				  request({
				      method: 'POST',
				      uri: 'https://notify-api.line.me/api/notify',
				      headers: {
				          'Content-Type': 'application/x-www-form-urlencoded'
				      },
				      auth: {
				          'bearer': 'NZDpkJnnwlxkhmSJE68aBXDjqBh8pCqJawA4A0LP4BN'
				      },
				      form: {
				          message: 'ยอดคงเหลือในกระเป๋า Oracle : ****************************************** คือ ' + newBalance.result.data.balance + ' LIKE'
				      }
				  }, (err, httpResponse, body) => {
				      if (err) {
				          console.log(err);
				      } else {
				      	resolve();
				      }
				  });
	      }			
	  });
	});
 
}

module.exports.getBalanceOracle = getBalanceOracle;