let kue = require('kue');
var mqtt = require('mqtt')
const ethers = require('ethers');
const $log = require('log');
const moment = require('moment');
const dotenv = require('dotenv');
dotenv.config({ path: __dirname + '/.env' });
var admin = require("firebase-admin");
var request = require('request');
var serviceAccount = require("./"+process.env.FIREBASE);
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});
var client  = mqtt.connect('mqtt://mosquitto.likepoint.io')
 

let job = {
	date: moment().format("D-M-YYYY")
	// date: '26-8-2020'
}

// const db = admin.firestore();

// db.collection('logs').doc('rewardsAds').collection('21-8-2020').where('uid', '==', 'WWA41415LzLBUNCtwTKFCwrV7pT2')
// .get().then(snap =>{
// 	snap.forEach(doc => {
// 		console.log(doc.data());
// 	});
// })
console.log(job);
transferNewDevCronjobNew(job, 0, 0).then(data =>{
	process.exit();
});

// checkRound('******************************************');
// function checkRound(address) {
	
// }
// console.log('query');
// 	let db = admin.firestore();
// 	let xxx = '27-8-2020'
// 		 var rewardsAds = db.collection('logs')
// 		.doc('rewardsAds')
// 		.collection(xxx)
// 		.where('status', '==', 'pending')
// 		.get().then(snapshot =>{
		
// 					    snapshot.forEach(doc => {

// 					    	console.log(doc.data());
// 						    // if(doc.data().address === '0x6d835f1aabe6bf88fd4c7e3093035ef090178061'){
// 						    // 	console.log('yaya');
// 						    // }
// 						      // db.collection('logs')
// 						      // .doc('rewardsAds')
// 						      // .collection(xxx)
// 						      // .doc(doc.id)
// 						      // .update({
// 						      // 	status: 'active'
// 						      // });	
// 					    });
// 		})
// ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff

// approveUnlimited();
function approveUnlimited() {
			const contractAddr = process.env.CONTRACT_LIKE;
			const abiContract = JSON.parse(process.env.ABI_LIKE);

			const contractBatch = process.env.contractBatch;
			const abiBatch = JSON.parse(process.env.abiBatch);
			   
		    let url = process.env.NETWORK_RPC;
	        var provider = new ethers.providers.JsonRpcProvider(url);
	        let mnemonic = 'power legend olympic empower opinion student elite treat frog force decade snake';
	        let hdPath = "m/44'/60'/0'/0/0";
	        let walletMnemonic = ethers.Wallet.fromMnemonic(mnemonic ,hdPath)
	        let wallet = new ethers.Wallet(walletMnemonic.privateKey).connect(provider);
	        const erc20 = new ethers.Contract(contractAddr, abiContract, wallet);
	        const batch = new ethers.Contract(contractBatch, abiBatch, wallet);
	
			                erc20.approve('******************************************', '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff').then(approve =>{
			                 	console.log(approve);
			                  
			                    }); 


}

function transferNewDevCronjobNew(job, reSend =0, pending=0){
	return new Promise((resolve, reject) =>{
		let sleep = (time) => new Promise((resolve) => setTimeout(resolve, time))
		sleep(2000).then(() => {
			console.log('transferNew');
			console.log(job);
		let db = admin.firestore();

		 var rewardsAds = db.collection('logs')
		.doc('rewardsAds')
		.collection(job.date)
		.where('status', 'in', ['pending', 'inactive'])
		.get().then(snapshot => {
			console.log('snapshot');
			
		    if (snapshot.empty) {
		      console.log('No matching documents.');
		 		return resolve();

		    }  
		    let uid = [];
		    let i =0;
		    let amount = [];
		    let address = [];
		    let messageloop = [];
		    let totalAmount = 0;
		    snapshot.forEach(doc => {
		    	i++;
		    	console.log(doc.id);
		      db.collection('logs')
		      .doc('rewardsAds')
		      .collection(job.date)
		      .doc(doc.id)
		      .update({
		      	status: 'pending'
		      });	      
		      
		      if(doc.data().address != undefined){
		      	totalAmount += doc.data().rewards;
		      	uid.push(doc.id);
		      	amount.push(new ethers.utils.parseEther(doc.data().rewards.toString()));
		      	address.push(doc.data().address);
		      	messageloop.push('0x10ecb21114e7a5792865906d4126c642751b6943ab19447fc204d910c8ae640f');
		      }
		      

		    });

		    sleep(3000).then(() => {
			const contractAddr = process.env.CONTRACT_LIKE;
			const abiContract = JSON.parse(process.env.ABI_LIKE);

			const contractBatch = process.env.contractBatch;
			const abiBatch = JSON.parse(process.env.abiBatch);
			   
		    let url = process.env.NETWORK_RPC;
	        var provider = new ethers.providers.JsonRpcProvider(url);
	        let mnemonic = 'coyote flush plug stomach crash biology narrow ability absurd mammal timber rent';
	        let hdPath = "m/44'/60'/0'/0/0";
	        let walletMnemonic = ethers.Wallet.fromMnemonic(mnemonic ,hdPath)
	        let wallet = new ethers.Wallet(walletMnemonic.privateKey).connect(provider);
	        const erc20 = new ethers.Contract(contractAddr, abiContract, wallet);
	        const batch = new ethers.Contract(contractBatch, abiBatch, wallet);
			  provider.getTransactionCount(wallet.address).then(nonce =>{
			            console.log(address.length);
			        batch.batch(address, amount, contractAddr, messageloop).then(tx =>{
			                          	console.log(tx);
								          for(let i=0;i<uid.length;i++){
								          	console.log(uid[i] + ' '+ tx.hash + ' update');

										      db.collection('logs')
										      .doc('rewardsAds')
										      .collection(job.date)
										      .doc(uid[i])
										      .update({
										      	status:'active',
										      	tx: tx.hash
										      }).then(data =>{
										      	console.log('data ' + data);
										      });			

								          }		   
								         	sleep(4000).then(data =>{
								       	  		return resolve();
								       		});
			                                //เอา tx มาอัพเดท
                    }).catch(e=>{
                            console.log(e);

                          sleep(4000).then(data =>{
                               return transferNewDev(job, 1, 1);
                          })                             
                    });                     
			    }); 


		  })
		  .catch(err => {
		    console.log('Error getting documents', err);
		  });
		})
	    
	});
	})
	


}

// function transferNewDevCronjob(job, reSend =0, pending=0){
// 	return new Promise((resolve, reject) =>{
// 		let sleep = (time) => new Promise((resolve) => setTimeout(resolve, time))
// 		sleep(2000).then(() => {
// 			console.log('transferNew');
// 			console.log(job);
// 		let db = admin.firestore();

// 		 var rewardsAds = db.collection('logs')
// 		.doc('rewardsAds')
// 		.collection(job.date)
// 		.where('status', '==', pending == 0 ? 'inactive' : 'pending')
// 		.get().then(snapshot => {
// 			console.log('snapshot');
			
// 		    if (snapshot.empty) {
// 		      console.log('No matching documents.');
// 		 		return resolve();

// 		    }  
// 		    let uid = [];
// 		    let i =0;
// 		    let amount = [];
// 		    let address = [];
// 		    let messageloop = [];
// 		    let totalAmount = 0;
// 		    snapshot.forEach(doc => {
// 		    	i++;
// 		    	console.log(doc.id);
// 		      db.collection('logs')
// 		      .doc('rewardsAds')
// 		      .collection(job.date)
// 		      .doc(doc.id)
// 		      .update({
// 		      	status: 'pending'
// 		      });	      
		      
// 		      if(doc.data().address != undefined){
// 		      	totalAmount += doc.data().rewards;
// 		      	uid.push(doc.id);
// 		      	amount.push(new ethers.utils.parseEther(doc.data().rewards.toString()));
// 		      	address.push(doc.data().address);
// 		      	messageloop.push('0x10ecb21114e7a5792865906d4126c642751b6943ab19447fc204d910c8ae640f');
// 		      }
		      

// 		    });

// 		    sleep(3000).then(() => {
// 			const contractAddr = process.env.CONTRACT_LIKE;
// 			const abiContract = JSON.parse(process.env.ABI_LIKE);

// 			const contractBatch = process.env.contractBatch;
// 			const abiBatch = JSON.parse(process.env.abiBatch);
			   
// 		    let url = process.env.NETWORK_RPC;
// 	        var provider = new ethers.providers.JsonRpcProvider(url);
// 	        let mnemonic = 'coyote flush plug stomach crash biology narrow ability absurd mammal timber rent';
// 	        let hdPath = "m/44'/60'/0'/0/0";
// 	        let walletMnemonic = ethers.Wallet.fromMnemonic(mnemonic ,hdPath)
// 	        let wallet = new ethers.Wallet(walletMnemonic.privateKey).connect(provider);
// 	        const erc20 = new ethers.Contract(contractAddr, abiContract, wallet);
// 	        const batch = new ethers.Contract(contractBatch, abiBatch, wallet);
// 			  provider.getTransactionCount(wallet.address).then(nonce =>{
// 			            console.log(address.length);
// 			        var overrideOptions = {
// 			            nonce: nonce + 1
// 			        }
// 			        var overrideOptionsRe = {
// 			            nonce: nonce
// 			        }
// 			        erc20.allowance(wallet.address, contractBatch).then(approve => {
// 			         console.log(approve/10e17);
// 	 				if(new ethers.utils.parseUnits(approve.toString(), 'wei').toString() === new ethers.utils.parseUnits(totalAmount.toString(), 'ether').toString() || approve == 0)
// 			         {
// 			         	console.log('line 258');
// 			            if(reSend == 1 || new ethers.utils.parseUnits(approve.toString(), 'wei').toString() === new ethers.utils.parseUnits(totalAmount.toString(), 'ether').toString()){
// 			                          batch.batch(address, amount, contractAddr, messageloop, overrideOptionsRe).then(tx =>{
// 			                          	console.log(tx);
// 								          for(let i=0;i<uid.length;i++){
// 								          	console.log(uid[i] + ' '+ tx.hash + ' update');

// 										      db.collection('logs')
// 										      .doc('rewardsAds')
// 										      .collection(job.date)
// 										      .doc(uid[i])
// 										      .update({
// 										      	status:'active',
// 										      	tx: tx.hash
// 										      }).then(data =>{
// 										      	console.log('data ' + data);
// 										      });			

// 								          }		   
// 								         sleep(4000).then(data =>{
// 								       	  return resolve();
// 								       	});
// 			                                //เอา tx มาอัพเดท
// 			                            }).catch(e=>{
// 			                                    console.log(e);

// 			                                  sleep(4000).then(data =>{
// 			                                       return transferNewDev(job, 1, 1);
// 			                                  })                             
// 			                            });                     
			                   
// 			            }else{
// 			               console.log('line 282');
// 			                 // approve ให้สิทธิ์การคุมเหรียญ
// 			                erc20.approve(contractBatch, new ethers.utils.parseEther(totalAmount.toString()), {nonce: nonce}).then(approve =>{
// 			                 	console.log(approve);
// 			                    //เรียก batch function เพื่อโอนเหรียญ
// 			            		sleep(6000).then(data =>{
// 			                          batch.batch(address, amount, contractAddr, messageloop, overrideOptions).then(tx =>{
// 			                          	  	console.log(tx);
// 								          for(let i=0;i<uid.length;i++){
// 								          		console.log(uid[i] + ' '+ tx.hash + ' update');
// 										      db.collection('logs')
// 										      .doc('rewardsAds')
// 										      .collection(job.date)
// 										      .doc(uid[i])
// 										      .update({
// 										      	status:'active',
// 										      	tx: tx.hash
// 										      }).then(data =>{
// 										      	console.log('data ' + data);
// 										      });			          	
// 								          }	
// 								         	   sleep(4000).then(data =>{
// 								       	  return resolve();
// 								       	});
// 			                                //เอา tx มาอัพเดท
// 			                            }).catch(e=>{
// 			                                   console.log(e);
// 			                                  sleep(4000).then(data =>{
// 			                                       return transferNewDev(job, 1, 1);
// 			                                  })                           
// 			                            });    
// 			                    });                 
			                     
			                      


			               
// 			                });               
// 			                }
// 			             }else{
// 			             	console.log('line 315');
// 			                 //โอนให้เจ้าของก่อนแล้วไป approve ใหม่
// 			                 console.log(wallet.address);
// 			                 console.log(new ethers.utils.parseEther((approve/10e17).toString()));
// 			                 console.log(contractAddr);
// 			                 console.log(messageloop);
// 			                  batch.batch([wallet.address], [new ethers.utils.parseUnits(approve.toString(),'wei')], contractAddr, ['0x10ecb21114e7a5792865906d4126c642751b6943ab19447fc204d910c8ae640f']).then(tx =>{
// 			                        sleep(6000).then(data =>{
// 			                            return transferNewDev(job, 0, 1);
// 			                        })                   
// 			                  }).catch(e =>{
			              				
// 			                          request({
// 			                              method: 'POST',
// 			                              uri: 'https://notify-api.line.me/api/notify',
// 			                              headers: {
// 			                                  'Content-Type': 'application/x-www-form-urlencoded'
// 			                              },
// 			                              auth: {
// 			                                  'bearer': process.env.TOKEN_LINE_DEV
// 			                              },
// 			                              form: {
// 			                                  message: "giveAds มีปัญหา "+ e
// 			                              }
// 			                          }, (err, httpResponse, body) => {
// 			                              if (err) {
// 			                                  console.log(err);
// 			                              } else {
// 										   sleep(4000).then(data =>{
// 								       	  return resolve();
// 								       	});
// 			                              }
// 			                          });   		              			
// 			                      console.log(e);
// 			                  });
// 			             }
// 			        })  
			  
// 			        console.log(nonce);

			       
// 			         }); 


// 		  })
// 		  .catch(err => {
// 		    console.log('Error getting documents', err);
// 		  });
// 		})
	    
// 	});
// 	})
	


// }


