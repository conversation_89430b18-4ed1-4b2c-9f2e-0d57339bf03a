'use strict';

//main
//likecoin-2d12d-firebase-adminsdk-l8rpq-4eb4b82772.json
//test
//likewalletTest.json

const dotenv = require('dotenv');
dotenv.config();
var admin = require("firebase-admin");
var serviceAccount = require("./"+process.env.FIREBASE);

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

let urlNotification = [
    {
      "icon":
      "https://cdn.pixabay.com/photo/2016/10/03/15/42/button-1711966_960_720.png",
      "title": 'โอน-ถอน-จ่าย ไม่คิดค่าธรรมเนียม',
      "detail":
      'ฟรี ฟรี ฟรี!! ค่าธรรมเนียมธนาคาร "ฟรีค่าธรรมเนียม" เรารอมาเนิ่นนานเหลือเกิน สุดท้ายมันก็เกิดขึ้นจริงๆสักที (เฉพาะการใช้บริการธนาคารออนไลน์)',
      'url':'https://likepoint.io'
    },
    {
      "icon":
      "https://cdn.pixabay.com/photo/2016/10/03/15/42/button-1711966_960_720.png",
      "title": 'โอน-ถอน-จ่าย ไม่คิดค่าธรรมเนียม',
      "detail":
      'ฟรี ฟรี ฟรี!! ค่าธรรมเนียมธนาคาร "ฟรีค่าธรรมเนียม" เรารอมาเนิ่นนานเหลือเกิน สุดท้ายมันก็เกิดขึ้นจริงๆสักที (เฉพาะการใช้บริการธนาคารออนไลน์)',
      'url':'https://likepoint.io'
    },
    {
      "icon":
      "https://cdn.pixabay.com/photo/2016/10/03/15/42/button-1711966_960_720.png",
      "title": 'โอน-ถอน-จ่าย ไม่คิดค่าธรรมเนียม',
      "detail":
      'ฟรี ฟรี ฟรี!! ค่าธรรมเนียมธนาคาร "ฟรีค่าธรรมเนียม" เรารอมาเนิ่นนานเหลือเกิน สุดท้ายมันก็เกิดขึ้นจริงๆสักที (เฉพาะการใช้บริการธนาคารออนไลน์)',
      'url':'https://likepoint.io'
    },
    {
      "icon":
      "https://cdn.pixabay.com/photo/2016/10/03/15/42/button-1711966_960_720.png",
      "title": 'โอน-ถอน-จ่าย ไม่คิดค่าธรรมเนียม',
      "detail":
      'ฟรี ฟรี ฟรี!! ค่าธรรมเนียมธนาคาร "ฟรีค่าธรรมเนียม" เรารอมาเนิ่นนานเหลือเกิน สุดท้ายมันก็เกิดขึ้นจริงๆสักที (เฉพาะการใช้บริการธนาคารออนไลน์)',
      'url':'https://likepoint.io'
    }
  ];
const db = admin.firestore();

// db.collection('sandbox').doc('tier1').collection('whitelist').where('phone', '==', '+66861478085').get().then(snapshot =>{
// snapshot.forEach(doc => {
//   console.log(doc.id, '=>', doc.data());
// });
// })

db.collection('addressDNS').where('address', '==', '0xc2d23c1fcf2fcbbe0309d002ed8639f01e3aa69f').get().then(snapshot =>{
  snapshot.forEach(doc =>{
    console.log(doc.id, '=>', doc.data());
  })
})
// for(let i=0;i<urlNotification.length;i++){
// 	db.collection('bellnotifications').add(urlNotification[i]);
// }

