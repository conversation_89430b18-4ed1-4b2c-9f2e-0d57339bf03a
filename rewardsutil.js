'use strict';


var admin = require("firebase-admin");
var serviceAccountOld = require("./oldlikepoint.json");

const dotenv = require('dotenv');
dotenv.config();
const express = require('express')
const app = express()
const bodyParser = require('body-parser');
var session = require('express-session')
var cryptico = require('cryptico');
var generator = require('generate-password');
var Promise = require('promise');
var await = require('await')``
var moment = require('moment-timezone');
// var moment = require('moment');
var elasticsearch = require('elasticsearch');
var await = require('await');
var request = require('request');
var bip39 = require('bip39')
var btcLib = require('bitcoinjs-lib');
var bs58check = require('bs58check');
const twilio = require('twilio');
var jwt = require('jsonwebtoken');
var crypto = require('crypto'),
  algorithm = 'aes-256-gcm',
  password = process.env.ENCRYPTION_KEY;

  //ipfs
const ipfsClient = require('ipfs-http-client')
const ipfsCluster = require('ipfs-cluster-api-remove-name')
const fs = require('fs')
//yep
const algorithmToFront = 'aes-256-cbc';
var algorithmToFrontGCM = 'aes-256-gcm';
var serviceAccount = require("./"+process.env.FIREBASE);
const passwordToFront = process.env.passwordToFront;
const ivToFront = process.env.ivToFront;
// const passwordToFront = process.env.ENCRYPTION_KEY_FRONT;
// const ivToFront = process.env.IV_FRONT; 
const $log = require('log');
const bcrypt = require('bcrypt');
const IV_LENGTH = 16;
const ethers = require('ethers');
const accountSid = process.env.TWILIO_KEY;
const authToken = process.env.TWILIO_SECRET;
const clientTwilio = require('twilio')(accountSid, authToken);
const secrets = require('secret-sharing.js');
const uuidv4 = require('uuid/v4');
const randomstring = require("randomstring");
const uuidAPIKey = require('uuid-apikey');
const aws = require('aws-sdk');
const web3 = require('web3');
const ed25519 = require('ed25519-hd-key')
const mysql      = require('mysql');
const kms = new aws.KMS({
    accessKeyId: '********************', //credentials for your IAM user
    secretAccessKey: 'XtBvGx6pX+UssPhxbCc1xSrITpNf9IPQZuTIn/2a', //credentials for your IAM user
    region: 'ap-southeast-1'
});

// import { rateLimiterUsingThirdParty } from './middlewares';
var rateLimiterUsingThirdParty = require('./middlewares');
//console.log(JSON.stringify());

const auth = require('./middlewares/auth.js');

const contractAddressSmartContract = process.env.CONTRACT_LIKE;
const abiSmartContract = process.env.ABI_LIKE;

//delay function
let sleep = (time) => new Promise((resolve) => setTimeout(resolve, time));
// inQuireQR();


function currentAPYToday() {
    return new Promise((resolve, reject) => {
          var con  = mysql.createPool({
            connectionLimit : 100,
             connectTimeout  : 60 * 60 * 1000,
            acquireTimeout  : 60 * 60 * 1000,
            timeout         : 60 * 60 * 1000,           
            host     : process.env.RDS_HOST,
            user     : process.env.RDS_USERNAME,
            password : process.env.RDS_PASSWORD,
            database : process.env.RDS_DATABASE
          });
          const sql = "SELECT * FROM uniswap WHERE owner = ? AND status_active = ? AND create_time BETWEEN ? AND ?";
          const curDate = moment().format('YYYY-MM-DD');
          const lastDate = moment().add(1, 'days').format('YYYY-MM-DD');

          con.query('SELECT SUM(amount)/10e17 as totalLocked FROM locked_likepoint', function(errc, resultc, fieldsc){
            if(errc) throw errc;
 
 
         con.query('SELECT * FROM uniswap WHERE owner = ? AND create_time BETWEEN ? AND ? ', ['BACKUP-LIKEPOINT', curDate, lastDate], function (err, result, fields) {
             console.log(result.length);
             if(err){
                  con.end();
                 $log("oracleFeedRewardsDaily : "+err);
                 console.log(err);
             }
 
             console.log(result);
 
  
             let countData = 0;
             if(result.length > 0){
                 //rewards
                 let sumRewards = 0;
                 //init fund
                 let sumFund = 0;
 
                 for(var i=0;i<result.length;i++){
                     countData += 1;
                     console.log(result[i].diff_balance);
                     
                     if(result[i].pool === 'CELSIUS'){
                         sumRewards += Number(result[i].profit);
                         sumFund += Number(result[i].init_fund);
                     }else{
                         sumRewards += Number(result[i].diff_balance);
                         sumFund += Number(result[i].init_fund);
                     }
 
 
                 }
                 //total rewards
                 console.log("sum Rewards " + sumRewards);
                 //total init func
                 console.log("sumFund " + sumFund);
                 //APY
                 console.log('apy : ' + sumRewards/sumFund*100*365);
                 const apy = sumRewards/sumFund*100*365/2;
                 console.log("total lock : "+ resultc[0]["totalLocked"]);
 
                 let rewardsToday = parseInt(resultc[0]["totalLocked"]*apy/100/365);
 
                 //50/50%
                 console.log(parseInt((sumRewards*31.3/2*100)));
 
                 // let rewardsToday = parseInt(sumRewards*31.3/2*100);
 
                 console.log('today reward : ' + rewardsToday);
                 notifyAllMember(apy).then(data =>{
                     resolve();
                 });
                }
                });
            });	
    });
}

function notifyAllMember(apy) {
    return new Promise((resolve, reject) =>{
        var con  = mysql.createPool({
            connectionLimit : 1000,
                         connectTimeout  : 60 * 60 * 1000,
            acquireTimeout  : 60 * 60 * 1000,
            timeout         : 60 * 60 * 1000,  
            host     : process.env.RDS_HOST_AGS,
            user     : process.env.RDS_USERNAME_AGS,
            password : process.env.RDS_PASSWORD_AGS,
            database : 'PPP7'
          });
    
          con.query('SELECT token_line FROM PKGemployee WHERE status = ?',['Y'], function(err, result, fields){
            if(err) throw err;
        
            let emoji;
            if(apy < 5){
                emoji = "😀";
            }else if(apy < 7){
                emoji = "💸";
            }else{
                emoji = "🚀";
            }
   
            
            
            
            
           
            let message = "\n💰 รายได้ Lock&Earn วันนี  "+apy.toFixed(2)+" % APY (ต่อปี) \n% รายได้จะไม่เท่ากันในแต่ละวันนะครับ 💸\nเพื่อนๆ ที่ Locked กันไว้แล้วก็อย่าลืมกดรับรางวัลกันน้าา\nใครที่ยังไม่ได้ Lock ก็ไปที่ LikeWallet ได้เลย\n\"Lock LIKE วันนี้ แล้วรับรางวัลได้ทุกวันกับ LikeWallet\"";
            for(let i=0;i<result.length;i++){
                
                if(result[i].token_line != null){
                    console.log(result[i].token_line);
                    sendLineNotify(result[i].token_line, message);
                }
    
                let sleep = (time) => new Promise((resolve) => setTimeout(resolve, time))
                sleep(5000).then(() => {
                    // process.exit();
                    resolve();
                });
             
            }
          });
    });
 

}



function notifyAllMemberByNoti(mmm) {
    return new Promise((resolve, reject) =>{
        var con  = mysql.createPool({
            connectionLimit : 1000,
                         connectTimeout  : 60 * 60 * 1000,
            acquireTimeout  : 60 * 60 * 1000,
            timeout         : 60 * 60 * 1000,  
            host     : process.env.RDS_HOST_AGS,
            user     : process.env.RDS_USERNAME_AGS,
            password : process.env.RDS_PASSWORD_AGS,
            database : 'PPP7'
          });
    
          con.query('SELECT token_line FROM PKGemployee WHERE status = ?',['Y'], function(err, result, fields){
            if(err) throw err;
        
            let emoji;
            if(apy < 5){
                emoji = "😀";
            }else if(apy < 7){
                emoji = "💸";
            }else{
                emoji = "🚀";
            }
   
            
            
            
            
           
            let message = mmm;
            for(let i=0;i<result.length;i++){
                console.log(result.length);
                if(result[i].token_line != null){
                    console.log(result[i].token_line);
                    sendLineNotify(result[i].token_line, message);
                }
    
                let sleep = (time) => new Promise((resolve) => setTimeout(resolve, time))
                sleep(5000).then(() => {
                    // process.exit();
                    resolve();
                });
             
            }
          });
    });
 

}



function sendLineNotify(token, message){

    request({
        method: 'POST',
        uri: 'https://notify-api.line.me/api/notify',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        auth: {
            'bearer': token
        },
        form: {
            message: message + "\n",
            imageThumbnail: "https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon16088861241608886108327%402x.jpg",
            imageFullsize: "https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon16088861241608886108327%402x.jpg"
        }
    }, (err, httpResponse, body) => {
        if (err) {
            console.log(err);
        } else {

        }
    });       
}


//##########################
//##########################
/// INQUIRY ZONE END

function getReportToday() {
    var con  = mysql.createPool({
        connectionLimit : 10,
        host     : process.env.RDS_HOST,
        user     : process.env.RDS_USERNAME,
        password : process.env.RDS_PASSWORD,
        database : process.env.RDS_DATABASE
      });

     con.query('SELECT * FROM uniswap WHERE owner = ? ORDER BY running DESC LIMIT 4', ['BACKUP-LIKEPOINT'], function (err, result, fields) {
         console.log(result.length);
         if(err){
              con.end();
             $log("oracleFeedRewardsDaily : "+err);
             console.log(err);
         }

         console.log(result);
         let data = [];
         for(let v=0;v<result.length;v++){
            data.push(result[v].create_time);
         }

         let countData = 0;
         if(result.length > 0){
                sendLineNotify('*******************************************', data);
            }
        });    
    
}

module.exports.currentAPYToday = currentAPYToday;
module.exports.getReportToday = getReportToday;

module.exports.notifyAllMemberByNoti = notifyAllMemberByNoti;