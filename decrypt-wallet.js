const crypto = require('crypto');
const fs = require('fs');
const { ethers } = require('ethers');

// ฟังก์ชันสำหรับถอดรหัส mnemonic
function decryptMnemonic(encryptedData, iv, password) {
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync(password, 'salt', 32);
    const decipher = crypto.createDecipheriv(algorithm, key, Buffer.from(iv, 'hex'));
    
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
}

// ฟังก์ชันสำหรับโหลดและถอดรหัส wallet
function loadWallet(filename, password) {
    try {
        // อ่านไฟล์ wallet
        const walletData = JSON.parse(fs.readFileSync(filename, 'utf8'));
        
        // ถอดรหัส mnemonic
        const mnemonic = decryptMnemonic(
            walletData.encryptedMnemonic,
            walletData.iv,
            password
        );
        
        console.log('=== Wallet Information ===');
        console.log('Address:', walletData.address);
        console.log('Public Key:', walletData.publicKey);
        console.log('Mnemonic:', mnemonic);
        console.log('Created At:', walletData.createdAt);
        console.log('Derivation Path:', walletData.derivationPath);
        
        // สร้าง wallet object จาก mnemonic
        const wallet = ethers.Wallet.fromMnemonic(mnemonic, walletData.derivationPath);
        
        // ตรวจสอบว่า address ตรงกันหรือไม่
        if (wallet.address.toLowerCase() === walletData.address.toLowerCase()) {
            console.log('✅ Wallet verification successful!');
        } else {
            console.log('❌ Wallet verification failed!');
        }
        
        return {
            wallet: wallet,
            mnemonic: mnemonic,
            data: walletData
        };
        
    } catch (error) {
        console.error('Error loading wallet:', error.message);
        return null;
    }
}

// ทดสอบการโหลด wallet
const filename = 'wallet-******************************************.json';
const password = 'my-secure-password';

if (fs.existsSync(filename)) {
    const result = loadWallet(filename, password);
    
    if (result) {
        console.log('\n=== Testing Wallet Functions ===');
        console.log('Private Key:', result.wallet.privateKey);
        console.log('Address from wallet:', result.wallet.address);
    }
} else {
    console.log('Wallet file not found:', filename);
}
