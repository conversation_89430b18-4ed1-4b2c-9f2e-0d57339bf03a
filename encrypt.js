const EthereumEncryption = require('ethereum-encryption');

  // const privateKey = EthereumEncryption.createPrivateKey();
const privateKey = 'd571483db27eb3248dfbed40cfe339b14aea927531388f76d1b9d5e0281d414c';
  console.log(privateKey);

   const publicKey = EthereumEncryption.publicKeyFromPrivateKey(
      privateKey
  );
     const address = EthereumEncryption.publicKeyToAddress(
      publicKey
  );

    const encrypted = EthereumEncryption.encryptWithPublicKey(
        publicKey, // publicKey
        'witch collapse practice feed shame open despair creek road again ice least' // data
    );

         console.log(encrypted);

    const message = EthereumEncryption.decryptWithPrivateKey(
        privateKey, // privateKey
        encrypted // encrypted-data
    );
    console.log(message);