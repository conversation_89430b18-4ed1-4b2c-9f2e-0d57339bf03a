'use strict';

var admin = require("firebase-admin");
var serviceAccountOld = require("./oldlikepoint.json");

const dotenv = require('dotenv');
dotenv.config();
const express = require('express')
const app = express()
const bodyParser = require('body-parser');
var session = require('express-session')
var cryptico = require('cryptico');
var generator = require('generate-password');
var Promise = require('promise');
var await = require('await')``
var moment = require('moment-timezone');
// var moment = require('moment');
var elasticsearch = require('elasticsearch');
var await = require('await');
var request = require('request');
var bip39 = require('bip39')
var btcLib = require('bitcoinjs-lib');
var bs58check = require('bs58check');
const twilio = require('twilio');
var crypto = require('crypto'),
  algorithm = 'aes-256-gcm',
  password = process.env.ENCRYPTION_KEY;

  //ipfs
const ipfsClient = require('ipfs-http-client')
const ipfsCluster = require('ipfs-cluster-api-remove-name')
const fs = require('fs')
//yep
const algorithmToFront = 'aes-256-cbc';
var algorithmToFrontGCM = 'aes-256-gcm';
var serviceAccount = require("./"+process.env.FIREBASE);
const passwordToFront = process.env.passwordToFront;
const ivToFront = process.env.ivToFront;
// const passwordToFront = process.env.ENCRYPTION_KEY_FRONT;
// const ivToFront = process.env.IV_FRONT; 
const $log = require('log');
const bcrypt = require('bcrypt');
const IV_LENGTH = 16;
const ethers = require('ethers');
const accountSid = process.env.TWILIO_KEY;
const authToken = process.env.TWILIO_SECRET;
const clientTwilio = require('twilio')(accountSid, authToken);
const secrets = require('secret-sharing.js');
const uuidv4 = require('uuid/v4');
const randomstring = require("randomstring");
const uuidAPIKey = require('uuid-apikey');
const aws = require('aws-sdk');
const web3 = require('web3');
const ed25519 = require('ed25519-hd-key')
const mysql      = require('mysql');
const kms = new aws.KMS({
    accessKeyId: '********************', //credentials for your IAM user
    secretAccessKey: 'XtBvGx6pX+UssPhxbCc1xSrITpNf9IPQZuTIn/2a', //credentials for your IAM user
    region: 'ap-southeast-1'
});


//console.log(JSON.stringify());

var convertlikepoint = require('./convertlikepoint.js');
var userManagement = require('./userManagement.js');
var utilitiesService = require('./utilities.js');
var getHistory = require('./getHistory.js');
var quickpayshop = require('./quickpayshop.js');
var thaiqr = require('./thaiqr.js');
var ipfs_control = require('./ipfs_control.js');
var apiBank = require('./apiBank.js');
var like_connect = require('./likewallet_connection.js');
var mqtt = require('./mqtt_publish.js');


 mqtt.publishToKPLUS();
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.json());

const contractAddressSmartContract = process.env.CONTRACT_LIKE;
const abiSmartContract = process.env.ABI_LIKE;

const openpgp = require('openpgp');
const pubkey = `-----BEGIN PGP PUBLIC KEY BLOCK-----
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=tQeo
-----END PGP PUBLIC KEY BLOCK-----
`;
app.use(function (req, res, next) {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept");
  next();
});


var listenPort = process.env.PORT_RESET;

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

var secondaryAppConfig = {
    credential: admin.credential.cert(serviceAccountOld)
};

// Initialize another app with a different config
var secondary = admin.initializeApp(secondaryAppConfig, "secondary");

// do not use a global iv for production, 
// generate a new one for each encryption
var iv = process.env.IV;


////############## END MANAGE USER #############///////
app.listen(listenPort, function () {
  console.log('Example app listening on port ' + listenPort + '!')
})
