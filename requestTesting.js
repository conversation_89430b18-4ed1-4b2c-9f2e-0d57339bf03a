var request = require('request');
console.log('starting...');
var headers = {
    'Connection': 'keep-alive',
    'Cache-Control': 'max-age=0',
    'sec-ch-ua': '^\^',
    'sec-ch-ua-mobile': '?0',
    'Origin': 'https://upload.prachakij.com',
    'Upgrade-Insecure-Requests': '1',
    'DNT': '1',
    'Content-Type': 'application/x-www-form-urlencoded',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-User': '?1',
    'Sec-Fetch-Dest': 'document',
    'Referer': 'https://upload.prachakij.com/login',
    'Accept-Language': 'en-US,en;q=0.9',
    'Cookie': '_ga=GA1.2.1198164023.1573455996; cwc_consent=^{^\^session_id^\^:^\^8d5eda88-3fd9-4e64-814b-4b975d6a7607^\^,^\^consentVersion^\^:^\^^\^,^\^purposes^\^:^{^\^289^\^:^{^\^name^\^:^\^Necessary^\^,^\^accepted^\^:true^},^\^290^\^:^{^\^name^\^:^\^Analytics^\^,^\^accepted^\^:true^},^\^291^\^:^{^\^name^\^:^\^Marketing^\^,^\^accepted^\^:true^}^}^}; XSRF-TOKEN=eyJpdiI6IlNpTXZDXC9PSDVUa1o1XC9kM2FrZzBNdz09IiwidmFsdWUiOiJRN2UweDdmd2lESXFXd0c1amFQcUZkeSs2cFpUaktmZ3lJMjdWNDdoQkhRcTVNdktBMStUb2ExbWY0KzdMUFwvcTI5YzRoTEpUd0NoZkxIeFNuamQ3NWc9PSIsIm1hYyI6IjE2NzVlOWQ5MzdjMTU1OGJiYzcyYWIyNmIyMzdkZTE4M2YyYmE0ZjU4MjEwMzhjMzY2ZGQ4OWVjNmNhOGE2ZTgifQ^%^3D^%^3D; laravel_session=eyJpdiI6IkRSUG9mT1dua1lSczRKOXFlcWpJS3c9PSIsInZhbHVlIjoiYVdVM29cL1p2QlczQ3c2dHZFTDR5UDBwZW5NZlhrZ25WeE9iMlM0OHlmVkFWV0pYWTRTcmZzWnNQajFaMkhCbElIbEJ5SCsrR0Q1T2NEZ2VYTFVXVnBnPT0iLCJtYWMiOiIxM2I0OTY5YmRjZDY4OWJmNmQwYTA5ZWQ5NGE4YzVlMmJhYzM2OWYwOGE4YTVhY2YyYmUxZmViODYwMTg5YzNhIn0^%^3D'
};

var dataString = '_token=EsEiEJ4Ecxv6U71xggRzj9gUhsd7i2h7u0rnmuuW&email=<EMAIL>&password=123456';

var options = {
    url: 'https://upload.prachakij.com/login',
    method: 'POST',
    headers: headers,
    body: dataString
};

function callback(error, response, body) {
    if (!error && response.statusCode == 200) {
        console.log(body);
    }else{
        console.log(error);
    }
}

request(options, callback);
