
module.exports = function (
	 app,
	 admin,
	 serviceAccount,
	 dotenv,
	 cryptico,
	 generator,
	 moment,
	 elasticsearch,
	 request,
	 bip39,
	 btcLib,
	 bs58check,
	 twilio,
	 crypto,
	 algorithm,
	 password,
	 algorithmToFront,
	 $log,
	 bcrypt,
	 IV_LENGTH,
	 ethers,
	 accountSid,
	 authToken,
	 clientTwilio,
	 secrets,
	 uuidv4,
	 randomstring,
	 uuidAPIKey,
	 aws,
	 ed25519,
	 mysql,
	 kms,
	 decryptToFrontInternal,
	 encrypt,
	 randomText,
	 recoverySeed,
	 encryptToFront,
	 encryptWithPassword,
	 openpgp,
	 pubkey,
	 secondary,
	 selectMysql,
	 ipfsClient,
	 ipfsCluster,
	 fs	 
	){

let uploadIPFS = function uploadIPFS(data){
	return new Promise((resolve, reject)=>{
		let sleep = (time) => new Promise((resolve) => setTimeout(resolve, time))
		// connect to ipfs daemon API server
		const cluster = ipfsCluster(process.env.IPFS_CLUSTER_HOST);
		//upload file to cluster
		let uploadContent = function() {
			cluster.add(Buffer.from(data),{name: 'uid'}, (err, result) => {
		  	if(err){
				console.error(err);
		          // return sleep(2000).then(() => {
		          //   return resolve(transfer(to, pk, amount));
		          // });
		          reject();
		  	}else{
				console.log(result)
				resolve(result);
		  	}
		})
		}		
		uploadContent();

	});
}

// uploadIPFS('this test upload data from dddipfs_cluste hahaddr').then(data=>{
// 	console.log(data[0].hash);
// }).catch(e=>{
// 	console.log(e);
// });
}

