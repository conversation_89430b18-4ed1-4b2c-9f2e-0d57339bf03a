

var bip39 = require('bip39')

const ethers = require('ethers');
var xxx = Buffer.from('Y2VtZW50IHBpZWNlIHNraXJ0IHN3aW0gcGxhbmV0IG92YWwgbWFwbGUgZ2VuaXVzIGF2b2lkIHRoZW1lIGluaXRpYWwgc2FuZA', 'base64').toString('ascii')

console.log(xxx);

// const hexSeed = bip39.mnemonicToSeedSync(xxx).toString('hex');

    // const privateKey = ed25519.derivePath("m/0'/2147483647'/0'/2147483646'/0'", hexSeed);
      let path = "m/44'/60'/0'/0/0";
      let mnemonicWallet = ethers.Wallet.fromMnemonic(xxx.toString(), path);

      console.log(mnemonicWallet.privateKey);