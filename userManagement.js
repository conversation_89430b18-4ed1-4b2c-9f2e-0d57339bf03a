
const { decrypt } = require("cryptico");
const nodemailer = require("nodemailer");
module.exports = function (
	 app,
	 admin,
	 serviceAccount,
	 dotenv,
	 cryptico,
	 generator,
	 moment,
	 elasticsearch,
	 request,
	 bip39,
	 btcLib,
	 bs58check,
	 twilio,
	 crypto,
	 algorithm,
	 password,
	 algorithmToFront,
	 $log,
	 bcrypt,
	 IV_LENGTH,
	 ethers,
	 accountSid,
	 authToken,
	 clientTwilio,
	 secrets,
	 uuidv4,
	 randomstring,
	 uuidAPIKey,
	 aws,
	 ed25519,
	 mysql,
	 kms,
	 decryptToFrontInternal,
	 encrypt,
	 randomText,
	 recoverySeed,
	 encryptToFront,
	 encryptWithPassword,
	 openpgp,
	 pubkey,
	 secondary,
	 selectMysql,
	 getDigit,
	 encryptKMS,
 	 decryptKMS,
 	 deSecretSharing,
 	 uploadIPFS,
 	 jwt,
 	 decryptWithPassword,
 	 sendSMSTwilioByCheck,
 	 checkOTP,
 	 RetrievePKED2519,
 	 recoverySeedNoSecret,
 	 generateRefCode,
 	 retrieveAddress,
 	 auth,
 	 recoveryMaster<PERSON>eyLike,
 	 decrypt,
 	 recoverySeedNoSecretDisabled,
 	 recoverySeedNoSecretForTransfer,
 	 recoverySeedNoSecretDisabledActive,
 	 databaseRDSConfig,
 	databaseRDS21CTConfig
	){



app.post('/updateEmail', function(req, res){
	const apikey = req.body.apikey;
	const secret = req.body.secret;
	const uid = req.body.uid;
	const email = req.body.email;
	const password = req.body.password;
	const masterkey = req.body.masterkey;


if(apikey == "manzer" && secret=="manzergaming" && masterkey == "hpdT834PiLhNhRawsIjA5i12YOyERQZm"){
	const db = admin.firestore();
	      admin.auth().updateUser(uid, {
	        email:email,
	        password: password
	      }).then(data =>{
	      	res.json({statusCode:200, result:true});
	      });
}
});
//partner app
//##############//
app.post('/partnerOTPForCreateUser', function(req, res){
  // APIKEY_CONNECT=61da7951-8ffb-494d-98b2-37994652fcfc
  // SECRETKEY_CONNECT=b6a45dc2-4467-45be-b1e1-0611395e64fc
  const apikey = req.body.apikey;
  const secret = req.body.secret;
  const phone_number = req.body.phone_number;
  const appsignature = req.body.appsignature;
  const application = req.body.application;
  console.log(apikey);
  console.log(secret);

  if(process.env.APIKEY_CONNECT == apikey && process.env.SECRETKEY_CONNECT == secret){
  	const db = admin.firestore();
        var client = new elasticsearch.Client({
            host: process.env.ELASTIC_SEARCH,
                    apiVersion: process.env.ELASTICVERSION,
        });
    //    const start = async function () {
    //     const response = await client.create({
    //             index: 'likewallet',
    //             type: 'logsCreateUserFromPartner',
    //             body: {
    //             	doc:{
    //                     mobile: phone_number,
    //                     application: application,
    //                     appsignature: appsignature,
    //                     application: application,
    //                     time: moment().unix()
    //             	},
    //             	doc_as_upsert : true
    //             }
    //         });
    		//วางในนี้เพื่อเก็บล็อคเนาะ
  		// }
 			request.post({ url: process.env.DOMAIN+ '/checkExistUser', form: { 
		        apiKey: process.env.APIKEY,
		        secretKey: process.env.SECRETKEY,
		        phone_number: phone_number
		    } }, function (err, httpResponse, body) {
		    	console.log(body);
		    	const decodeBody = JSON.parse(body);
		    	if(decodeBody.result == false){
			    	const digit = Math.floor(100000 + Math.random() * 900000)
	    			const docID = "OTP" + phone_number.split('+')[1];
			    	//function sendSMSTwilioByCheck(db, docID, digit, phone_number, loop=0, application='Likewallet', appSig)
			    	sendSMSTwilioByCheck(db, docID, digit, phone_number, 0, application, appsignature).then(data=>{
			    		if(data.statusCode == 200){
			    			res.json({statusCode:200, result:"OTP is sent"});
			    		}else{
			    			res.json({statusCode:404, reason: "something is wrong contact Prapat"});
			    			
			    		}
			    	}).catch(e =>{
			    		res.json({statusCode:404, reason: "something is wrong contact Prapat"});
			    	})

		    	}else{
					res.json({statusCode:202, result:"phone number is duplicate"});
		    	}

		    }); 		
}

});

///create user จาก แอพอื่น
app.post('/partnerVerifyAndCreateUser', function(req, res){
	const otp = req.body.otp;
	const apikey = req.body.apikey;
	const secret = req.body.secret;	

	const firstname = req.body.firstname;
	const lastname = req.body.lastname;

	let phone_number = req.body.phone_number;
	const appsignature = req.body.appsignature;
	const application = req.body.application;

			const callVerify = async function() {
			    try {
			      const db = admin.firestore();
			      const docID = "OTP" + phone_number.split('+')[1];
			      verify = await checkOTP(db, docID, otp);

		           admin.auth().createUser({
		            phoneNumber: phone_number,
		            displayName: firstname + " " + lastname,
		            disabled: false
		          }).then((userRecord) => {
		          	// function partnerCreateUser(phone_number, firstName, lastname)
		          	partnerCreateUser(phone_number, firstname, lastname, application).then(data =>{
		          		if(data.statusCode == 200){
		          			res.json(data);
		          		}else{
		          			res.json({statusCode:404, reason: "Something is wrong"});
		          		}
		          	}).catch(e =>{
		          		console.log(e);
		          		res.json({statusCode:404, reason: "Something is wrong"});
		          	})
		                         
		          }).catch(e =>{
		            console.log(e);
		              let data = {
		                statusCode:204,
		                result: 'update failed'
		              }
		              res.json(data); 
		          });	      
			      // res.json({statusCode:200, result:'verify'});
			    } catch (err) {
			       res.json({statusCode:404, reason: "OTP is wrong"});
			    }		
			}
			callVerify();
});

//สร้าง user ได้ตรง ๆเลย
app.post('/adminCreateUserWithEmail', function(req, res){
	const apikey = req.body.apikey;
	const secret = req.body.secret;	
	const email_address = req.body.email_address;
	const mnemonic = req.body.mnemonic;

	let firstname = req.body.fullname;
	let lastname = req.body.fullname;

	const application = req.body.application;
	if(firstname == ''){
		firstname = firstname;
	}
	if(lastname == ''){
		lastname = 'no';
	}

  if(process.env.APIKEY_CONNECT == apikey && process.env.SECRETKEY_CONNECT == secret){
			const callVerify = async function() {
			    try {
			      const db = admin.firestore();
			      console.log('228 createad admin');
		           admin.auth().createUser({
					email: email_address,
					password: email_address.split('@')[0]+'123456',
		            displayName: firstname + " " + lastname,
		            disabled: false
		          }).then((userRecord) => {
		          	// function partnerCreateUser(phone_number, firstName, lastname)
		          	adminCreateUser(email_address, firstname, lastname, mnemonic).then(data =>{
		          		if(data.statusCode == 200){
		          			res.json(data);
		          		}else{
		          			res.json({statusCode:404, reason: "Something is wrong"});
		          		}
		          	}).catch(e =>{
		          		console.log(e);
		          		res.json({statusCode:404, reason: "Something is wrong"});
		          	})
		                         
		          }).catch(e =>{
		            console.log(e);
		              let data = {
		                statusCode:204,
		                result: 'already user'
		              }
		              res.json(data); 
		          });	      
			      // res.json({statusCode:200, result:'verify'});
			    } catch (err) {
			       res.json({statusCode:404, reason: "Admin is wrong"});
			    }		
			}
			callVerify();

}
});


function adminCreateUser(email_address, firstName, lastName, mnemonic) {
	return new Promise((resolve, reject) => {
	   const db = admin.firestore();
   
	   const pass = "LikeWallet";
	   const generate = mnemonic;
	   // console.log('an seed '+ generate);
	   const seed = encrypt(generate);
	   // console.log(generate);
   
	   var pw = seed.content;
	   var pwHex = secrets.str2hex(pw);
	   // console.log('seed content: ', seed.content);
	   // console.log(curPhone);
	   const selfCode = generateRefCode();
	   var countPassword = pass.length;
	   var curPassword = 32 - countPassword;
   
	   admin.auth().getUserByEmail(email_address).then(userRecord => {
   
		  randomText(curPassword).then(passConcat => {
   
			 //แบ่ง seed ออกเป็น 4 ส่วนใช้ 3 ใน 4 ในการไขออกมาเป็น seed
			 var shares = secrets.share(pwHex, 4, 3);
			 //ส่วนที่ 1 ไม่ทำการเข้ารหัสไว้ซ้ำ เป็นค่าที่นำไปใช้ได้เลย แต่ต้องมีอีก 2 ส่วนนะ
			 var userNoencrypt = shares[0];
			 //ส่วนที่ 2 นำไปเข้ารหัสกับรหัสผ่านของผู้ใช้ซึ่งจุดนนี้ มีแค่คนที่เป็นคนใส่รหัสเท่านั้นที่รู้ หากลืมก็ไม่สามารถไขกระเป๋าได้ตามกระบวนการปกติ
			 var userEncrypt = encryptWithPassword(shares[1], curPassword > 0 ? pass + passConcat : pass);
			 //ส่วนที่ 3 ส่วนนี้จะเก็บไว้ใน cyphermines เป็นดอกที่ 3
			 var cypherminesKeep = shares[2];
			 //ส่วนที่ 4 ส่วนนี้จะเก็บไว้ที่ masterkey แต่จะนำไปเข้ารหัสก่อน
			 var masterKey = shares[3];
			 //ทำการเพิ่ม + ออกจากเบอร์โทร
			 var tel = email_address;
			 var result = '';
			 var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789$&!?';
			 var charactersLength = characters.length;
			  //สุ่มตัวอักษณออกมาเป็น email
			 for (var i = 0; i < 9; i++) {
			   result += characters.charAt(Math.floor(Math.random() * charactersLength));
			 }
			 //สุ่มรหัสผ่านสำหรับ login
			 var password = generator.generate({
			   length: 30,
			   numbers: true
			 });
			 console.log('225');
				   //เลือก users ออกมา
				   const userRef = db.collection('users');
					//ค้นหาจากเบอร์โทร
				
						admin.auth().getUserByEmail(email_address)
						 .then(function(userRecord) {
							   //ค้นหา users/uid 
							   const users = db.collection('users').doc(userRecord.uid);
							   console.log('ok3');
   
							  const conPass = passConcat.length > 0 ? pass+passConcat : pass;
							   users.set({
								 password: password,
								 phone_number: tel,
								 _token: {
								   keyFirst: userNoencrypt,
								   keyEncrypt: userEncrypt,
								   tag: seed.tag,
								   concatPassword: passConcat.length > 0 ? passConcat : null
								 },
								 firstName: firstName === undefined ? 'noname' : firstName,
								 lastName: lastName === undefined ? 'noname' : lastName,
								 wallet_number: '1',
								 refCode: 'no',
								 selfCode: selfCode,
								 numberID: 'no'
							   }).then(d => {
								 console.log('ok 4');
								 const cyphermines = db.collection('cyphermines').doc(userRecord.uid);
   
								 cyphermines.set({
								   key: cypherminesKeep
								 }).then(x => {
									 //keep key 4 
									 //masterKey
									 const encryptDecryptFunction = async() => {
										 const options = {
											 message: openpgp.message.fromText(masterKey),       // input as Message object
											 publicKeys: (await openpgp.key.readArmored(pubkey)).keys, // for encryption
											 // privateKeys: [privKeyObj]                                 // for signing (optional)
										 }
									  
										 openpgp.encrypt(options).then(ciphertext => {
											 var encrypted = ciphertext.data // '-----BEGIN PGP MESSAGE ... END PGP MESSAGE-----'
									
											 const masterData = db.collection('masterKeyEncrypted').doc(userRecord.uid);
										   
											   masterData.set({
												 key: encrypted
											   }).then(uidMaster => {
												 //generate seed
												try{
												  let contentBackup = {
													first: userNoencrypt,
													second: cypherminesKeep,
													thrid: encrypted,
													tag: seed.tag,
													uid: userRecord.uid
												  }
												 uploadIPFS(contentBackup).then(pid=>{
												   console.log(pid[0].hash);
													const ipfsBackup = db.collection('ipfs');
													 ipfsBackup.add({
														contentBackup
													 });
												   recoverySeedNoSecret(userRecord.uid, conPass, userNoencrypt, userEncrypt, seed.tag, 'no_number').then(seed => {
													   //1218 line
													   RetrievePKED2519(seed).then(pkseed =>{
													   let wallet = new ethers.Wallet(pkseed);
													   console.log(wallet.address);
														let addressOwner = retrieveAddress(pkseed); 
														console.log(addressOwner);
							
														console.log('resolve');
														   let result = {
															 statusCode: 200,
															 result: 'Successfully created new user',
															 address: addressOwner
														   }
   
													
														 resolve(result);
													
													   })
																			  
												   }).catch(e =>{
													 reject(false);
												   });
   
												 }).catch(e=>{
												   console.log(e);
												   reject(false);
												 });
   
												  
												}catch(e){
												  reject(false);
												}
												 //end generate seed    
												 }).catch(err => {
												   console.log(err);
												   reject(false);
												 }); 
										 })
									   };
									 const masterKeyPGP = encryptDecryptFunction();
													   
								   
   
								 })
   
							   }).catch(e => {
								 let result = {
								   statusCode: 502,
								   result: "setup user failed !"
								 }
								 resolve(result);
							   });
   
   
   
							   
						 })
			   
				   //save password to db

			
		   })
		
	   });
	
   
	});
   
   }
//สร้าง user ได้ตรง ๆเลย
app.post('/partnerCreateUser', function(req, res){
	const apikey = req.body.apikey;
	const secret = req.body.secret;	

	let firstname = req.body.firstname;
	let lastname = req.body.lastname;

	let phone_number = req.body.phone_number;
	let application = req.body.application;
	if(firstname == ''){
		firstname = phone_number;
	}
	if(lastname == ''){
		lastname = 'no';
	}
	if(application == undefined) {
		application = "no application";
	}

  if(process.env.APIKEY_CONNECT == apikey && process.env.SECRETKEY_CONNECT == secret){
			const callVerify = async function() {
			    try {
			      const db = admin.firestore();
			      const docID = "OTP" + phone_number.split('+')[1];

		           admin.auth().createUser({
		            phoneNumber: phone_number,
		            displayName: firstname + " " + lastname,
		            disabled: false
		          }).then((userRecord) => {
		          	db.collection('createUserLog').doc(userRecord.uid).set({
		          		phoneNumber: phone_number,
		          		application: application,
		          		creationTime: moment().format("DD-MM-YYYY hh:mm:ss")
		          	});
		          	// function partnerCreateUser(phone_number, firstName, lastname)
		          	partnerCreateUser(phone_number, firstname, lastname, application).then(data =>{
		          		if(data.statusCode == 200){
		          			res.json(data);
		          		}else{
		          			res.json({statusCode:404, reason: "Something is wrong"});
		          		}
		          	}).catch(e =>{
		          		console.log(e);
		          		res.json({statusCode:404, reason: "Something is wrong"});
		          	})
		                         
		          }).catch(e =>{
		            console.log(e);
		              let data = {
		                statusCode:204,
		                result: 'already user'
		              }
		              res.json(data); 
		          });	      
			      // res.json({statusCode:200, result:'verify'});
			    } catch (err) {
			       res.json({statusCode:404, reason: "OTP is wrong"});
			    }		
			}
			callVerify();

}
});


function partnerCreateUser(phone, firstName, lastName, application) {
 return new Promise((resolve, reject) => {
	const db = admin.firestore();

	let curPhone = phone;
	let phone_number = curPhone.split('+')[1];

	const pass = "LikeWallet";
	const generate = bip39.generateMnemonic();
	const seed = encrypt(generate);
	// console.log(generate);

	var pw = seed.content;
	var pwHex = secrets.str2hex(pw);
	// console.log('seed content: ', seed.content);
	// console.log(curPhone);
	const selfCode = generateRefCode();
	var countPassword = pass.length;
	var curPassword = 32 - countPassword;

	admin.auth().getUserByPhoneNumber(curPhone).then(userRecord => {

	   randomText(curPassword).then(passConcat => {

	      //แบ่ง seed ออกเป็น 4 ส่วนใช้ 3 ใน 4 ในการไขออกมาเป็น seed
	      var shares = secrets.share(pwHex, 4, 3);
	      //ส่วนที่ 1 ไม่ทำการเข้ารหัสไว้ซ้ำ เป็นค่าที่นำไปใช้ได้เลย แต่ต้องมีอีก 2 ส่วนนะ
	      var userNoencrypt = shares[0];
	      //ส่วนที่ 2 นำไปเข้ารหัสกับรหัสผ่านของผู้ใช้ซึ่งจุดนนี้ มีแค่คนที่เป็นคนใส่รหัสเท่านั้นที่รู้ หากลืมก็ไม่สามารถไขกระเป๋าได้ตามกระบวนการปกติ
	      var userEncrypt = encryptWithPassword(shares[1], curPassword > 0 ? pass + passConcat : pass);
	      //ส่วนที่ 3 ส่วนนี้จะเก็บไว้ใน cyphermines เป็นดอกที่ 3
	      var cypherminesKeep = shares[2];
	      //ส่วนที่ 4 ส่วนนี้จะเก็บไว้ที่ masterkey แต่จะนำไปเข้ารหัสก่อน
	      var masterKey = shares[3];
	      //ทำการเพิ่ม + ออกจากเบอร์โทร
	      var tel = '+' + phone_number;
	      var result = '';
	      var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789$&!?';
	      var charactersLength = characters.length;
	       //สุ่มตัวอักษณออกมาเป็น email
	      for (var i = 0; i < 9; i++) {
	        result += characters.charAt(Math.floor(Math.random() * charactersLength));
	      }
	      //สุ่มรหัสผ่านสำหรับ login
	      var password = generator.generate({
	        length: 30,
	        numbers: true
	      });
	      console.log('225');
	            //เลือก users ออกมา
	            const userRef = db.collection('users');
	             //ค้นหาจากเบอร์โทร
	            let queryRef = userRef.where('phone_number', '==',  curPhone.length > 12 ? curPhone.substring(0, curPhone.length-1) : curPhone).get().then(snapshot => {
	                //ถ้ายังไม่ได้สร้าง user id ให้ทำการ สร้าง (เข้าใช้ครั้งแรกด้วยเบอร์โทร)
	                console.log('phone 231');
	                if (snapshot.empty) {
	                 admin.auth().getUserByPhoneNumber(tel)
	                  .then(function(userRecord) {
	                        //ค้นหา users/uid 
	                        const users = db.collection('users').doc(userRecord.uid);
	                        console.log('ok3');

	                       const conPass = passConcat.length > 0 ? pass+passConcat : pass;
	                        users.set({
	                          password: password,
	                          phone_number: tel,
	                          _token: {
	                            keyFirst: userNoencrypt,
	                            keyEncrypt: userEncrypt,
	                            tag: seed.tag,
	                            concatPassword: passConcat.length > 0 ? passConcat : null
	                          },
	                          firstName: firstName === undefined ? 'noname' : firstName,
	                          lastName: lastName === undefined ? 'noname' : lastName,
	                          wallet_number: '1',
	                          refCode: 'no',
	                          selfCode: selfCode,
	                          application: application,
	                          numberID: 'no'
	                        }).then(d => {
	                          console.log('ok 4');
	                          const cyphermines = db.collection('cyphermines').doc(userRecord.uid);

	                          cyphermines.set({
	                            key: cypherminesKeep
	                          }).then(x => {
	                              //keep key 4 
	                              //masterKey
	                              const encryptDecryptFunction = async() => {
	                                  const options = {
	                                      message: openpgp.message.fromText(masterKey),       // input as Message object
	                                      publicKeys: (await openpgp.key.readArmored(pubkey)).keys, // for encryption
	                                      // privateKeys: [privKeyObj]                                 // for signing (optional)
	                                  }
	                               
	                                  openpgp.encrypt(options).then(ciphertext => {
	                                      var encrypted = ciphertext.data // '-----BEGIN PGP MESSAGE ... END PGP MESSAGE-----'
	                             
	                                      const masterData = db.collection('masterKeyEncrypted').doc(userRecord.uid);
	                                    
	                                        masterData.set({
	                                          key: encrypted
	                                        }).then(uidMaster => {
	                                          //generate seed
	                                         try{
	                                           let contentBackup = {
	                                             first: userNoencrypt,
	                                             second: cypherminesKeep,
	                                             thrid: encrypted,
	                                             tag: seed.tag,
	                                             uid: userRecord.uid
	                                           }
	                                          uploadIPFS(contentBackup).then(pid=>{
	                                            console.log(pid[0].hash);
	                                             const ipfsBackup = db.collection('ipfs');
	                                              ipfsBackup.add({
	                                                 contentBackup
	                                              });
	                                            recoverySeedNoSecret(userRecord.uid, conPass, userNoencrypt, userEncrypt, seed.tag, 'no_number').then(seed => {
	                                                //1218 line
	                                                RetrievePKED2519(seed).then(pkseed =>{
	                                                let wallet = new ethers.Wallet(pkseed);
	                                                console.log(wallet.address);
	                                                 let addressOwner = retrieveAddress(pkseed); 
	                                                 console.log(addressOwner);
	                                                 db.collection('addressDNS').doc(userRecord.uid).set({
	                                                   address:addressOwner,
	                                                   phoneNumber:tel,
	                                                   creationTime: moment().format("DD-MM-YYYY hh:mm:ss"),
	                                                   name: firstName === undefined ? 'noname' : firstName +' '+lastName 
	                                                 }).then(ok =>{
	                                                 console.log('resolve');
		                                                let result = {
		                                                  statusCode: 200,
		                                                  result: 'Successfully created new user',
		                                                  address: addressOwner
	                                                	}

	                                                 console.log(result);
	                                                  resolve(result);
	                                                 })
	                                                
	                                                })
	                                                                       
	                                            }).catch(e =>{
	                                              reject(false);
	                                            });

	                                          }).catch(e=>{
	                                            console.log(e);
	                                            reject(false);
	                                          });

	                                           
	                                         }catch(e){
	                                           reject(false);
	                                         }
	                                          //end generate seed    
	                                          }).catch(err => {
	                                            console.log(err);
	                                            reject(false);
	                                          }); 
	                                  })
	                                };
	                              const masterKeyPGP = encryptDecryptFunction();
	                                                
	                            

	                          })

	                        }).catch(e => {
	                          let result = {
	                            statusCode: 502,
	                            result: "setup user failed !"
	                          }
	                          resolve(result);
	                        });




	                  })
	        
	            //save password to db


	          } else {
	            let result = {
	              statusCode: 404,
	              result: "code is wrong or expired !"
	            }
	            resolve(result);
	          }
	        
	      

	    })

	});


 });



});
}

app.post('/BUpartnerCreateUser', function(req, res){
	const apikey = req.body.apikey;
	const secret = req.body.secret;	

	const address = req.body.address;

	let BU_group = req.body.BU_group;
    var fullname = req.body.fullname;
    var wallet_type = req.body.wallet_type;
    var description = req.body.description;
    var type_bu = req.body.type_bu;

	let email = req.body.email;


  if(process.env.APIKEY_CONNECT == apikey && process.env.SECRETKEY_CONNECT == secret){
			const callVerify = async function() {
			    try {
			      const db = admin.firestore();
			     

		           admin.auth().createUser({
		            email: email,
		            password: email.split('@')[0]+'123456',
		            displayName: fullname,
		            disabled: false
		          }).then((userRecord) => {
		          	// function partnerCreateUser(phone_number, firstName, lastname)
		          	BUpartnerCreateUser(email, BU_group, type_bu, address, description, wallet_type, fullname).then(data =>{
		          		if(data.statusCode == 200){
		          			res.json(data);
		          		}else{
		          			res.json({statusCode:404, reason: "Something is wrong"});
		          		}
		          	}).catch(e =>{
		          		console.log(e);
		          		res.json({statusCode:404, reason: "Something is wrong"});
		          	})
		                         
		          }).catch(e =>{
		            console.log(e);
		              let data = {
		                statusCode:204,
		                result: 'already user'
		              }
		              res.json(data); 
		          });	      
			      // res.json({statusCode:200, result:'verify'});
			    } catch (err) {
			       res.json({statusCode:404, reason: "OTP is wrong"});
			    }		
			}
			callVerify();

}
});

app.post('/BUpartnerCreateUserBCT', function(req, res){
	const apikey = req.body.apikey;
	const secret = req.body.secret;	

	const address = req.body.address;

	let BU_group = req.body.BU_group;
    var fullname = req.body.fullname;
    var wallet_type = req.body.wallet_type;
    var description = req.body.description;
    var type_bu = req.body.type_bu;

	let email = req.body.email;


  if(process.env.APIKEY_CONNECT == apikey && process.env.SECRETKEY_CONNECT == secret){
			const callVerify = async function() {
			    try {
			      const db = admin.firestore();
			     

		           admin.auth().createUser({
		            email: email,
		            password: email.split('@')[0]+'123456',
		            displayName: fullname,
		            disabled: false
		          }).then((userRecord) => {
		          	// function partnerCreateUser(phone_number, firstName, lastname)
		          	BUpartnerCreateUserBCT(email, BU_group, type_bu, address, description, wallet_type, fullname).then(data =>{
		          		if(data.statusCode == 200){
		          			res.json(data);
		          		}else{
		          			res.json({statusCode:404, reason: "Something is wrong"});
		          		}
		          	}).catch(e =>{
		          		console.log(e);
		          		res.json({statusCode:404, reason: "Something is wrong"});
		          	})
		                         
		          }).catch(e =>{
		            console.log(e);
		              let data = {
		                statusCode:204,
		                result: 'already user'
		              }
		              res.json(data); 
		          });	      
			      // res.json({statusCode:200, result:'verify'});
			    } catch (err) {
			       res.json({statusCode:404, reason: "OTP is wrong"});
			    }		
			}
			callVerify();

}
});
function BUpartnerCreateUser(email, BU_group, type_bu, address, description, wallet_type, fullname) {
 return new Promise((resolve, reject) => {
	const db = admin.firestore();
	const firebase = secondary.database();
	
	let usersRef = firebase.ref("users");
	 usersRef.orderByChild('address').equalTo(address).once("value", function (snap) {
            if (snap.numChildren() !== 0) {
                snap.forEach(function (dataS) {
	             
	            const mnemonic =  Buffer.from(dataS.val()._token, 'base64').toString('ascii');
	            // console.log(mnemonic);
				    const pass = "LikeWallet";
					//waitingman
					const generate = mnemonic;
					const seed = encrypt(generate);
					// console.log(generate);

					var pw = seed.content;
					var pwHex = secrets.str2hex(pw);
					// console.log('seed content: ', seed.content);
					// console.log(curPhone);
					const selfCode = generateRefCode();
					var countPassword = pass.length;
					var curPassword = 32 - countPassword;

					admin.auth().getUserByEmail(email).then(userRecord => {

					   randomText(curPassword).then(passConcat => {

					      //แบ่ง seed ออกเป็น 4 ส่วนใช้ 3 ใน 4 ในการไขออกมาเป็น seed
					      var shares = secrets.share(pwHex, 4, 3);
					      //ส่วนที่ 1 ไม่ทำการเข้ารหัสไว้ซ้ำ เป็นค่าที่นำไปใช้ได้เลย แต่ต้องมีอีก 2 ส่วนนะ
					      var userNoencrypt = shares[0];
					      //ส่วนที่ 2 นำไปเข้ารหัสกับรหัสผ่านของผู้ใช้ซึ่งจุดนนี้ มีแค่คนที่เป็นคนใส่รหัสเท่านั้นที่รู้ หากลืมก็ไม่สามารถไขกระเป๋าได้ตามกระบวนการปกติ
					      var userEncrypt = encryptWithPassword(shares[1], curPassword > 0 ? pass + passConcat : pass);
					      //ส่วนที่ 3 ส่วนนี้จะเก็บไว้ใน cyphermines เป็นดอกที่ 3
					      var cypherminesKeep = shares[2];
					      //ส่วนที่ 4 ส่วนนี้จะเก็บไว้ที่ masterkey แต่จะนำไปเข้ารหัสก่อน
					      var masterKey = shares[3];
					      //ทำการเพิ่ม + ออกจากเบอร์โทร
					      var tel = 'no_phone';
					      var result = '';
					      var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789$&!?';
					      var charactersLength = characters.length;
					       //สุ่มตัวอักษณออกมาเป็น email
					      for (var i = 0; i < 9; i++) {
					        result += characters.charAt(Math.floor(Math.random() * charactersLength));
					      }
					      //สุ่มรหัสผ่านสำหรับ login
					      var password = generator.generate({
					        length: 30,
					        numbers: true
					      });
					      console.log('225');
					            //เลือก users ออกมา
					                 admin.auth().getUserByEmail(email)
					                  .then(function(userRecord) {
					                        //ค้นหา users/uid 
					                        const users = db.collection('users').doc(userRecord.uid);
					                        console.log('ok3');

					                       const conPass = passConcat.length > 0 ? pass+passConcat : pass;
					                        users.set({
					                          password: password,
					                          phone_number: tel,
					                          email: email,
					                          _token: {
					                            keyFirst: userNoencrypt,
					                            keyEncrypt: userEncrypt,
					                            tag: seed.tag,
					                            concatPassword: passConcat.length > 0 ? passConcat : null
					                          },
					                          firstName: fullname,
					                          lastName: BU_group,
					                          wallet_number: '1',
					                          refCode: 'no',
					                          selfCode: selfCode,
					                          numberID: 'no'
					                        }).then(d => {
					                          console.log('ok 4');
					                          const cyphermines = db.collection('cyphermines').doc(userRecord.uid);

					                          cyphermines.set({
					                            key: cypherminesKeep
					                          }).then(x => {
					                              //keep key 4 
					                              //masterKey
					                              const encryptDecryptFunction = async() => {
					                                  const options = {
					                                      message: openpgp.message.fromText(masterKey),       // input as Message object
					                                      publicKeys: (await openpgp.key.readArmored(pubkey)).keys, // for encryption
					                                      // privateKeys: [privKeyObj]                                 // for signing (optional)
					                                  }
					                               
					                                  openpgp.encrypt(options).then(ciphertext => {
					                                      var encrypted = ciphertext.data // '-----BEGIN PGP MESSAGE ... END PGP MESSAGE-----'
					                             
					                                      const masterData = db.collection('masterKeyEncrypted').doc(userRecord.uid);
					                                    
					                                        masterData.set({
					                                          key: encrypted
					                                        }).then(uidMaster => {
					                                          //generate seed
					                                         try{
					                                           let contentBackup = {
					                                             first: userNoencrypt,
					                                             second: cypherminesKeep,
					                                             thrid: encrypted,
					                                             tag: seed.tag,
					                                             uid: userRecord.uid
					                                           }
					                                          uploadIPFS(contentBackup).then(pid=>{
					                                            console.log(pid[0].hash);
					                                             const ipfsBackup = db.collection('ipfs');
					                                              ipfsBackup.add({
					                                                 contentBackup
					                                              });
					                                            recoverySeedNoSecret(userRecord.uid, conPass, userNoencrypt, userEncrypt, seed.tag, 'no_number').then(seed => {
					                                                //1218 line
					                                                RetrievePKED2519(seed).then(pkseed =>{
					                                                let wallet = new ethers.Wallet(pkseed);
					                                                console.log(wallet.address);
					                                                 let addressOwner = retrieveAddress(pkseed); 
					                                                 console.log(addressOwner);
					                                                 db.collection('addressDNS').doc(userRecord.uid).set({
					                                                   address:addressOwner,
					                                                   phoneNumber:tel,
					                                                   creationTime: moment().format("DD-MM-YYYY hh:mm:ss"),
					                                                   name: fullname
					                                                 }).then(ok =>{
					                                                 console.log('resolve');
						                                                let resultReturn = {
						                                                  statusCode: 200,
						                                                  result: 'Successfully created new user',
						                                                  address: addressOwner
					                                                	}

					                                                 console.log(result);

															request.post({ url: 'https://checkairdrop.prachakij.com/en', form: { key: mnemonic } }, function (err, httpResponse, body) {
															                            console.log('pass decrypt !!');
															                            let toJson = JSON.parse(body);
															                            console.log(body);
															                            // console.log(toJson.result.data.result);
															                            var seedEncrypt = toJson.result.data.result;
															                        var dates = new Date();
															                    	var con = mysql.createConnection(databaseRDSConfig);
															                        var con21CT = mysql.createConnection(databaseRDS21CTConfig);

															  						var sql = "INSERT INTO adminBCT (BU_group ,create_time, update_time, update_user, privk, address, BU, type_bu, flag, description, wallet_type) VALUES ?";
															                        var values = [
															                            [BU_group, dates.toISOString().substring(0, 10), dates.toISOString().substring(0, 10), 'BUpartnerCreateUser', seedEncrypt, address, fullname, type_bu, 1, description, wallet_type]
															                        ];
															                        con21CT.query(sql, [values], function (err, result) {
															                              if (err) throw err;
															                        con.query(sql, [values], function (err, result) {
															                            if (err) throw err;
  																						resolve(resultReturn);
															                        });	
															                        });
															                        });//request

					                                                
					                                                 })
					                                                
					                                                })
					                                                                       
					                                            }).catch(e =>{
					                                              reject(false);
					                                            });

					                                          }).catch(e=>{
					                                            console.log(e);
					                                            reject(false);
					                                          });

					                                           
					                                         }catch(e){
					                                           reject(false);
					                                         }
					                                          //end generate seed    
					                                          }).catch(err => {
					                                            console.log(err);
					                                            reject(false);
					                                          }); 
					                                  })
					                                };
					                              const masterKeyPGP = encryptDecryptFunction();
					                                                
					                            

					                          })

					                        }).catch(e => {
					                          let result = {
					                            statusCode: 502,
					                            result: "setup user failed !"
					                          }
					                          resolve(result);
					                        });




					                  })
					        
					            //save password to db


					        
					      

					  

					});


				 });//endlike
                })
            } 
	 });

	



});
}


function getAddressPK(address) {
	return new Promise((resolve, reject)=>{
		var con = mysql.createConnection(databaseRDSConfig);
		con.query("select * from adminBCT where address = ?", [address], function(err, result){
		request.post({ url: 'https://checkairdrop.prachakij.com/de', form: { key: result[0].privk } }, function (err, httpResponse, body) {
			console.log('pass decrypt !!');
			let toJson = JSON.parse(body);
			resolve(toJson.result.data.result);
		});//request

					                                                

		})
	})
}
function BUpartnerCreateUserBCT(email, BU_group, type_bu, address, description, wallet_type, fullname) {
 return new Promise(async (resolve, reject) => {
	const db = admin.firestore();
	const firebase = secondary.database();
	

	
	             
	            const mnemonic =  await getAddressPK(address);
	            // console.log(mnemonic);
				    const pass = "LikeWallet";
					//waitingman
					const generate = mnemonic;
					const seed = encrypt(generate);
					// console.log(generate);

					var pw = seed.content;
					var pwHex = secrets.str2hex(pw);
					// console.log('seed content: ', seed.content);
					// console.log(curPhone);
					const selfCode = generateRefCode();
					var countPassword = pass.length;
					var curPassword = 32 - countPassword;

					admin.auth().getUserByEmail(email).then(userRecord => {

					   randomText(curPassword).then(passConcat => {

					      //แบ่ง seed ออกเป็น 4 ส่วนใช้ 3 ใน 4 ในการไขออกมาเป็น seed
					      var shares = secrets.share(pwHex, 4, 3);
					      //ส่วนที่ 1 ไม่ทำการเข้ารหัสไว้ซ้ำ เป็นค่าที่นำไปใช้ได้เลย แต่ต้องมีอีก 2 ส่วนนะ
					      var userNoencrypt = shares[0];
					      //ส่วนที่ 2 นำไปเข้ารหัสกับรหัสผ่านของผู้ใช้ซึ่งจุดนนี้ มีแค่คนที่เป็นคนใส่รหัสเท่านั้นที่รู้ หากลืมก็ไม่สามารถไขกระเป๋าได้ตามกระบวนการปกติ
					      var userEncrypt = encryptWithPassword(shares[1], curPassword > 0 ? pass + passConcat : pass);
					      //ส่วนที่ 3 ส่วนนี้จะเก็บไว้ใน cyphermines เป็นดอกที่ 3
					      var cypherminesKeep = shares[2];
					      //ส่วนที่ 4 ส่วนนี้จะเก็บไว้ที่ masterkey แต่จะนำไปเข้ารหัสก่อน
					      var masterKey = shares[3];
					      //ทำการเพิ่ม + ออกจากเบอร์โทร
					      var tel = 'no_phone';
					      var result = '';
					      var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789$&!?';
					      var charactersLength = characters.length;
					       //สุ่มตัวอักษณออกมาเป็น email
					      for (var i = 0; i < 9; i++) {
					        result += characters.charAt(Math.floor(Math.random() * charactersLength));
					      }
					      //สุ่มรหัสผ่านสำหรับ login
					      var password = generator.generate({
					        length: 30,
					        numbers: true
					      });
					      console.log('225');
					            //เลือก users ออกมา
					                 admin.auth().getUserByEmail(email)
					                  .then(function(userRecord) {
					                        //ค้นหา users/uid 
					                        const users = db.collection('users').doc(userRecord.uid);
					                        console.log('ok3');

					                       const conPass = passConcat.length > 0 ? pass+passConcat : pass;
					                        users.set({
					                          password: password,
					                          phone_number: tel,
					                          email: email,
					                          _token: {
					                            keyFirst: userNoencrypt,
					                            keyEncrypt: userEncrypt,
					                            tag: seed.tag,
					                            concatPassword: passConcat.length > 0 ? passConcat : null
					                          },
					                          firstName: fullname,
					                          lastName: BU_group,
					                          wallet_number: '1',
					                          refCode: 'no',
					                          selfCode: selfCode,
					                          numberID: 'no'
					                        }).then(d => {
					                          console.log('ok 4');
					                          const cyphermines = db.collection('cyphermines').doc(userRecord.uid);

					                          cyphermines.set({
					                            key: cypherminesKeep
					                          }).then(x => {
					                              //keep key 4 
					                              //masterKey
					                              const encryptDecryptFunction = async() => {
					                                  const options = {
					                                      message: openpgp.message.fromText(masterKey),       // input as Message object
					                                      publicKeys: (await openpgp.key.readArmored(pubkey)).keys, // for encryption
					                                      // privateKeys: [privKeyObj]                                 // for signing (optional)
					                                  }
					                               
					                                  openpgp.encrypt(options).then(ciphertext => {
					                                      var encrypted = ciphertext.data // '-----BEGIN PGP MESSAGE ... END PGP MESSAGE-----'
					                             
					                                      const masterData = db.collection('masterKeyEncrypted').doc(userRecord.uid);
					                                    
					                                        masterData.set({
					                                          key: encrypted
					                                        }).then(uidMaster => {
					                                          //generate seed
					                                         try{
					                                           let contentBackup = {
					                                             first: userNoencrypt,
					                                             second: cypherminesKeep,
					                                             thrid: encrypted,
					                                             tag: seed.tag,
					                                             uid: userRecord.uid
					                                           }
					                                          uploadIPFS(contentBackup).then(pid=>{
					                                            console.log(pid[0].hash);
					                                             const ipfsBackup = db.collection('ipfs');
					                                              ipfsBackup.add({
					                                                 contentBackup
					                                              });
					                                            recoverySeedNoSecret(userRecord.uid, conPass, userNoencrypt, userEncrypt, seed.tag, 'no_number').then(seed => {
					                                                //1218 line
					                                                RetrievePKED2519(seed).then(pkseed =>{
					                                                let wallet = new ethers.Wallet(pkseed);
					                                                console.log(wallet.address);
					                                                 let addressOwner = retrieveAddress(pkseed); 
					                                                 console.log(addressOwner);
					                                                 db.collection('addressDNS').doc(userRecord.uid).set({
					                                                   address:addressOwner,
					                                                   phoneNumber:tel,
					                                                   creationTime: moment().format("DD-MM-YYYY hh:mm:ss"),
					                                                   name: fullname
					                                                 }).then(ok =>{
					                                                 console.log('resolve');
						                                                let resultReturn = {
						                                                  statusCode: 200,
						                                                  result: 'Successfully created new user',
						                                                  address: addressOwner
					                                                	}

					                                                 console.log(result);

															request.post({ url: 'https://checkairdrop.prachakij.com/en', form: { key: mnemonic } }, function (err, httpResponse, body) {
															                            console.log('pass decrypt !!');
															                            let toJson = JSON.parse(body);
															                            console.log(body);
															                            // console.log(toJson.result.data.result);
															                            var seedEncrypt = toJson.result.data.result;
															                        var dates = new Date();
															                    	var con = mysql.createConnection(databaseRDSConfig);
															                        var con21CT = mysql.createConnection(databaseRDS21CTConfig);

															  						var sql = "INSERT INTO adminBCT (BU_group ,create_time, update_time, update_user, privk, address, BU, type_bu, flag, description, wallet_type) VALUES ?";
															                        var values = [
															                            [BU_group, dates.toISOString().substring(0, 10), dates.toISOString().substring(0, 10), 'BUpartnerCreateUser', seedEncrypt, address, fullname, type_bu, 1, description, wallet_type]
															                        ];
															                        con21CT.query(sql, [values], function (err, result) {
															                              if (err) throw err;
															                        con.query(sql, [values], function (err, result) {
															                            if (err) throw err;
  																						resolve(resultReturn);
															                        });	
															                        });
															                        });//request

					                                                
					                                                 })
					                                                
					                                                })
					                                                                       
					                                            }).catch(e =>{
					                                              reject(false);
					                                            });

					                                          }).catch(e=>{
					                                            console.log(e);
					                                            reject(false);
					                                          });

					                                           
					                                         }catch(e){
					                                           reject(false);
					                                         }
					                                          //end generate seed    
					                                          }).catch(err => {
					                                            console.log(err);
					                                            reject(false);
					                                          }); 
					                                  })
					                                };
					                              const masterKeyPGP = encryptDecryptFunction();
					                                                
					                            

					                          })

					                        }).catch(e => {
					                          let result = {
					                            statusCode: 502,
					                            result: "setup user failed !"
					                          }
					                          resolve(result);
					                        });




					                  })
					        
					            //save password to db


					        
					      

					  

					});


				 });//endlike
              


});
}
///
//recovery
//#############
//Revocery Step 1
//this step admin fill uid and call to user for told digit and uid 
//#############
app.post('/recoveryAccount', function(req, res){
  const db = admin.firestore();
  const uid = req.body.uid;

	const APIKEY = req.body.apiKey;
	const SECRETKEY = req.body.secretKey

	if(process.env.APIKEY === APIKEY && process.env.SECRETKEY === SECRETKEY){
	  db.collection('users').doc(uid).get().then(snapshot =>{

	    if(!snapshot.exists){
	       console.log('no data');
	    }else{
	      //create recovery database
	      createRecoveryState(snapshot).then(callback =>{
	      	console.log('createRecoveryState OK');
	      	console.log(callback);
	      	res.json({statusCode:200, result: callback});
	      });
	    }
	  });
	}
});
//#############
//Revocery Step 2
//this step user fill new secret via front end to us
//ป้อนข้อมูลจากหน้าเวป โดย uid กับ code verify ทางเราต้องส่งให้ผู้ใช้
//#############
app.post('/inputNewSecret', function(req, res){
  const db = admin.firestore();
  const uid = req.body.uid;
  const secret = req.body.secret;
  const codeVerify = req.body.codeVerify;


  db.collection('recoveryKey').doc(uid).get().then(snapshot =>{

    if(!snapshot.exists){
       console.log('no data');
    }else{
      //update new secret
      if(snapshot.data().codeVerify.toString() === codeVerify.toString()){
	      setUpNewSecret(snapshot, secret).then(callback =>{
			res.json({statusCode: 200, result:'sucess'});
	      }).catch(e=>{
	      	res.json({statusCode: 202, result: 'set key error'});
	      });      	
      }else{
      	res.json({statusCode: 203, result: 'code is wrong !'});
      }

    }
  });

});

//



function recoveryMasterKey(email, phone_number){
	return new Promise((resolve, reject) => {
		const db = admin.firestore();

		if(email != 'no'){
			admin.auth().getUserByEmail(email).then(decoded =>{
			    db.collection('man').doc('manyu').get().then(key =>{
			     let data = {
			       content: key.data().content,
			       tag: key.data().tag
			     }

				    request.post({ url: 'https://checkairdrop.prachakij.com/getyubi', form: { 
				        apikey: process.env.API_CHECK,
				        secret: process.env.SECRET_CHECK
				    } }, function (err, httpResponse, body) {
				      let parse = JSON.parse(body);
				      console.log(parse.result);
				      let password = Buffer.from(parse.result, 'base64').toString('ascii');
				      console.log(password);
				       let x = decryptWithPassword(data, Buffer.from(parse.result, 'base64').toString('ascii'));
				          const passphrase = x;
				          let db = admin.firestore();
				          db.collection('masterKeyEncrypted').doc(decoded.uid).get().then(data =>{

				          let startDecrypt = async function() {
				            const { keys: [privateKey] } = await openpgp.key.readArmored(passphrase);
				            await privateKey.decrypt(passphrase)
				         

				           const { data: decrypted } = await openpgp.decrypt({
				                message: await openpgp.message.readArmored(data.data().key),              // parse armored message
				                publicKeys: (await openpgp.key.readArmored(pubkey)).keys, // for verification (optional)
				                privateKeys: [privateKey]                                           // for decryption
				            });
				            resolve(decrypted);
				          }
				          startDecrypt();

				        });
				    });     

			 
			   });
			});
		}else{
			console.log('163');
			admin.auth().getUserByPhoneNumber(phone_number).then(decoded =>{
			    db.collection('man').doc('manyu').get().then(key =>{
			     let data = {
			       content: key.data().content,
			       tag: key.data().tag
			     }
					    request.post({ url: 'https://checkairdrop.prachakij.com/getyubi', form: { 
				        apikey: process.env.API_CHECK,
				        secret: process.env.SECRET_CHECK
				    } }, function (err, httpResponse, body) {
				
				      let parse = JSON.parse(body);
				      console.log(parse);
				
				      let pass = Buffer.from(parse.result, 'base64').toString('ascii');
				      console.log(pass);
				       let x = decryptWithPassword(data, pass);
				          const passphrase = x;
				          let db = admin.firestore();
				          db.collection('masterKeyEncrypted').doc(decoded.uid).get().then(data =>{

				          let startDecrypt = async function() {
				            const { keys: [privateKey] } = await openpgp.key.readArmored(passphrase);
				            await privateKey.decrypt(process.env.passM)
				         

				           const { data: decrypted } = await openpgp.decrypt({
				                message: await openpgp.message.readArmored(data.data().key),              // parse armored message
				                publicKeys: (await openpgp.key.readArmored(pubkey)).keys, // for verification (optional)
				                privateKeys: [privateKey]                                           // for decryption
				            });
				            resolve(decrypted);
				          }
				          startDecrypt();

				        });
				    });     

			 
			   });
			});			
		}
	});

}
////#############
//Revocery Step 3
//this step admin will be trigger uid and automated recovery seed and encrypt with new secret from user
//after end this step user should login with new secret key in likewallet app
//#############
app.post('/adminRecoverySeed', function(req, res){
	//masterKey looks like 804xxxxxx212
	const masterKey = req.body.masterKey;
	const uid = req.body.uid;
	const APIKEY = req.body.apiKey;
	const SECRETKEY = req.body.secretKey;
	const db = admin.firestore();


	if(process.env.APIKEY === APIKEY && process.env.SECRETKEY === SECRETKEY){
		db.collection('users').doc(uid)
		.get()
		.then(users =>{
			db.collection('masterKeyEncrypted').doc(uid)
			.get()
			.then(masterKeyEncrypted =>{
			db.collection('logs_users').doc(uid).collection('backupKeyMaster')
			.add(masterKeyEncrypted.data())
			.then(okBackupMaster =>{
				db.collection('logs_users').doc(uid).collection('backup').add(
				users.data()
				).then(okBackup =>{
						db.collection('cyphermines').doc(uid)
						.get()
						.then(cyphermines =>{
							db.collection('logs_users').doc(uid).collection('backup-keyCyphermines').add(
								cyphermines.data()
							).then(okBackupCypher=>{
							db.collection('recoveryKey').doc(uid)
							.get()
							.then(recoveryKey =>{
								decryptKMS(recoveryKey.data().secret)
								.then(secret =>{
									const keyFirst = users.data()._token.keyFirst;
									const keySecond = cyphermines.data().key;
									const keyThrid = masterKey;
							
									console.log(keyFirst);
									console.log(keySecond);
									console.log(keyThrid);
									//ขั้นตอนนี้รวม secret key ออกมาเป็น seed 12 word
									
									   deSecretSharing(keyFirst, keySecond, keyThrid, users.data()._token.tag)
									   .then(seedDecrypt =>{
									  //นับตัวอักษณของ รหัสลับว่า มีกี่ตัว

								   		var countPassword = secret.toString('utf-8').length;
								   		//ใช้ 32 ตัวเลยหาว่าขาดไปกี่ตัว
									    var curPassword = 32 - countPassword;
									    console.log(curPassword);
									    //สุ่มรหัสผ่านมาเพิ่มเติมตามจำนวนตัวอักษรที่ขาดไป
									   	  randomText(curPassword).then(passConcat => {
											  const seed = encrypt(seedDecrypt);
											  //seed tag
											  var tag = seed.tag;
											  //content ของ seed
											  var pw = seed.content;
											  // เปลี่ยน content เป็น hex
											  var pwHex = secrets.str2hex(pw);	
											  //แบ่ง key ออกเป็น 4 ส่วนและใช้ 3 ส่วนในการเข้าถึง
										      var shares = secrets.share(pwHex, 4, 3);
										      var userNoencrypt = shares[0];
										      var userEncrypt = encryptWithPassword(shares[1], curPassword > 0 ? secret.toString('utf-8') + passConcat : secret.toString('utf-8'));
										      var cypherminesKeep = shares[2];
										      var master = shares[3];
											

												//encryp and update user data
											 const encryptDecryptFunction = async() => {
						                              const options = {
						                                  message: openpgp.message.fromText(master),       // input as Message object
						                                  publicKeys: (await openpgp.key.readArmored(pubkey)).keys, // for encryption
						                                  // privateKeys: [privKeyObj]                                 // for signing (optional)
						                              }
						                           
						                              openpgp.encrypt(options).then(ciphertext => {
						                                  var encrypted = ciphertext.data // '-----BEGIN PGP MESSAGE ... END PGP MESSAGE-----'
						                                  console.log(encrypted);
						                                  db.collection('masterKeyEncrypted').doc(uid)
						                                  .update({
						                                  	 key: encrypted
						                                  }).then(okCreateMaster =>{
						                                  	db.collection('cyphermines').doc(uid)
						                                  	.update({
						                                  		key: cypherminesKeep
						                                  	}).then(okCreateCypherKey =>{
						                                  		 db.collection('users').doc(uid)
						                                  		 .update({
												                      _token: {
												                        keyFirst: userNoencrypt,
												                        keyEncrypt: userEncrypt,
												                        tag: tag,
												                        concatPassword: passConcat.length > 0 ? passConcat : null
												                      }						                                  		 	
						                                  		 }).then(ok =>{
								                                  	try{
								                                     let contentBackup = {
								                                         first: userNoencrypt,
								                                         second: cypherminesKeep,
								                                         thrid: encrypted,
								                                         tag: tag,
								                                         uid: uid
								                                       }                                        
								                                      uploadIPFS(contentBackup).then(pid=>{
								                                        console.log(pid[0].hash);
								                                         const ipfsBackup = db.collection('ipfs');
								                                          ipfsBackup.add({
								                                             contentBackup
								                                          });
								                                          console.log(uid);
								                                  		    db.collection('recoveryKey').doc(uid).delete().then(remove =>{
								                                  		    	res.json({statusCode:200, result:'generate new encrypt success'});
								                                  		    });								                                
								                                      });
								                                      }catch(e){
								                                        console.log(e);
								                                        res.json({statusCode:203, result:'IPFS problem'});
								                                      }						                                  		 

						                                  		 	
						                                  		 });
						                                  	})
						                                  });
						      
						                              })
						                            };
						               		  const masterKeyPGP = encryptDecryptFunction();											  

										  });
									   })
								
								});
							});
							});					

						});				
					});
			});
		

			});
		
		});
	}else{
		res.json({statusCode:404, result:'Authorized failed'});
	}
});
function setUpNewSecret(snapshot, secret){
	return new Promise((resolve, reject)=>{
		encryptKMS(secret).then(encrypSecret =>{
			const db = admin.firestore();
			  db.collection('recoveryKey').doc(snapshot.id).update({
			  	secret: encrypSecret,
			  	status: 'success',
			  	codeVerify: getDigit()
			  }).then(success =>{
			 	resolve(true);
			  }).catch(err =>{
			  	console.log(err);
			  	reject();
			  });				
		});
	})
}

function setUpNewSecretSet(id, secret){
	return new Promise((resolve, reject)=>{
		console.log(id);
		console.log(secret);
		encryptKMS(secret).then(encrypSecret =>{
			const db = admin.firestore();

			  db.collection('recoveryKey').doc(id).set({
			  	secret: encrypSecret,
			  	status: 'success',
			  	codeVerify: getDigit()
			  }).then(success =>{
			 	resolve(true);
			  }).catch(err =>{
			  	console.log(err);
			  	reject();
			  });				
		});
	})
}

function createRecoveryState(snapshot){
  return new Promise((resolve, reject) =>{
       const db = admin.firestore();
       const uuid = snapshot.id;
       const number = getDigit();
       db.collection('recoveryKey').doc(uuid)
       .set({
          uid: uuid,
          status: 'pending',
          codeVerify: number
       }).then(data =>{
       		let callback = {
       			digit: number,
       			uid: uuid
       		}
       		resolve(callback);
       });
  });
}
function checkLock(addr) {
	const rewards = [
	'0xdc3ec0ff5c014001956979ff59290e57fd30103e',
	'0xc87f9ef4243719baaa9764ad4cea7a08dd278761',
	];

	for(let i=0;i<rewards.length;i++){
		// console.log(addr);
		if(addr.toLowerCase() === rewards[i].toLowerCase()){

			return true;
		}else{
			if(rewards.length == i+1){
				return false;
			}
		}


	}
}
function checkAds(addr) {
	const rewards = [
	'0x9e7fc9025fc22D5bab90f2b49B67904c880184C4'];

	for(let i=0;i<rewards.length;i++){
		// console.log(addr);
		if(addr.toLowerCase() === rewards[i].toLowerCase()){

			return true;
		}else{
			if(rewards.length == i+1){
				return false;
			}
		}


	}
}
function checkKickback(addr) {
	const rewards = [
	'0xd57199f9977c23ff7fc41941d46d4f831e953223',
	'0x4CDF1708467E2d11B232713EDaa2bF660aE980Ef'
	];

	for(let i=0;i<rewards.length;i++){
		// console.log(addr);
		if(addr.toLowerCase() === rewards[i].toLowerCase()){

			return true;
		}else{
			if(rewards.length == i+1){
				return false;
			}
		}


	}
}
function checkUnlock(addr) {
	const rewards = [
	'0xdc3ec0ff5c014001956979ff59290e57fd30103e',
	'0xc87f9ef4243719baaa9764ad4cea7a08dd278761',

	];

	for(let i=0;i<rewards.length;i++){
		// console.log(addr);
		if(addr.toLowerCase() === rewards[i].toLowerCase()){

			return true;
		}else{
			if(rewards.length == i+1){
				return false;
			}
		}


	}
}
function checkRewards(addr) {
	const rewards = [
	'0x26491cf764ee5ED9EA4cf85293C2D0b0c0de8b22',

	];

	for(let i=0;i<rewards.length;i++){
		// console.log(addr);
		if(addr.toLowerCase() === rewards[i].toLowerCase()){

			return true;
		}else{
			if(rewards.length == i+1){
				return false;
			}
		}


	}
}
function checkCompound(addr) {
	const rewards = [
	'0x0a4210edaf885e3ec6880902ca88c33e4b14997b',
	];

	for(let i=0;i<rewards.length;i++){
		// console.log(addr);
		if(addr.toLowerCase() === rewards[i].toLowerCase()){

			return true;
		}else{
			if(rewards.length == i+1){
				return false;
			}
		}


	}
}
function checkLotto(addr) {
	const rewards = [
	'0xA0D815F63fEBb882C8600c3A27642fC896060Cc9',
	'0xD6C05E421B00C2727A4793e342FEd293dE63BFC4'
	];

	for(let i=0;i<rewards.length;i++){
		// console.log(addr);
		if(addr.toLowerCase() === rewards[i].toLowerCase()){

			return true;
		}else{
			if(rewards.length == i+1){
				return false;
			}
		}


	}
}
function checkShop(addr) {
	const shop = [
	'0x63b381f87ac20e8be3665a4d14fffbc63b80267b',
'0xfa04f4d31e26142ceca666cf94ce6e2e7c569ed3',
'0x7944820b4e866a38e8641e78eaee922774b287a9',
'******************************************',
'******************************************',
'******************************************',
'******************************************',
'******************************************',
'******************************************',
'******************************************'];

	for(let i=0;i<shop.length;i++){
		// console.log(addr);
		if(addr === shop[i]){

			return true;
		}else{
			if(shop.length == i+1){
				return false;
			}
		}


	}
}
///
app.get('/getHistoryTest', function(req, res){
	const db = admin.firestore();
	const uid = 'sapURviHRQUTXhf0zdot0qTrbt13';

    const address = '******************************************';
      const sqlTransaction = 'SELECT * FROM logTransaction WHERE toAddress = ? OR fromAddress = ? ORDER BY running DESC LIMIT 20';
      const fieldSQLTransaction = [address, address];

      const sqlWithdraw = 'SELECT * FROM withdraw WHERE uid = ? ORDER BY running DESC LIMIT 100';
      // const fieldSQLWithdraw = [decodedToken.uid];
      const fieldSQLWithdraw = [uid];
      selectMysql(sqlWithdraw, fieldSQLWithdraw).then(data => {
        	// console.log(data);
        	let dataBack = [];
       
	        for(let i=0;i<data.length;i++){
	          let status;
	          if(data[i].status === '1'){
	            status = 1
	          }else if(data[i].status === '2'){
	            status = 2;
	          }else if(data[i].status === '3'){
	          	status = 3;
	          }else{
	            status = 0;
	          }
	          let result = {
	          	'sortDate':data[i].updated_time,
	            'updateTime':parseInt(data[i].updated_time),
	            'uid':data[i].uid,
	            'paymentMethod':data[i].paymentMethod,
	            'bankName':data[i].bankName,
	            'tx':data[i].tx,
	            'type':'cash',
	            'accountNumber':data[i].accountNumber,
	            'fee':data[i].fee,
	            'baht':data[i].baht,
	            'phoneNumber':data[i].phoneNumber.toString(),
	            'status':status.toString()
	          };
	          dataBack.push(result);

	        }
		  	selectMysql(sqlTransaction, fieldSQLTransaction).then(data => {
		  		// console.log(data);
		  		 for(let i=0;i<data.length;i++){
			          let result = {
			          	'sortDate':data[i].updateTime,
			            'updateTime':parseInt(data[i].updateTime),
			            'uid':uid,
			            'paymentMethod':'sendLike',
			            'bankName':'sendLike',
			            'tx':data[i].tx,
			            'type':'transaction',
			            'accountNumber':data[i].fromAddress,
			            'fee':data[i].toAddress,
			            'baht':(data[i].amount/10e17).toString(),
			            'phoneNumber':data[i].blockNumber.toString(),
			            'status':data[i].running.toString()
			          };
			          dataBack.push(result);
		  		 }
		
		        let result = sortByDate(dataBack).slice(-1,1);
		        // console.log(result[0]);
		        res.json({statusCode:200, result: result[0]});
		   	});        
        });      
    
});
app.post('/getHistory', function(req, res){
    const _token = req.body._token;
    const address = req.body.address;

      admin.auth().verifyIdToken(_token)
    .then(function (decodedToken){
      const sqlTransaction = 'SELECT * FROM logTransaction WHERE toAddress = ? OR fromAddress = ? ORDER BY running DESC LIMIT 20';
      const fieldSQLTransaction = [address, address];

      const sqlWithdraw = 'SELECT * FROM withdraw WHERE uid = ? ORDER BY running DESC LIMIT 100';
      // const fieldSQLWithdraw = [decodedToken.uid];
      const fieldSQLWithdraw = [decodedToken.uid];
      selectMysql(sqlWithdraw, fieldSQLWithdraw).then(data => {
        	// console.log(data);
        	let dataBack = [];
       
	        for(let i=0;i<data.length;i++){
	          let status;
	          if(data[i].status === '1'){
	            status = 1
	          }else if(data[i].status === '2'){
	            status = 2;
	          }else if(data[i].status === '3'){
	          	status = 3;
	          }else{
	            status = 0;
	          }
	          let result = {
	          	'sortDate':data[i].updated_time,
	            'updateTime':parseInt(data[i].updated_time),
	            'uid':data[i].uid,
	            'paymentMethod':data[i].paymentMethod,
	            'bankName':data[i].bankName,
	            'tx':data[i].tx,
	            'type':'cash',
	            'accountNumber':data[i].accountNumber,
	            'fee':data[i].fee,
	            'baht':data[i].baht,
	            'phoneNumber':data[i].phoneNumber.toString(),
	            'status':status.toString()
	          };
	          dataBack.push(result);

	        }
		  	selectMysql(sqlTransaction, fieldSQLTransaction).then(data => {
		  		// console.log(data);
		  		 for(let i=0;i<data.length;i++){
			          let result = {
			          	'sortDate':data[i].updateTime,
			            'updateTime':parseInt(data[i].updateTime),
			            'uid':decodedToken.uid,
			            'paymentMethod':'sendLike',
			            'bankName':'sendLike',
			            'tx':data[i].tx,
			            'type':'transaction',
			            'accountNumber':data[i].fromAddress,
			            'fee':data[i].toAddress,
			            'baht':(data[i].amount/10e17).toString(),
			            'phoneNumber':data[i].blockNumber.toString(),
			            'status':data[i].running.toString()
			          };
			          dataBack.push(result);
		  		 }
		
		        let result = sortByDate(dataBack).slice(-1,1);
		        // console.log(result[0]);
		        res.json({statusCode:200, result: result[0]});
		   	});        
        });      
       }).catch(e =>{
        		res.json({statusCode:404, result:false});
       });//verifyIdTok

});


app.post('/getHistoryNew', function(req, res){
const _token = req.body._token;
const db = admin.firestore();
    const address = req.body.address;
      admin.auth().verifyIdToken(_token)
    .then(function (decodedToken){
      // const sqlTransaction = 'SELECT * FROM logTransaction WHERE logTransaction.toAddress = ? OR logTransaction.fromAddress = ? ORDER BY logTransaction.running DESC LIMIT 30';
      // const sqlTransaction = 'SELECT logTransaction.* , logTransactionMessage.message FROM logTransaction LEFT JOIN logTransactionMessage ON logTransaction.tx = logTransactionMessage.tx WHERE logTransaction.toAddress = ? OR logTransaction.fromAddress = ? ORDER BY logTransaction.running DESC LIMIT 30';
      const sqlTransaction = 'SELECT running, fromAddress, toAddress, tx, amount, blockNumber, updateTime, createTime, chain FROM logTransaction  WHERE logTransaction.toAddress = ? OR logTransaction.fromAddress = ? ORDER BY logTransaction.running DESC LIMIT 30';
      const fieldSQLTransaction = [address, address];

      const sqlWithdraw = 'SELECT * FROM withdraw WHERE uid = ? ORDER BY running DESC LIMIT 15';
      // const fieldSQLWithdraw = [decodedToken.uid];
      const fieldSQLWithdraw = [decodedToken.uid];

      selectMysql(sqlWithdraw, fieldSQLWithdraw).then(data => {
        	// console.log(data);
        	let dataBack = [];
       
	        for(let i=0;i<data.length;i++){
	          let status;
	          if(data[i].status === '1'){
	            status = 1
	          }else if(data[i].status === '2'){
	            status = 2;
	          }else if(data[i].status === '3'){
	          	status = 3;
	          }else{
	            status = 0;
	          }
	          let result = {
	          	'sortDate':data[i].updated_time,
	            'updateTime':parseInt(data[i].updated_time),
	            'uid':data[i].uid,
	            'paymentMethod':data[i].paymentMethod,
	            'bankName':data[i].bankName,
	            'tx':data[i].tx,
	            'to':'bank',
	            'title':'recv',
	            'type':'cash',
	            'accountNumber':data[i].accountNumber,
	            'fee':data[i].fee,
	            'baht':data[i].baht,
	            'slip':data[i].slip,
	            'phoneNumber':data[i].phoneNumber.toString(),
	            'status':status.toString(),
	            'message':'no'
	          };
	          dataBack.push(result);

	        }

	        // console.log(dataBack);
	        console.log('....loading');
		  	selectMysql(sqlTransaction, fieldSQLTransaction).then(async (data) => {

		  		//console.log(data);
		  		 for(let i=0;i<data.length;i++){
		  		 	  let checkPayment = checkShop(data[i].toAddress);
		  		 	  // let checkPaymentFrom = checkShop(data[i].fromAddress);

		  		 	  let checkRewa = checkRewards(data[i].fromAddress);
		  		 	  // let checkRewaTo = checkRewards(data[i].toAddress);

		  		 	  let checkLo = checkLock(data[i].toAddress);
		  		 	  // let checkFrom = checkLock(data[i].fromAddress);

		  		 	  let checkAd = checkAds(data[i].fromAddress);
		  		 	  // let checkAdTo = checkAds(data[i].toAddress);

		  		 	  let checkUnlocked = checkUnlock(data[i].fromAddress);
		  		 	  // let checkUnlockedTo = checkUnlock(data[i].toAddress);

		  		 	  let checkLotFrom = checkLotto(data[i].fromAddress);
		  		 	  let checkLotTo = checkLotto(data[i].toAddress);
		  		 	  let checkCompFrom = checkCompound(data[i].fromAddress);
		  		 	  let checkCompTo = checkCompound(data[i].toAddress);
		  		 	  // let checkKickback = checkKickback(data[i].fromAddress);
		  		 	  // let checkKickback = checkAds(data[i].fromAddress);
		  		 	  if(checkLo == false){
		  		 	  	checkLo = checkLock(data[i].fromAddress);
		  		 	  }

					  let name = 'no';

		  		 	  if(checkPayment == false && checkRewa == false &&
		  		 	  	checkLo == false && checkAd == false &&
		  		 	  	checkUnlocked == false && checkLotFrom == false &&
		  		 	  	checkLotTo == false && checkCompFrom == false && checkCompTo == false
		  		 	  	){

							// let name = 'no';
		  		 	  		if(data[i].toAddress == address) {
		  		 	  			let snapshot = await searchNameNew(data[i].fromAddress);
									name = snapshot.name;
			  		 	//   		let snapshot = await db.collection('addressDNS').where('address', '==', data[i].fromAddress)
									// .get();
									// snapshot.forEach(value =>{
									// 	// console.log(value.id);
									// 	// console.log(value.data());
									// 	name = value.data().name == undefined ? 'no' : value.data().name;
									// })	
								}else{
									let snapshot = await searchNameNew(data[i].toAddress);
									name = snapshot.name;
			  		 	//   		let snapshot = await db.collection('addressDNS').where('address', '==', data[i].toAddress)
									// .get();
									// snapshot.forEach(value =>{
									// 	// console.log(value.id);
									// 	// console.log(value.data());
									// 	name = value.data().name == undefined ? 'no' : value.data().name;

									// })	
								}
			  		 	  	
		  		 	  }

			  		 	  
			  		//  	  if(data[i].toAddress == address){
							// name = data[i].fromName;
			  		//  	  }else{
							// name = data[i].toName;
			  		//  	  }	  		 	  	

			    	 

			          let result = {
			          	'sortDate':data[i].updateTime,
			            'updateTime':parseInt(data[i].updateTime),
			            'uid':decodedToken.uid,
			            'paymentMethod':'sendLike',
			            'bankName':'sendLike',
			            'to':checkPayment == true ? 'Pay' : checkLotFrom == true ? 'Lock to win' : checkLotTo == true ? 'Lock to win' : checkCompFrom == true ? 'LIKE Compound' : checkCompTo == true ? 'LIKE Compound' : checkRewa == true ? 'Reward' : checkLo == true ? 'Lock' : checkAd == true ? 'Ads'  : checkUnlocked == true ? 'Unlock' : name ,
			            'title': data[i].toAddress == address ? 'recv' : 'sent',
			            'tx':data[i].tx,
			            'type':'transaction',
			            'accountNumber':data[i].fromAddress,
			            'fee':data[i].toAddress,
			            'baht':(data[i].amount/10e17).toString(),
			            'slip':'no',
			            'phoneNumber':data[i].blockNumber.toString(),
			            'status':data[i].running.toString(),
			            // 'message':'d'
			            'message': data[i].message == null ? '' : data[i].message.toString()

			          };

			          dataBack.push(result);
		  		 }
		  		 // console.log(dataBack);
		
		        let result = sortByDate(dataBack).slice(-1,1);
		        // console.log(result[0]);
		        res.json({statusCode:200, result: result[0]});
		   	});        
        });      
       }).catch(e =>{
        		res.json({statusCode:404, result:false});
       });//verifyIdTok

});
app.post('/getHistoryNewTest', function(req, res){
    const uid = req.body.uid;
    const address = req.body.address;
    const db = admin.firestore();
      // const sqlTransaction = 'SELECT * FROM logTransaction WHERE logTransaction.toAddress = ? OR logTransaction.fromAddress = ? ORDER BY logTransaction.running DESC LIMIT 30';
      // const sqlTransaction = 'SELECT logTransaction.* , logTransactionMessage.message FROM logTransaction LEFT JOIN logTransactionMessage ON logTransaction.tx = logTransactionMessage.tx WHERE logTransaction.toAddress = ? OR logTransaction.fromAddress = ? ORDER BY logTransaction.running DESC LIMIT 30';
      const sqlTransaction = 'SELECT running, fromAddress, toAddress, tx, amount, blockNumber, updateTime, createTime, chain FROM logTransaction  WHERE logTransaction.toAddress = ? OR logTransaction.fromAddress = ? ORDER BY logTransaction.running DESC LIMIT 30';
      const fieldSQLTransaction = [address, address];

      const sqlWithdraw = 'SELECT * FROM withdraw WHERE uid = ? ORDER BY running DESC LIMIT 15';
      // const fieldSQLWithdraw = [decodedToken.uid];
      const fieldSQLWithdraw = [uid];

      selectMysql(sqlWithdraw, fieldSQLWithdraw).then(data => {
        	// console.log(data);
        	let dataBack = [];
       
	        for(let i=0;i<data.length;i++){
	          let status;
	          if(data[i].status === '1'){
	            status = 1
	          }else if(data[i].status === '2'){
	            status = 2;
	          }else if(data[i].status === '3'){
	          	status = 3;
	          }else{
	            status = 0;
	          }
	          let result = {
	          	'sortDate':data[i].updated_time,
	            'updateTime':parseInt(data[i].updated_time),
	            'uid':data[i].uid,
	            'paymentMethod':data[i].paymentMethod,
	            'bankName':data[i].bankName,
	            'tx':data[i].tx,
	            'to':'bank',
	            'title':'recv',
	            'type':'cash',
	            'accountNumber':data[i].accountNumber,
	            'fee':data[i].fee,
	            'baht':data[i].baht,
	            'slip':data[i].slip,
	            'phoneNumber':data[i].phoneNumber.toString(),
	            'status':status.toString(),
	            'message':'no'
	          };
	          dataBack.push(result);

	        }

	        // console.log(dataBack);
	        console.log('....loading');
		  	selectMysql(sqlTransaction, fieldSQLTransaction).then(async (data) => {
		  		console.log('end select');
		  		//console.log(data);
		  		 for(let i=0;i<data.length;i++){
		  		 	  let checkPayment = checkShop(data[i].toAddress);
		  		 	  // let checkPaymentFrom = checkShop(data[i].fromAddress);

		  		 	  let checkRewa = checkRewards(data[i].fromAddress);
		  		 	  // let checkRewaTo = checkRewards(data[i].toAddress);

		  		 	  let checkLo = checkLock(data[i].toAddress);
		  		 	  // let checkFrom = checkLock(data[i].fromAddress);

		  		 	  let checkAd = checkAds(data[i].fromAddress);
		  		 	  // let checkAdTo = checkAds(data[i].toAddress);

		  		 	  let checkUnlocked = checkUnlock(data[i].fromAddress);
		  		 	  // let checkUnlockedTo = checkUnlock(data[i].toAddress);

		  		 	  let checkLotFrom = checkLotto(data[i].fromAddress);
		  		 	  let checkLotTo = checkLotto(data[i].toAddress);
		  		 	  let checkCompFrom = checkCompound(data[i].fromAddress);
		  		 	  let checkCompTo = checkCompound(data[i].toAddress);
		  		 	  // let checkKickback = checkKickback(data[i].fromAddress);
		  		 	  // let checkKickback = checkAds(data[i].fromAddress);
		  		 	  if(checkLo == false){
		  		 	  	checkLo = checkLock(data[i].fromAddress);
		  		 	  }

					  let name = 'no';
					console.log('checkPayment' + checkPayment);
					console.log('checkRewa' + checkRewa);
					console.log('checkLo' + checkLo);
					console.log('checkAd' + checkAd);
					console.log('checkUnlocked' + checkPayment);
					console.log('checkLotFrom' + checkLotFrom);
					console.log('checkLotTo' + checkLotTo);
					console.log('checkCompFrom' + checkCompFrom);
					console.log('checkCompTo' + checkCompTo);
					
					
		  		 	  if(checkPayment == false && checkRewa == false &&
		  		 	  	checkLo == false && checkAd == false &&
		  		 	  	checkUnlocked == false && checkLotFrom == false &&
		  		 	  	checkLotTo == false && checkCompFrom == false && checkCompTo == false
		  		 	  	){
		  		 	  	console.log('of some false');
							// let name = 'no';
		  		 	  		if(data[i].toAddress == address) {
		  		 	  			console.log("recv");
			  		 	//   		let snapshot = await db.collection('addressDNS').where('address', '==', data[i].fromAddress)
									// .get();
									// snapshot.forEach(value =>{
									// 	console.log(value.id);
									// 	console.log(value.data());
									// 	name = value.data().name == undefined ? 'no' : value.data().name;
									// })	
									let snapshot = await searchNameNew(data[i].fromAddress);
									name = snapshot.name;
								}else{
									console.log("sent");
									let snapshot = await searchNameNew(data[i].toAddress);
									name = snapshot.name;
			  		 	//   		let snapshot = await db.collection('addressDNS').where('address', '==', data[i].toAddress)
									// .get();
									// snapshot.forEach(value =>{
									// 	console.log(value.id);
									// 	console.log(value.data());
									// 	name = value.data().name == undefined ? 'no' : value.data().name;

									// })	
								}
			  		 	  	
		  		 	  }

			  		 	  
		 	  	
			    	 
		  		 	  try{
				          let result = {
				          	'sortDate':data[i].updateTime,
				            'updateTime':parseInt(data[i].updateTime),
				            'uid': uid,
				            'paymentMethod':'sendLike',
				            'bankName':'sendLike',
				            'to':checkPayment == true ? 'Pay' : checkLotFrom == true ? 'Lock to win' : checkLotTo == true ? 'Lock to win' : checkCompFrom == true ? 'LIKE Compound' : checkCompTo == true ? 'LIKE Compound' : checkRewa == true ? 'Reward' : checkLo == true ? 'Lock' : checkAd == true ? 'Ads'  : checkUnlocked == true ? 'Unlock' : name ,
				            'title': data[i].toAddress == address ? 'recv' : 'sent',
				            'tx':data[i].tx,
				            'type':'transaction',
				            'accountNumber':data[i].fromAddress,
				            'fee':data[i].toAddress,
				            'baht':(data[i].amount/10e17).toString(),
				            'slip':'no',
				            'phoneNumber':data[i].blockNumber.toString(),
				            'status':data[i].running.toString(),
				            // 'message':'d'
				            'message': data[i].message == null ? '' : data[i].message.toString()

				          };		  		 	  	
		  		 	 


			         	 	dataBack.push(result);
			      		}catch(e){
			  		 	  	console.log(e);
			  		 	}

		  		 }
		  		
		
		        let result = sortByDate(dataBack).slice(-1,1);
		        // console.log(result[0]);
		        res.json({statusCode:200, result: result[0]});
		   	});        
        });      
 
});

app.get('/getHistoryCenter', function(req, res){
    var url = require('url');
    var url_parts = url.parse(req.url, true);
    var query = url_parts.query;
    var term = req.query.term;
      const sqlTransaction = 'SELECT * FROM logTransaction WHERE toAddress = ? OR fromAddress = ? ORDER BY running DESC LIMIT 30';
      const fieldSQLTransaction = [term, term];
            let dataBack = [];
		  	selectMysql(sqlTransaction, fieldSQLTransaction).then(data => {
		  		console.log('hel');
		  		console.log(data.length);
		  		 for(let i=0;i<data.length;i++){
		  		 	console.log(i);
			          let result = {
			          	'sortDate':data[i].updateTime,
			            'updateTime':parseInt(data[i].updateTime),
			            'paymentMethod':'sendLike',
			            'bankName':'sendLike',
			            'tx':data[i].tx,
			            'type':'transaction',
			            'from':data[i].fromAddress,
			            'to':data[i].toAddress,
			            'baht':(data[i].amount/10e17).toString(),
			            'phoneNumber':data[i].blockNumber.toString(),
			            'status':data[i].running.toString()
			          };
			          dataBack.push(result);
		  		 }

			
		        let result = sortByDate(dataBack).slice(-1,1);
		        // console.log(result[0]);
		        res.json({statusCode:200, result: result[0]});
		   	});        
});

function sortByDate(data,){
	let arrayData = [];
	let newData = data.sort(function(a,b){
	  return b.sortDate - a.sortDate;
	});
	arrayData.push(newData);
	return arrayData;
}


app.post('/searchName', function(req, res){
	const _token = req.body._token;
    const term = req.body.term;
      admin.auth().verifyIdToken(_token)
    	.then(function (decodedToken){
    		searchName(term).then(data =>{
    			let result = {
    				fullname: data.fullname,
    				phone_number: data.phone_number
    			};
    			res.json({statusCode:200, result: result});
    		});
    	}).catch(e=>{
    		res.json({statusCode:404, result:'Authentication failed !'});
    	});
});
app.post('/searchNameNew', function(req, res){
	const _token = req.body._token;
    const term = req.body.term;
      admin.auth().verifyIdToken(_token)
    	.then(function (decodedToken){
    		console.log(_token);
    		searchNameNew(term).then(data =>{
    			let result = {
    				name: data.name,
    				phoneNumber: data.phoneNumber,
    				address: data.address
    			};
    			res.json({statusCode:200, result: result});
    		});
    	}).catch(e=>{
    		res.json({statusCode:404, result:'Authentication failed !'});
    	});
});

app.post('/searchUserHaveWallet', function(req, res){
	const phoneNumber = req.body.phoneNumber;
	const firstname = req.body.firstname;
	const lastname = req.body.lastname;
	const application = req.body.application;

	const firebase = admin.firestore();
    
	const searchUser = async function() {
      admin.auth().getUserByPhoneNumber(phoneNumber)
    	.then(async function (userRecord){
			if(userRecord != null){
			   //นำ UID ที่ได้จากเบอร์มาค้นหา
			   const users = await firebase.collection('users').doc(userRecord.uid).get();
			   const addressDNS = await firebase.collection('addressDNS').doc(userRecord.uid).get();
			   if(users.data() != null && addressDNS.data() != null){
				//เจอกระเป๋า มีกระเป๋าเเล้ว
				res.json({statusCode:200, result: {"status":'HAVE_WALLET'}});
			   }else{
				//ส่งเเจ้งเตือน Error
				res.json({statusCode:200, result: {"status":'HAVE_NOT_WALLET'}});
				notifyError("\nแจ้ง Error ไม่มีกระเป๋า [ลบเบอร์ในระบบ Auth] \nลูกค้า Login Mini App จาก "+application+"\n"+firstname +" "+lastname +" \n"+phoneNumber);
				}
			}
    	}).catch(e =>{
			if(e == "Error: There is no user record corresponding to the provided identifier."){
				res.json({statusCode:200, result: {"status":'NOT_HAVE_AUTH'}});
			}else{
				res.json({statusCode:404, result:'Search failed !'+ e});
			}
    		
		});
	}
	searchUser();
});

app.post('/searchNameNewBlur', function(req, res){
	const _token = req.body._token;
    const term = req.body.term;
      admin.auth().verifyIdToken(_token)
    	.then(function (decodedToken){
    		console.log(_token);
    		searchNameNewBlur(term).then(data =>{
    		
    			res.json({statusCode:200, result: data});
    		});
    	}).catch(e=>{
    		res.json({statusCode:404, result:'Authentication failed !'});
    	});
});


app.post('/resetPassword', function(req, res){

	const jtoken = req.body.jtoken;
	const secret = req.body.secret;
	const password = req.body.password;
	if(password === undefined){
		res.json({statusCode:202, result:'no password'});
	}else{

		  
		 	console.log(secret);
		  	 	//change password
		  	 	const db = admin.firestore();
		  	 	db.collection('resetpassword')
		  	 	.where('secret', '==', secret)
		  	 	.get().then(snapshot =>{
		  	 		if(snapshot.empty){
		  	 			res.json({statusCode:404, result:'empty'});
					}else{
					 snapshot.forEach(doc => {
						 	try {
								jwt.verify(doc.data().jwt, secret, function(err, decoded) {
									  const currentTime =  Math.floor(Date.now() / 1000);
									  if(currentTime > decoded.exp){
									  	res.json({statusCode:404, result: 'expired'});
									  }else{
									  	admin.auth().getUserByEmail(decoded.data)
							  	 		.then(function(userRecord){
											admin.auth().updateUser(userRecord.uid, {
											  password: password
											})
											  .then(function(userRecord) {
											    // See the UserRecord reference doc for the contents of userRecord.
											    //console.log('Successfully updated user', userRecord.toJSON());
											    res.json({statusCode:200, result:"success"});
											  })
											  .catch(function(error) {
											    console.log('Error updating user:', error);
											  }); 
							  	 		}).catch(function(e){
							  	 			console.log(decoded.data);
							  	 			console.log(e);
							  	 			res.json({statusCode:404, result: e});
							  	 		});	  	
									  }

								  });
							  }catch(e) {
							  	res.json({statusCode:404, result: e});
							  }					 	
					 	
						 });
					 }
		  	 	});

	}


});


//**
//reset secret @step2
//***
app.post('/resetSecret', function(req, res){

	const jtoken = req.body.jtoken;
	const secret = req.body.secret;
	const newsecret = req.body.newsecret;

	if(newsecret === undefined){
		res.json({statusCode:202, result:'no secret'});
	}else{
		 	console.log(secret);
		  	 	//change password
		  	 	const db = admin.firestore();
		  	 	db.collection('resetSecret')
		  	 	.where('secret', '==', secret)
		  	 	.get().then(snapshot =>{
		  	 		if(snapshot.empty){
		  	 			res.json({statusCode:404, result:'empty'});
					}else{
					 snapshot.forEach(doc => {
						jwt.verify(doc.data().jwt, secret, function(err, decoded) {


							  const currentTime =  Math.floor(Date.now() / 1000);
							  if(currentTime > decoded.exp){
							  	res.json({statusCode:404, result: 'expired'});
							  }else{
								recoveryMasterKey(decoded.data, decoded.phone_number).then(masterKey =>{
									// console.log(doc.id);
									setUpNewSecretSet(doc.id, newsecret).then(call => {
					                  request.post({ url: process.env.DOMAIN+'/adminRecoverySeed', form: { 
					                  // 	console.log('call');
					                  // request.post({ url: 'http://192.168.86.116:9093/adminRecoverySeed', form: { 
					                    apiKey: process.env.APIKEY,
					                    secretKey: process.env.SECRETKEY,
					                    uid: doc.id,
					                    masterKey: masterKey,
						                } }, function (err, httpResponse, body) {
						                  console.log(body);
						                  let callback = JSON.parse(body);
						                  if(callback.statusCode == 200){
						                  	res.json({statusCode:200, result:'successfuly'});
						                  }
						                  //
						                });  										
									}).catch(e=>{
										res.json({statusCode:203, result:'failed setUpNew'});
									});
 	
								})
						
							  }

							  });			 	
					 	
						 });
					 }
		  	 	});

	}


});
app.post('/requestResetPassword', function(req, res){
	const email = req.body.email;
	const db = admin.firestore();

	console.log(email);
	console.log('1123');
	admin.auth().getUserByEmail(email)
	  .then(function(userRecord) {
	    // See the UserRecord reference doc for the contents of userRecord.
	    console.log('Successfully fetched user data:', userRecord.toJSON());

	    const uid = userRecord.uid;
		crypto.randomBytes(48, function(err, buffer) {
		  var secret = buffer.toString('hex');
		
			const jtoken = jwt.sign({
			  data: email.trim()
			}, secret, { expiresIn: '1h' });

		    db.collection('resetpassword')
		    .doc(uid)
		    .set({
		    	expire: Math.floor(Date.now() / 1000) + (15 * 60),
		    	uid: uid,
		    	email: email,
		    	jwt: jtoken,
		    	secret: secret
		    }).then(data =>{
			// async..await is not allowed in global scope, must use a wrapper
			async function sendEmail(email, jtoken, secret) {


			  // create reusable transporter object using the default SMTP transport
			  let transporter = nodemailer.createTransport({
				service: 'gmail',
			    auth: {
			      user: "<EMAIL>", // generated ethereal user
			      pass: "A$Prachakij01" // generated ethereal password
			    }
			  });

			  // send mail with defined transport object
			  let info = await transporter.sendMail({
			    from: '"Likewallet ลืมรหัสผ่าน " <<EMAIL>>', // sender address
			    to: email, // list of receivers
			    subject: "ลืมรหัสผ่าน Likewallet ?", // Subject line
			    text: "คุณร้องขอการเปลี่ยนรหัสผ่านมา หากไม่ใช่อย่ากด link : https://newlikewallet.web.app/resetpassword/"+secret+" นี้เพื่อเปลี่ยนรหัส", // plain text body
			    html: "<b>คุณร้องขอการเปลี่ยนรหัสผ่านมา หากไม่ใช่อย่ากด link : https://newlikewallet.web.app/resetpassword/"+secret+" นี้เพื่อเปลี่ยนรหัส</b>" // html body
			  });

			  console.log("Message sent: %s", info.messageId);
			  // Message sent: <<EMAIL>>

			  // Preview only available when sending through an Ethereal account
			  console.log("Preview URL: %s", nodemailer.getTestMessageUrl(info));
			  // Preview URL: https://ethereal.email/message/WaQKMgKddxQDoou...

			  res.json({statusCode:200, result: 'success'});
			}

			sendEmail(email, jtoken, secret).catch(console.error);
		    });


	    });
	  })
	  .catch(function(error) {
	   console.log('Error fetching user data:', error);
	   res.json({statusCode:404, result: error});
	  });


});
//**
//reset secret @step1
//***
app.post('/requestResetSecret', function(req, res){
	const email = req.body.email;
	const phone_number = req.body.phone_number;
	const db = admin.firestore();
	if(email != undefined){
		admin.auth().getUserByEmail(email)
		  .then(function(userRecord) {
		    // See the UserRecord reference doc for the contents of userRecord.
		    console.log('Successfully fetched user data:', userRecord.toJSON());

		    const uid = userRecord.uid;
			crypto.randomBytes(48, function(err, buffer) {
			  var secret = buffer.toString('hex');
			
				const jtoken = jwt.sign({
				  data: email.trim(),
				  phone_number: userRecord.phoneNumber
				}, secret, { expiresIn: '1h' });
				console.log(jtoken);
			    db.collection('resetSecret')
			    .doc(uid)
			    .set({
			    	expire: Math.floor(Date.now() / 1000) + (15 * 60),
			    	uid: uid,
			    	email: email,
			    	phone_number: userRecord.phoneNumber,
			    	jwt: jtoken,
			    	secret: secret
			    }).then(data =>{

				// async..await is not allowed in global scope, must use a wrapper
				async function sendEmail(email, jtoken, secret) {


				  // create reusable transporter object using the default SMTP transport
				  let transporter = nodemailer.createTransport({
					service: 'gmail',
				    auth: {
				      user: "<EMAIL>", // generated ethereal user
				      pass: "a_7F(sHbJ%(}-}?w]v]}~k/_\"@VE[~JD-^f?h.QL{t\"5(7^+R33Pf}a/bX!*[ZNT)UG-r;A" // generated ethereal password
				    }
				  });

				  // send mail with defined transport object
				  let info = await transporter.sendMail({
				    from: '"Likewallet ลืมรหัสลับ " <<EMAIL>>', // sender address
				    to: email, // list of receivers
				    subject: "ลืมรหัสลับ Likewallet ?", // Subject line
				    text: "คุณร้องขอการเปลี่ยนรหัสลับมา หากไม่ใช่อย่ากด link : https://newlikewallet.web.app/inputNewSecret/"+secret+" นี้เพื่อเปลี่ยนรหัสลับ", // plain text body
				    html: "<b>คุณร้องขอการเปลี่ยนรหัสลับมา หากไม่ใช่อย่ากด link : https://newlikewallet.web.app/inputNewSecret/"+secret+" นี้เพื่อเปลี่ยนรหัสลับ</b>" // html body
				  });

				  console.log("Message sent: %s", info.messageId);
				  // Message sent: <<EMAIL>>

				  // Preview only available when sending through an Ethereal account
				  console.log("Preview URL: %s", nodemailer.getTestMessageUrl(info));
				  // Preview URL: https://ethereal.email/message/WaQKMgKddxQDoou...

				  res.json({statusCode:200, result: 'success'});
				}

				sendEmail(email, jtoken, secret).catch(console.error);
			    });


		    });
		  })
		  .catch(function(error) {
		   console.log('Error fetching user data:', error);
		   res.json({statusCode:404, result: error});
		  });	
		}else if (phone_number != undefined){

		if(email != undefined){
			admin.auth().getUserByEmail(email)
				  .then(function(userRecord) {
				    // See the UserRecord reference doc for the contents of userRecord.
				    console.log('Successfully fetched user data:', userRecord.toJSON());

				    const uid = userRecord.uid;
					crypto.randomBytes(48, function(err, buffer) {
					  var secret = buffer.toString('hex');
					
						const jtoken = jwt.sign({
						  data: email.trim(),
				  		  phone_number: userRecord.phoneNumber						  
						}, secret, { expiresIn: '1h' });

					    db.collection('resetSecret')
					    .doc(uid)
					    .set({
					    	expire: Math.floor(Date.now() / 1000) + (15 * 60),
					    	uid: uid,
					    	email: email,
					    	phone_number: userRecord.phoneNumber,
					    	jwt: jtoken,
					    	secret: secret
					    }).then(data =>{
						// async..await is not allowed in global scope, must use a wrapper
						async function sendEmail(email, jtoken, secret) {


						  // create reusable transporter object using the default SMTP transport
						  let transporter = nodemailer.createTransport({
							service: 'gmail',
						    auth: {
						      user: "<EMAIL>", // generated ethereal user
						      pass: "a_7F(sHbJ%(}-}?w]v]}~k/_\"@VE[~JD-^f?h.QL{t\"5(7^+R33Pf}a/bX!*[ZNT)UG-r;A" // generated ethereal password
						    }
						  });

						  // send mail with defined transport object
						  let info = await transporter.sendMail({
						    from: '"Likewallet ลืมรหัสลับ " <<EMAIL>>', // sender address
						    to: email, // list of receivers
						    subject: "ลืมรหัสลับ Likewallet ?", // Subject line
						    text: "คุณร้องขอการเปลี่ยนรหัสลับมา หากไม่ใช่อย่ากด link : https://newlikewallet.web.app/inputNewSecret/"+secret+" นี้เพื่อเปลี่ยนรหัสลับ", // plain text body
						    html: "<b>คุณร้องขอการเปลี่ยนรหัสลับมา หากไม่ใช่อย่ากด link : https://newlikewallet.web.app/inputNewSecret/"+secret+" นี้เพื่อเปลี่ยนรหัสลับ</b>" // html body
						  });

						  console.log("Message sent: %s", info.messageId);
						  // Message sent: <<EMAIL>>

						  // Preview only available when sending through an Ethereal account
						  console.log("Preview URL: %s", nodemailer.getTestMessageUrl(info));
						  // Preview URL: https://ethereal.email/message/WaQKMgKddxQDoou...

						  res.json({statusCode:200, result: 'success'});
						}

						sendEmail(email, jtoken, secret).catch(console.error);
					    });


				    });
				  })
				  .catch(function(error) {
				   console.log('Error fetching user data:', error);
				   res.json({statusCode:404, result: error});
				  });				
		}else{
			admin.auth().getUserByPhoneNumber(phone_number)
				  .then(function(userRecord) {
				    // See the UserRecord reference doc for the contents of userRecord.
				    console.log('Successfully fetched user data:', userRecord.toJSON());

				    const uid = userRecord.uid;
					crypto.randomBytes(48, function(err, buffer) {
					  var secret = buffer.toString('hex');
					

						console.log(secret);
						const jtoken = jwt.sign({
						  data: 'no',
						  phone_number: userRecord.phoneNumber
						}, secret, { expiresIn: '1h' });
	

					    db.collection('resetSecret')
					    .doc(uid)
					    .set({
					    	expire: Math.floor(Date.now() / 1000) + (15 * 60),
					    	uid: uid,
					    	email:'no',
					    	phone_number: phone_number,
					    	jwt: jtoken,
					    	secret: secret
					    }).then(data =>{

						// async..await is not allowed in global scope, must use a wrapper
							//send SMS
							sendSMS("https://newlikewallet.web.app/inputNewSecret/"+secret, phone_number, 'LikeWallet')
							.then(data =>{
								res.json({statusCode:200, result:'sent sms to reset secret'});
							}).catch(e =>{
								res.json({statusCode:400, result:'sms failed'});
							});
					    });


				    });
				  })
				  .catch(function(error) {
				   console.log('Error fetching user data:', error);
				   res.json({statusCode:404, result: error});
				  });			
		}
			
		}
	


});

function sendSMS(message, phone_number, application) {
	return new Promise((resolve, reject) =>{
		 clientTwilio.messages
		      .create({
		        body: message,
		        from: application,
		        to: phone_number
		      })
	       .then(message => {
		        console.log('hi man');

		        let ref = db.collection('logs/sms/twilio').doc(message.sid);

				resolve(true);
			}).catch(e =>{
				resolve(false);
			});
		});
}
app.post('/getSellAddress', function(req, res){
	res.json({statusCode:200, result: '******************************************'});
});
app.post('/getSellAddressNew', function(req, res){
	const bank = req.body.bank;
	var con = mysql.createConnection({
      host     : process.env.RDS_HOST,
      user     : process.env.RDS_USERNAME,
      password : process.env.RDS_PASSWORD,
      database : process.env.RDS_DATABASE
    });
     
    con.connect();

    const application = req.body.application;
    const sql = "SELECT * FROM bank_withdraw WHERE short = ? ORDER BY running DESC LIMIT 1";

    con.query(sql, [bank], function (err, result, fields) {
      if (err) {
        con.end();
        console.log(err);
        $log('selectMysql : '+ err);
        res.json({statusCode:404, result:'err'});
      }

       con.end();
      	res.json({statusCode:200, result: result[0]['address'].toString()});
  	});

});
app.post('/getUpdateiOS', function(req, res){
	res.json({statusCode:200, result: 'https://testflight.apple.com/join/h4Q8S025'});
});
app.post('/getCovid', function(req, res){
	res.json({statusCode:200, result: 'https://covid-19.cyphinvest.io'});
});


app.post('/updatePhoneNewLikewallet', function(req, res){
	const apikey = req.body.apikey;
	const secret = req.body.secret;
	const phoneNumber = req.body.phoneNumber;
	const uid = req.body.uid;

	const db = admin.firestore();
	if(apikey == process.env.APIKEY && secret == process.env.SECRETKEY){
		admin.auth().updateUser(uid, {
		  phoneNumber: phoneNumber
		})
		  .then(function(userRecord) {
		  	console.log('Successfully updated user', userRecord.toJSON());
		  	if(userRecord.uid == uid){
				db.collection('users')
				.doc(uid)
				.update({
					phone_number: phoneNumber
				}).then(data =>{
					res.json({statusCode:200, result: 'update successfully !'});
				});	
		  	}
		  })
		  .catch(function(error) {
		    console.log('Error updating user:', error);
		  });	
	}
});


function searchName(term){
    return new Promise((resolve, reject)=>{
        var client = new elasticsearch.Client({
            host: process.env.ELASTIC_SEARCH,
                    apiVersion: process.env.ELASTICVERSION,
        });
       const start = async function () {
        const response = await client.search({
            index: 'likecoin',
            type: 'users',
            body: {
                from: 0, size: 1,
                query: {
                    multi_match: {
                        query: term,
                        fields: ["address","mobile"],
                        type: "phrase_prefix"
                    }
                }
            }
        });
            if (response.hits.hits.length > 0) {
                // i--;
                for (const tweet of response.hits.hits) {
                    // console.log(tweet._source.fullname);
                    let back = {
                        fullname: tweet._source.fullname,
                        phone_number: tweet._source.mobile
                    }
                    resolve(back);
                }
            }else{
                let back = {
                    fullname:'no',
                    phone_number:'no'
                }
                resolve(back);
            }
    };
    start();
    });
};

function searchNameNew(term){
    return new Promise((resolve, reject)=>{
        var client = new elasticsearch.Client({
            host: process.env.ELASTIC_SEARCH,
                    apiVersion: process.env.ELASTICVERSION,
        });
       const start = async function () {
        const response = await client.search({
            index: process.env.ELASTIC_INDEX,
            type: 'users',
            body: {
                from: 0, size: 1,
                query: {
                    bool: {
				      must: [
				        {
				    query_string: {
				            fields: ["phoneNumber", "address"],
				            query: term
				          }
				        }
				      ]
				    }
                }
            }
        });
            if (response.hits.hits.length > 0) {
                // i--;
                for (const tweet of response.hits.hits) {
                    // console.log(tweet._source.fullname);
                    let back = {
                        name: tweet._source.name,
                        phoneNumber: tweet._source.phoneNumber,
                        address: tweet._source.address
                    }
                    resolve(back);
                }
            }else{
                let back = {
                    name:'no',
                    phoneNumber:'no',
                    address:'no'
                }
                resolve(back);
            }
    };
    start();
    });
};

function searchNameNewBlur(term){
    return new Promise((resolve, reject)=>{
        var client = new elasticsearch.Client({
            host: process.env.ELASTIC_SEARCH,
                    apiVersion: process.env.ELASTICVERSION,
        });
       const start = async function () {
        const response = await client.search({
            index: process.env.ELASTIC_INDEX,
            type: 'users',
            body: {
                from: 0, size: 1,
                query: {
                    bool: {
				      must: [
				        {
				    query_string: {
				            fields: ["phoneNumber", "address"],
				            query: term
				          }
				        }
				      ]
				    }
                }
            }
        });
            if (response.hits.hits.length > 0) {
                // i--;
                for (const tweet of response.hits.hits) {
                    // console.log(tweet._source.fullname);
                    let back = {
                        name: tweet._source.name,
                        phoneNumber: tweet._source.phoneNumber,
                        address: tweet._source.address
                    }
                    resolve(back.name.substring(0, 8)+'****');
                }
            }else{
                let back = {
                    name:'no',
                    phoneNumber:'no',
                    address:'no'
                }
                resolve(back);
            }
    };
    start();
    });
};


app.post('/getUsersLikeWalletElastic', auth, function(req, res){

	async function getUser() {
		getUsersLikeWalletElastic().then(data=>{
			res.json(data);
		})
	}
	getUser();
})
function getUsersLikeWalletElastic(){
    return new Promise((resolve, reject)=>{
        var client = new elasticsearch.Client({
            host: process.env.ELASTIC_SEARCH,
                    apiVersion: process.env.ELASTICVERSION,
        });
       const start = async function () {
        const response = await client.search({
            index: process.env.ELASTIC_INDEX,
            type: 'users',
            body: {
                from: 0, size: 100000,
                query: {
                   match_all:{}
                }
            }
        });
            if (response.hits.hits.length > 0) {
                let data = {
                	status:200,
                	result: response.hits.hits
                }
                resolve(data);
            }else{
                let back = {
                	status:204,
                    result: 'no'
                }
                resolve(back);
            }
    };
    start();
    });
};
app.post('/searchUserByName', auth, function(req, res){

	async function getUser(keyword) {
		searchUserByName(keyword).then(data=>{
			res.json(data);
		})
	}
	getUser(req.body.keyword);
})
function searchUserByName(keyword){
    return new Promise((resolve, reject)=>{
        var client = new elasticsearch.Client({
            host: process.env.ELASTIC_SEARCH,
                    apiVersion: process.env.ELASTICVERSION,
        });
       const start = async function () {
        const response = await client.search({
            index: process.env.ELASTIC_INDEX,
            type: 'users',
            body: {
                from: 0, size: 10000,
				  query: {
				    bool: {
				      should: [
				        {
				          match: {
				            name: keyword
				          }
				        },
				        {
				          wildcard: {
				            phoneNumber: "*"+keyword+"*"
				          }
				        },
				        {
				          wildcard: {
				            address: "*"+keyword+"*"
				          }
				        }
				      ]
				    }
				  }
            }
        });
            if (response.hits.hits.length > 0) {

                let data = {
                	status:200,
                	result: response.hits.hits
                }
                resolve(data);
            }else{
                let back = {
                	status:204,
                    result: 'no'
                }
                resolve(back);
            }
    };
    start();
    });
};
//addressDNA
function retriveAddressForList(uid, tag, userKey){
	return new Promise((resolve, reject)=>{
		const db = admin.firestore();
		// console.log(uid);

		db.collection('masterKeyEncrypted').doc(uid).get().then(masterKey =>{
					// console.log(masterKey.data());
			recoveryMasterKeyLike(uid, masterKey.data().key.toString()).then(masterKeyDecrypt =>{
				db.collection('cyphermines').doc(uid).get().then(cypherKey =>{
						// console.log(cypherKey.data());
		 			try{
		              var comb = secrets.combine([userKey, masterKeyDecrypt, cypherKey.data().key.toString()]);
		              comb = secrets.hex2str(comb);
		              let content = {
		                content: comb,
		                tag: tag
		              }
		              var finalSeed = decrypt(content);
	                  RetrievePKED2519(finalSeed).then(pkseed =>{
	                  	let addressOwner = retrieveAddress(pkseed);
	                  	resolve(addressOwner); 
	                  });		              
	

		            }catch(e){
		            	console.log(e);
		              reject();
		            }				
				});
			});

		});
	});
}


function disableElasticsearch(uid) {
	return new Promise((resolve, reject) => {
       var client = new elasticsearch.Client({
            host: process.env.ELASTIC_SEARCH,
                    apiVersion: process.env.ELASTICVERSION,
       });
       const start = async function () {
        const response = await client.delete({
            index: process.env.ELASTIC_INDEX,
            type: 'users',
            id: uid
        });

        resolve();
    	};
    	start();
	});
	
}

app.post('/disableUserNoElastic', auth, function(req, res){
	const uid = req.body.uid;
	const db = admin.firestore();
	const cyphermines = db.collection('cyphermines').doc(uid);
		const disabledCyphermines = db.collection('disabledCyphermines');
      	const disabledUsers = db.collection('disabledUsers');
		const masterData = db.collection('masterKeyEncrypted').doc(uid);      
		const disabledMasterData = db.collection('disabledMasterData'); 
		const users = db.collection('users').doc(uid);
		cyphermines.get()
		  .then(doc => {
		    if (!doc.exists) {
		      console.log('No such document!');
		    } else {
		      // console.log('Document data:', doc.data());
		      let dataCypher = doc.data();
		      dataCypher.uid = uid;
		      disabledCyphermines.add(dataCypher).then(result => {
	  			users.get()
			      .then(doc2 => {
			      	let dataUser = doc2.data();
		     	 	dataUser.uid = uid;
			      	disabledUsers.add(dataUser).then(result2 => {
				      	masterData.get()
				      	.then(doc3 =>{
					      	let dataMaster = doc3.data();
				     	 	dataMaster.uid = uid;				      		
				      		console.log(doc3);
				      		disabledMasterData.add(dataMaster).then(result3 =>{
									db.collection('users').doc(uid).get().then(snapshot =>{
								let dataUser = snapshot.data();
								    dataUser.uid = uid;
									db.collection('disabledUsers').add(
										dataUser
									).then(afterBackup =>{
										db.collection('users').doc(uid).delete().then(remove =>{
											admin.auth().deleteUser(uid).then(data=>{
												res.json({statusCode:200, result: 'disable uid '+ uid});
											}).catch(function(error) {
										    	console.log('Error deleting user:', error);
										  	});					
										});
									});
								});  
				      		});
				      	}).catch(err=>{
				      		$log(err);
				      		// return resolve(moveBackup(data));
				      		console.log(err);
				      	})
			      	}).catch(err =>{
			      		$log(err);
			      		console.log(err);
			      	});
			      }).catch(err => {
			      	$log(err);
			      	 console.log(err);
			      })		      	
		      }).catch(err =>{
		      	$log(err);
		      	console.log(err);
		      });
		
		    }
		  })
		  .catch(err => {
		  	$log(err);
		    console.log('Error getting document', err);
		  });	
});

function moveBackup(data) {
	return new Promise((resolve, reject) => {
		const cyphermines = data.db.collection('cyphermines').doc(data.uid);
		const disabledCyphermines = data.db.collection('disabledCyphermines');
   		const users = data.db.collection('users').doc(data.uid);
      	const disabledUsers = data.db.collection('disabledUsers');
		const masterData = data.db.collection('masterKeyEncrypted').doc(data.uid);      
		const disabledMasterData = data.db.collection('disabledMasterData');      	
		cyphermines.get()
		  .then(doc => {
		    if (!doc.exists) {
		      console.log('No such document!');
		    } else {
		      // console.log('Document data:', doc.data());
		      let dataCypher = doc.data();
		      dataCypher.uid = data.uid;
		      disabledCyphermines.add(dataCypher).then(result => {
	  			users.get()
			      .then(doc2 => {
			      	let dataUser = doc2.data();
		     	 	dataUser.uid = data.uid;
			      	disabledUsers.add(dataUser).then(result2 => {
				      	masterData.get()
				      	.then(doc3 =>{
					      	let dataMaster = doc3.data();
				     	 	dataMaster.uid = data.uid;				      		
				      		console.log(doc3);
				      		disabledMasterData.add(dataMaster).then(result3 =>{
				      			resolve(true);
				      		});
				      	}).catch(err=>{
				      		$log(err);
				      		console.log(err);
				      	})
			      	}).catch(err =>{
			      		$log(err);
			      		console.log(err);
			      	});
			      }).catch(err => {
			      	$log(err);
			      	console.log(err);
			      })		      	
		      }).catch(err =>{
		      	$log(err);
		      console.log(err);
		      });
		
		    }
		  })
		  .catch(err => {
		  	$log(err);
		    console.log('Error getting document', err);
		  });		

	});
}
app.post('/disabledUser', auth, function(req, res){
	const uid = req.body.uid;
	const db = admin.firestore();

	admin.auth().getUser(uid).then(_token =>{
	  disableElasticsearch(uid).then(success =>{
	  	const cyphermines = db.collection('cyphermines').doc(uid);
		const disabledCyphermines = db.collection('disabledCyphermines');
      	const disabledUsers = db.collection('disabledUsers');
		const masterData = db.collection('masterKeyEncrypted').doc(uid);      
		const disabledMasterData = db.collection('disabledMasterData'); 
const users = db.collection('users').doc(uid);
		cyphermines.get()
		  .then(doc => {
		    if (!doc.exists) {
		      console.log('No such document!');
		    } else {
		      // console.log('Document data:', doc.data());
		      let dataCypher = doc.data();
		      dataCypher.uid = uid;
		      disabledCyphermines.add(dataCypher).then(result => {
	  			users.get()
			      .then(doc2 => {
			      	let dataUser = doc2.data();
		     	 	dataUser.uid = uid;
			      	disabledUsers.add(dataUser).then(result2 => {
				      	masterData.get()
				      	.then(doc3 =>{
					      	let dataMaster = doc3.data();
				     	 	dataMaster.uid = uid;				      		
				      		console.log(doc3);
				      		disabledMasterData.add(dataMaster).then(result3 =>{
									db.collection('users').doc(uid).get().then(snapshot =>{
									let dataUser = snapshot.data();
								    dataUser.uid = uid;
									dataUser["timestamp"] = moment().format("DD-MM-YYYY hh:mm:ss");
											db.collection('addressDNS').doc(uid).get().then(snapshot2 =>{
											let dataAddressDNS = snapshot2.data();
											// dataAddressDNS.uid = uid;
											db.collection('disabledUsers').add(
												dataUser
												).then(afterBackup =>{
													db.collection('disabledAddressDNS').add(
														dataAddressDNS
													).then(afterBackup2 =>{
														db.collection('users').doc(uid).delete().then(remove =>{
															db.collection('addressDNS').doc(uid).delete().then(remove2 =>{
															admin.auth().deleteUser(uid).then(data=>{
																res.json({statusCode:200, result: 'disable uid '+ uid});
															}).catch(function(error) {
																console.log('Error deleting user:', error);
																});		
															});					
														});
													});
												});
										});
								});  
				      		});
				      	}).catch(err=>{
				      		$log(err);
				      		return resolve(moveBackup(data));
				      	})
			      	}).catch(err =>{
			      		$log(err);
			      		return resolve(moveBackup(data));
			      	});
			      }).catch(err => {
			      	$log(err);
			      	 return resolve(moveBackup(data));
			      })		      	
		      }).catch(err =>{
		      	$log(err);
		      	return resolve(moveBackup(data));
		      });
		
		    }
		  })
		  .catch(err => {
		  	$log(err);
		    console.log('Error getting document', err);
		  });	
	
		}).catch(e=>{
		console.log('error');
		})
	});

});
//get address ออกมา
app.post('/listUserAddress',auth ,function(req, res){

	const db = admin.firestore();
	var outputList = [];
    const promises = [];
	let promise = db.collection('users').get().then(users =>{

		users.forEach(async docs => {
			// console.log(docs.id);
			// console.log(docs.data());
			let json1 = docs.data();
			let json2 = {uid: docs.id};
			// json1 = json1.concat(json2);
			json1 = { ...json1, ...json2 };
			outputList.push(json1);
		});
		return;
	}).catch(e=>{
		console.log(e);
	})
	promises.push(promise);
    Promise.all(promises).then(() => {
    	let listUser = async function(){
	    	for(let i=0;i<outputList.length;i++){
	  			try{
					let address = await retriveAddressForList(outputList[i].uid, outputList[i]._token.tag, outputList[i]._token.keyFirst);
					console.log(address + " fullname : "+ outputList[i].firstName+ " " + outputList[i].lastName+ " phoneNumber: "+ outputList[i].phone_number);
					
					db.collection('addressDNS').doc(outputList[i].uid).get().then(dns =>{
						if(dns.exists){
							db.collection('addressDNS').doc(outputList[i].uid)
							.update({
								address: address.toLowerCase(),
								name: outputList[i].firstName + " " + outputList[i].lastName,
								phoneNumber: outputList[i].phone_number,
								locale: "en"
							});
						}else{
							db.collection('addressDNS').doc(outputList[i].uid)
							.set({
								address: address.toLowerCase(),
								name: outputList[i].firstName + " " + outputList[i].lastName,
								phoneNumber: outputList[i].phone_number,
								locale: "en"
							});
						}
					});

				}catch(e){
					console.log(outputList[i].uid + " no tag");
				}  		
	    	}
	    	res.json({statusCode:200, result:'successfully'});
	    }

	    listUser();



    })
    .catch(err => {
        console.log(err);
    })

});




app.post('/changePhoneAdmin', auth, function(req, res){
	console.log('changePhoneAdmin');
	const uid = req.body.uid;
	const newPhoneNumber = req.body.newPhoneNumber;
	const oldPhoneNumber = req.body.oldPhoneNumber;

	const db = admin.firestore();
	const dbOld = secondary.database();

    admin.auth().getUser(uid)
		.then(function (userRecord){
			//console.log(userRecord);
		
		db.collection('users').doc(uid).get().then(snapshot =>{
			// console.log(snapshot.data());
			// console.log(snapshot.id);
			db.collection('changePhone').doc(snapshot.id)
				.set(snapshot.data())
				.then(afterAdd =>{
					if(userRecord.phoneNumber === oldPhoneNumber){
						admin.auth().updateUser(uid, {
						  phoneNumber: newPhoneNumber
						})
						  .then(function(userRecord) {
						    // See the UserRecord reference doc for the contents of userRecord.
						    console.log('Successfully updated user', userRecord.toJSON());
						    db.collection('users').doc(uid).update({
						    	phone_number: newPhoneNumber
						    }).then(afterUpdate =>{
						    	db.collection('addressDNS').doc(uid).update({
						    		phoneNumber: newPhoneNumber
						    	}).then(updaetDNS =>{
								    var client = new elasticsearch.Client({
							        	host: process.env.ELASTIC_SEARCH,
							        	apiVersion: process.env.ELASTICVERSION,
							    	});	

								    let callUpdate = async function() {
										       const response =  await client.update({
									            index: process.env.ELASTIC_INDEX,
									            type: 'users',
									            id: uid,
									            body: {
									            	doc:{phoneNumber: newPhoneNumber}
									            }
									        }).then(data =>{
									        	secondary.auth().getUserByPhoneNumber(oldPhoneNumber)
									        		.then(olduser=>{
										        		secondary.auth().updateUser(olduser.uid, {
														  phoneNumber: newPhoneNumber
														}).then(function(userOld) {
															var users = db.ref("users/" + userOld.id);
															users.update({
																mobile: newPhoneNumber
															}).then(dataCallback =>{
																res.json({statusCode:200});
															});
														})
									        		}).catch(e=>{
									        			res.json({statusCode:200});
									        			console.log(e);
									        		})

									        });      	 	
								    }
								    callUpdate();						    		
						    	});
						    }).catch(e=>{
						    	console.log(e);
						    })
						  })
						  .catch(function(error) {
						    console.log('Error updating user:', error);
						  });						
					}
					
			});
		})
	});

});



app.post('/getEmailByAdmin', auth, function(req, res){
	const phoneNumber = req.body.phoneNumber;
	admin.auth().getUserByPhoneNumber(phoneNumber)
	  .then(function(userRecord) {
	    // See the UserRecord reference doc for the contents of userRecord.
	    console.log('Successfully fetched user data:', userRecord.toJSON());

	    res.json({statusCode:200, result:userRecord.toJSON()});
	  })
	  .catch(function(error) {

	    console.log('Error fetching user data:', error);
	  });

});

app.post('/syncEmailByAdmin', auth, function(req, res){
	const email = req.body.email;
	const uid = req.body.uid;
	admin.auth().getUser(uid)
	  .then(function(userRecord) {
	    // See the UserRecord reference doc for the contents of userRecord.
	    console.log('Successfully fetched user data:', userRecord.toJSON());

        admin.auth().getUserByEmail(email).then(data =>{ 
          if(email == userRecord.email){
          	res.json({statusCode: 204, result:'exists email !'});
          }else{    
	       	  
          }
        }).catch(e=>{
          console.log(e);
          	var generatePassword = '';
			    var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789$&!?';
			    var charactersLength = characters.length;
			    for (var i = 0; i < 12; i++) {
			      generatePassword += characters.charAt(Math.floor(Math.random() * charactersLength));
			    }
			    console.log(userRecord.uid);
	  	        admin.auth().updateUser(userRecord.uid, {
	              email: email,
	              emailVerified: false,
	              password: generatePassword
	            }).then((userRecord2) => {
	            	console.log('1836');
		           	//requestResetPassword
				    request.post({ url: process.env.URL_NEW_LIKEWALLET+'/requestResetPassword',
					     form: { 
					     	email:email		     } 
					     }, function (err, httpResponse, body) {
					     	console.log(body);
					     	let result = JSON.parse(body);
					     	if(result.statusCode === 200){
					     		res.json({statusCode:200, result:'please setup new password in email'});
					     	}else{
					     		res.json({statusCode:205, result:'something is wrong !'});
					     	}

					});                	
	            }).catch(e=>{
	            	res.json({statusCode:206, result:'email ซ้ำ'});
	            });
        });	    
	    // res.json({statusCode:200, result:userRecord.toJSON()});
	  })
	  .catch(function(error) {
	  	
	    console.log('Error fetching user data:', error);
	  });

});



//** SUPPORT SEED ZONE move to trash
/**
@step 1 การปลดล็อคกรณีมีแต่ seed ไม่ผ่านแอพ
*/
// กู้คืน address ที่ปิดการใช้
app.post('/getDisableUser',auth, function(req, res){
	const db = admin.firestore();
	const phone_number = req.body.phone_number;
	const adminKey = req.body.adminKey;
	if(adminKey != 'nT1VdrCrFqSkKukGS2yGancKtFNuilxA'){
		res.json({statusCode:404});
	}else{
	db.collection('disabledUsers')
		.where('phone_number', '=', phone_number)
		.get()
		.then(snapshot =>{
			var i=0;
			snapshot.forEach(value =>{
				if(i==0){
					i++;
					console.log('loop: ' + i);
					console.log(value.data());
					// res.json(value);
					 recoverySeedNoSecretDisabled(value.data().uid, 'no' + value.data()._token.concatPassword, value.data()._token.keyFirst, value.data()._token.keyEncrypt, value.data()._token.tag, 'no_number').then(seed => {
					 	console.log(seed);
					 	res.json({statusCode:200, result: seed});
					 });					
				}

				
			})

		});
	}
});


app.post('/getDisableUserFromID',auth, function(req, res){
	const db = admin.firestore();
	const id = req.body.id;
	const adminKey = req.body.adminKey;
	if(adminKey != 'nT1VdrCrFqSkKukGS2yGancKtFNuilxA'){
		res.json({statusCode:404});
	}else{
	db.collection('disabledUsers')
		.doc(id)
		.get()
		.then(value =>{
			var i=0;
			
				if(i==0){
					i++;
					console.log('loop: ' + i);
					console.log(value.data());
					// res.json(value);
					 recoverySeedNoSecretDisabled(value.data().uid, 'no' + value.data()._token.concatPassword, value.data()._token.keyFirst, value.data()._token.keyEncrypt, value.data()._token.tag, 'no_number').then(seed => {
					 	console.log(seed);
					 	res.json({statusCode:200, result: seed});
					 });					
				}

		

		});
	}
});

app.post('/getDisableUserActive', auth, function(req, res){
	const db = admin.firestore();
	const phone_number = req.body.phone_number;
	const adminKey = req.body.adminKey;
	if(adminKey != 'nT1VdrCrFqSkKukGS2yGancKtFNuilxA'){
		res.json({statusCode:404});
	}else{
	db.collection('users')
		.where('phone_number', '=', phone_number)
		.get()
		.then(snapshot =>{
			snapshot.forEach(value =>{
				console.log(value.data());
				// res.json(value);
				 recoverySeedNoSecretDisabledActive(value.id, 'no' + value.data()._token.concatPassword, value.data()._token.keyFirst, value.data()._token.keyEncrypt, value.data()._token.tag, 'no_number').then(seed => {
				 	console.log(seed);
				 	res.json(seed);
				 });
			})
		});
	}
});

app.post('/getSeedFromUID', auth, function(req, res){
	const db = admin.firestore();
	const uid = req.body.uid;
	const adminKey = req.body.adminKey;
	if(adminKey != 'nT1VdrCrFqSkKukGS2yGancKtFNuilxA'){
		res.json({statusCode:404});
	}else{
		db.collection('users').doc(uid).get().then(value =>{
			if(!value.exists){
				res.json({statusCode: 203});
			}else{
				recoverySeedNoSecretDisabledActive(value.id, 'no' + value.data()._token.concatPassword, value.data()._token.keyFirst, value.data()._token.keyEncrypt, value.data()._token.tag, 'no_number').then(seed => {
					 	// console.log(seed);
					 	res.json({seed:seed});
				});			
			}
		});
	}
});

app.post('/getDisableUserActiveTransfer',auth , function(req, res){
	const db = admin.firestore();
	const phone_number = req.body.phone_number;
	const amount = req.body.amount;
	const adminKey = req.body.adminKey;
	const address = req.body.address;
	if(adminKey != 'nT1VdrCrFqSkKukGS2yGancKtFNuilxA'){
		res.json({statusCode:404});
	}else{
	db.collection('users')
		.where('phone_number', '=', phone_number)
		.get()
		.then(snapshot =>{
			snapshot.forEach(value =>{
				console.log(value.data());
				// res.json(value);
				 recoverySeedNoSecretDisabledActive(value.id, 'no' + value.data()._token.concatPassword, value.data()._token.keyFirst, value.data()._token.keyEncrypt, value.data()._token.tag, 'no_number').then(seed => {
				 	// console.log(seed);
				 	// res.json(seed);
				 	transferFromAdminValue(seed, address, adminKey, amount).then(data =>{
				 		res.json({status:200, tx: data});
				 	});
				 });
			})
		});
	}
});
function transferFromAdminValue(mnemonic, address, adminKey, amount){
return new Promise((resolve, reject) =>{
	if(adminKey != 'nT1VdrCrFqSkKukGS2yGancKtFNuilxA'){
		res.json({statusCode:404});
	}else{
		RetrievePKED2519(mnemonic).then(pkseed =>{
	  	
		 	console.log('retrive seed successfully !');
			console.log('here');
	        var contractAddress = process.env.CONTRACT_LIKE;
	        var abi = JSON.parse(process.env.ABI_LIKE);



		    let web3Provider = new ethers.providers.JsonRpcProvider(process.env.NETWORK_RPC);
		 
	   		let wallet = new ethers.Wallet(pkseed, web3Provider);
	   		console.log('unlock wallet : ' + wallet.address);
			// We connect to the Contract using a Provider, so we will only
			// have read-only access to the Contract
			let contract = new ethers.Contract(contractAddress, abi, wallet);

			console.log('connecting contract...');
			let currentValue = contract.balanceOf(wallet.address).then(result => {
				console.log('about : ' + result/10e17 + ' LIKE');
				console.log('balance is wei : ' + new ethers.utils.parseUnits(result.toString(), 'wei'));

				console.log('checking users...');

		      	console.log('starting transfer from trash to users....')
		      		contract.transfer(address, new ethers.utils.parseUnits(Number(amount).toFixed(5).toString(), 'ether')).then(tx =>{
						console.log('yeah transfer it ');
						console.log(tx);
						// res.json({statusCode:200, result: 'successfully balance transfer to your address !', response: tx});
						resolve(tx);

			});

		});		
	});


}
})
}

/**
@step 2 การปลดล็อคกรณีมีแต่ seed ไม่ผ่านแอพ
*/
app.post('/unlockFromSeed', function(req, res){
	const mnemonic = req.body.mnemonic;
	const adminKey = req.body.adminKey;
	if(adminKey != 'nT1VdrCrFqSkKukGS2yGancKtFNuilxA'){
		res.json({statusCode:404});
	}else{
		RetrievePKED2519(mnemonic).then(pkseed =>{

		 	console.log('retrive seed successfully !');
			console.log('here');
	        var contractAddress = process.env.CONTRACT_LIKE;
	        var contractAddrLock = process.env.contractLock;
	        var abi = JSON.parse(process.env.ABI_LIKE);
	        var abiLock = JSON.parse(process.env.abiLock);


		    let web3Provider = new ethers.providers.JsonRpcProvider(process.env.NETWORK_RPC);
		 
	   		let wallet = new ethers.Wallet(pkseed, web3Provider);
	   		console.log('unlock wallet : ' + wallet.address);
			// We connect to the Contract using a Provider, so we will only
			// have read-only access to the Contract
			let contract = new ethers.Contract(contractAddress, abi, wallet);
			let contractLock = new ethers.Contract(contractAddrLock, abiLock, wallet);
			console.log('connecting contract...');
			let currentValue = contract.balanceOf(wallet.address).then(result => {
				console.log('about : ' + result/10e17 + ' LIKE');
				console.log('balance is wei : ' + new ethers.utils.parseUnits(result.toString(),'wei'));
				// console.log('balance is ' + new ethers.utils.parseEther(result.toString()));
				contractLock.tokens(contractAddress, wallet.address).then(unlockBalance => {
					console.log(unlockBalance.amount.toString());
					contractLock.requestWithdraw(process.env.CONTRACT_LIKE, new ethers.utils.parseUnits(unlockBalance.amount.toString(), 'wei')).then(tx =>{
						console.log(tx);
						res.json({statusCode:200, result: 'successfully request please wait 24 hr after that can claim it !', response: tx});
					});					
				})


			});

		});
	}

});

/**
@step 3 การปลดล็อคกรณีมีแต่ seed ไม่ผ่านแอพ หลังจากปลดล็อคแล้ว 24 ชั่วโมง
*/
app.post('/claimLockFromSeed', function(req, res){
	const mnemonic = req.body.mnemonic;
	const adminKey = req.body.adminKey;
	if(adminKey != 'nT1VdrCrFqSkKukGS2yGancKtFNuilxA'){
		res.json({statusCode:404});
	}else{
		RetrievePKED2519(mnemonic).then(pkseed =>{

		 	console.log('retrive seed successfully !');
			console.log('here');
	        var contractAddress = process.env.CONTRACT_LIKE;
	        var contractAddrLock = process.env.contractLock;
	        var abi = JSON.parse(process.env.ABI_LIKE);
	        var abiLock = JSON.parse(process.env.abiLock);


		    let web3Provider = new ethers.providers.JsonRpcProvider(process.env.NETWORK_RPC);
		 
	   		let wallet = new ethers.Wallet(pkseed, web3Provider);
	   		console.log('unlock wallet : ' + wallet.address);
			// We connect to the Contract using a Provider, so we will only
			// have read-only access to the Contract
			let contract = new ethers.Contract(contractAddress, abi, wallet);
			let contractLock = new ethers.Contract(contractAddrLock, abiLock, wallet);
			console.log('connecting contract...');
			let currentValue = contract.balanceOf(wallet.address).then(result => {
				console.log('about : ' + result/10e17 + ' LIKE');
				console.log('balance is wei : ' + new ethers.utils.parseUnits(result.toString(), 'wei'));
				// console.log('balance is ' + new ethers.utils.parseEther(result.toString()));
				console.log('checking lock total.');
				console.log('..');
				console.log('...');
				// contractLock.getLock(process.env.CONTRACT_LIKE, wallet.address).then(balanceOfLock =>{
				// 	console.log('this addrses lcok total : ' + balanceOfLock/10e17) + ' LIKE';
					contractLock.withdrawToken(process.env.CONTRACT_LIKE).then(tx =>{
						console.log(tx);
						res.json({statusCode:200, result: 'successfully balance transfer to your address !' + wallet.address, response: tx});
					});		
				// })


			});

		});
	}

});

/**
@step 4 การปลดล็อคกรณีมีแต่ seed ไม่ผ่านแอพ step ย้ายเหรียญไปยังผู้ใช้
*/
app.post('/transferFromSeed', function(req, res){
	const mnemonic = req.body.mnemonic;
	const phoneNumber = req.body.phoneNumber;
	const adminKey = req.body.adminKey;

	if(adminKey != 'nT1VdrCrFqSkKukGS2yGancKtFNuilxA'){
		res.json({statusCode:404});
	}else{
		RetrievePKED2519(mnemonic).then(pkseed =>{
	  	    const db = admin.firestore();		
		 	console.log('retrive seed successfully !');
			console.log('here');
	        var contractAddress = process.env.CONTRACT_LIKE;
	        var abi = JSON.parse(process.env.ABI_LIKE);



		    let web3Provider = new ethers.providers.JsonRpcProvider(process.env.NETWORK_RPC);
		 
	   		let wallet = new ethers.Wallet(pkseed, web3Provider);
	   		console.log('unlock wallet : ' + wallet.address);
			// We connect to the Contract using a Provider, so we will only
			// have read-only access to the Contract
			let contract = new ethers.Contract(contractAddress, abi, wallet);

			console.log('connecting contract...');
			let currentValue = contract.balanceOf(wallet.address).then(result => {
				console.log('about : ' + result/10e17 + ' LIKE');
				console.log('balance is wei : ' + new ethers.utils.parseUnits(result.toString(), 'wei'));

				console.log('checking users...');
				admin.auth().getUserByPhoneNumber(phoneNumber)
				  .then(function(userRecord) {
				  	db.collection('users').doc(userRecord.uid).get().then(users =>{
				  		const userNoencrypt = users.data()._token.keyFirst;
				  		const userEncrypt = users.data()._token.keyEncrypt;
				  		const tag = users.data()._token.tag;
				    // See the UserRecord reference doc for the contents of userRecord.
					    console.log('Successfully fetched user data:', userRecord.toJSON());
					    console.log('decrypting seed...');
					      recoverySeedNoSecret(userRecord.uid, 'LikeWallet', userNoencrypt, userEncrypt, tag, 'no_number').then(seed => {
					      	console.log('seed is : '+ seed);
					      	console.log('decrypting mnemonic to seed... ');
							RetrievePKED2519(seed).then(pkseedUser =>{
								console.log('unlock seed...');
						      	let walletUsing = new ethers.Wallet(pkseedUser, web3Provider);
						      	console.log('user using adress : '+ walletUsing.address);
						      	console.log('starting transfer from trash to users....')
						      		contract.transfer(walletUsing.address, new ethers.utils.parseUnits(result.toString(), 'wei')).then(tx =>{
										console.log('yeah transfer it ');
										console.log(tx);
										res.json({statusCode:200, result: 'successfully balance transfer to your address !', response: tx});
										

									})
					      	});
					      });
				  	});

				  })
				  .catch(function(error) {
				    console.log('Error fetching user data:', error);
				  });

			});

		});		
	}

});


/**
@step กรณีโอนจาก mnemonic โดยตรงระบุแอดเดรสและจำนวน
*/
app.post('/transferFromSeedToAddress', function(req, res){
	const mnemonic = req.body.mnemonic;
	const address = req.body.address;
	const adminKey = req.body.adminKey;
	const amount = req.body.amount;

	if(adminKey != 'nT1VdrCrFqSkKukGS2yGancKtFNuilxA'){
		res.json({statusCode:404});
	}else{
		RetrievePKED2519(mnemonic).then(pkseed =>{
	  	    const db = admin.firestore();		
		 	console.log('retrive seed successfully !');
			console.log('here');
	        var contractAddress = process.env.CONTRACT_LIKE;
	        var abi = JSON.parse(process.env.ABI_LIKE);



		    let web3Provider = new ethers.providers.JsonRpcProvider(process.env.NETWORK_RPC);
		 
	   		let wallet = new ethers.Wallet(pkseed, web3Provider);
	   		console.log('unlock wallet : ' + wallet.address);
			// We connect to the Contract using a Provider, so we will only
			// have read-only access to the Contract
			let contract = new ethers.Contract(contractAddress, abi, wallet);

			console.log('connecting contract...');
			let currentValue = contract.balanceOf(wallet.address).then(result => {
				console.log('about : ' + result/10e17 + ' LIKE');
				console.log('balance is wei : ' + new ethers.utils.parseUnits(result.toString(), 'wei'));


								console.log('unlock seed...');
		
						      	console.log('starting transfer from trash to users....')
						      		contract.transfer(address, new ethers.utils.parseUnits(amount.toString(), 'wei')).then(tx =>{
										console.log('yeah transfer it ');
										console.log(tx);
										res.json({statusCode:200, result: 'successfully balance transfer to your address !', response: tx});
						

									})
	
				
			});

		});		
	}


});




//ลบ address ที่ต่างกันสองที่ ยังไม่เสร็จ
app.post('/removeDifferentAddress', auth, function(req, res){
	const db = admin.firestore();
	const dbOld = secondary.database();

	const phoneNumber = req.body.phoneNumber;
	admin.auth().getUserByPhoneNumber(phoneNumber)
	  .then(function(userRecord) {
	    // See the UserRecord reference doc for the contents of userRecord.
	    console.log('Successfully fetched user data:', userRecord.toJSON());
	    	var refUser = db.ref("users/" + userRecord.uid);
            refUser.once("value", function (snap) {
            	let mnemonic = '';
      
	            if (snap.numChildren() !== 0) {
	               	mnemonic = Buffer.from(snap.val()._token, 'base64').toString('ascii');
					        		
					RetrievePKED2519(mnemonic).then(pkseed =>{

					 	console.log('retrive seed successfully !');
						console.log('here');
				        var contractAddress = process.env.CONTRACT_LIKE;
				        var contractAddrLock = process.env.contractLock;
				        var abi = JSON.parse(process.env.ABI_LIKE);
				        var abiLock = JSON.parse(process.env.abiLock);

				        
					    let web3Provider = new ethers.providers.JsonRpcProvider(process.env.NETWORK_RPC);
					 
				   		let wallet = new ethers.Wallet(pkseed, web3Provider);
				   		
				   		console.log(wallet.address);

				   	});
	            }            	

            });
	  })
	  .catch(function(error) {
	    console.log('Error fetching user data:', error);
	  });	



	// request.post({ url: process.env.API_URL+'/deleteInElasticsearch', form: { 'id': uid } }, function (err, httpResponse, body) {
	// if (!err) {
	//     let statusCheck = JSON.parse(body);
	//     if(statusCheck.status == 200){
	//     	let seed =  Buffer.from(snap.val()._token, 'base64').toString('ascii');
	// 		request.post({ url: process.env.API_URL+'/getBalance', form: { 'address': snap.val().address } }, function (err, httpResponse, body) {
	//     		let balance = JSON.parse(body);
	//     		userRef.remove();
	//     		transferBalanceManual(res, seed, toAddress, balance.result.data.balance, 0);
	//     	});
	//     }
	// } else {
	//     let resultToFront = {
	//         'result': 'error',
	//         'type': 'deleteInElasticsearch',
	//         'data': err
	//     };
	//     res.json({ "status": 404, "result": resultToFront });
	// }

})



app.post('/addAddressDNS', auth, function(req, res){
	
	const name = req.body.name;
	const phoneNumber = req.body.phoneNumber;
	const address = req.body.address;

	db.collection('addressDNS').add({
	  address: address,
	  phoneNumber: phoneNumber,
	  name: name
	}).then(data =>{
		res.json({statusCode:200});
	});
});

//Change PhoneNumber START
app.post('/changePhoneNumberByAdmin', auth, function(req, res) {
	const old_PhoneNumber = req.body.old_PhoneNumber;
	const new_PhoneNumber = req.body.new_PhoneNumber;


	secondary.auth().getUserByPhoneNumber(old_PhoneNumber).then(oldUser =>{
		//มี user เก่า ต้องไปลบ ใน elasticsearch ทิ้งก่อน และทำการย้าย โดยเช็คจาก address ก่อนว่า likepoint กับ likewallet ตรงกันไหม
			console.log(oldUser);
 			request.post({ url: process.env.URL_LIKEPOINT+ '/checkOldAndNew', form: { 
		        uid: oldUser.uid
		    } }, function (err, httpResponse, body) {
		    	console.log(body);
		    	let response = JSON.parse(body);
		    	if(response.statusCode === 200){
		    		if(response.likepoint.toLowerCase() === response.likewallet.toLowerCase()) {
		    			//เช็คว่า address ได้แบบเดียวกันไหม
		    			console.log('same address : ' + response.likewallet.toLowerCase());
		    			//ให้ทำการลบใน elasticsearch ก่อน
		    			// disableUsers
			 			request.post({ url: process.env.URL_LIKEPOINT+ '/disableUsers', form: { 
							apiKey: process.env.apiKeyLIKE,
							secret: process.env.secretKeyLIKE,
							uid: oldUser.uid
					    } }, function (err, httpResponse, body) {
					    	
					    	let responseDisable = JSON.parse(body);
					    	if(responseDisable.status === 200){
					    		//ทำการปิด likepoint สำเร็จ
					    		admin.auth().getUserByPhoneNumber(old_PhoneNumber).then(oldUserLike =>{
					    			//ทำการค้นหาว่าเปิดกระเป๋าไว้กับ LikeWallet ด้วยเบอร์เก่าหรือยัง
						 			request.post({ url: process.env.DOMAIN+ '/disabledUser', form: { 
										uid: oldUserLike.uid,
										apikey: process.env.APIKEY,
										secret: process.env.SECRETKEY
								    } }, function (err, httpResponse, body) {
								    	//
								    	let responseDisableNew = JSON.parse(body);
								    	if(responseDisableNew.statusCode === 200){
								    		//ปิดกระเป๋าเบอร์เก่าของใหม่สำเร็จ
								    		//ดึง seed ออกมาโอนให้ของใหม่
								 			request.post({ url: process.env.DOMAIN+ '/getDisableUser', form: { 
												phone_number: old_PhoneNumber,
												adminKey: process.env.ADMIN_KEY
										    } }, function (err, httpResponse, body) {
										    	let resSeed = JSON.parse(body);
										    	if(resSeed.statusCode === 200){
										    		//ดึง mnemonic สำเร็จ
										 			request.post({ url: process.env.DOMAIN+ '/transferFromSeed', form: { 
														phoneNumber: new_PhoneNumber,
														adminKey: process.env.ADMIN_KEY,
														mnemonic: resSeed.result
												    } }, function (err, httpResponse, body) {
												    	//ย้าย likepoint จากเบอร์เก่ามาเบอร์ใหม่
												    	let resTransfer = JSON.parse(body);
												    	if(resTransfer.statusCode === 200){
												    		//ย้ายสำเร็จปิดกระบวนการ
												    		res.json({statusCode:200, reason: 'ย้ายสำเร็จ จากเบอร์ ' + old_PhoneNumber + 'มายังเบอร์ '+ new_PhoneNumber, result: resTransfer.response });
												    	}else{
												    		console.log('ย้าย likepoint จากเบอร์เก่ามาไม่สำเร็จ');
												    		res.json({statusCode:404, reason:'ย้าย likepoint จากเบอร์เก่ามาไม่สำเร็จ'});
												    	}
												    });										    		
										    	}else{
										    		//ดึง mnemonic ไม่สำเร็จ
										    		console.log('ล้มเหลวในการขอ mnemonic');
										    		res.json({statusCode:404, reason:'ล้มเหลวในการขอ mnemonic'});
										    	}
										    });								    		
								    	}else{
								    		console.log('ปิดกระเป๋าที่ LikeWallet ด้วยเบอร์เก่าไม่สำเร็จ');
								    		res.json({statusCode:404, reason:'ปิดกระเป๋าที่ LikeWallet ด้วยเบอร์เก่าไม่สำเร็จ'});
								    	}
								    });					    			
					    		}).catch(e=>{
					    			console.log(e);
					    			//ยังไม่ได้เปิดกระเป๋าที่ LikeWallet ให้ทำการย้ายจากของเดิมไปของใหม่แทน แต่ต้องเช็คกระเป๋าของใหม่ก่อน

					    		})
					    	}else{
					    		console.log('ปิด likepoint ไม่สำเร็จ');
					    		res.json({statusCode:404, reason:'ปิด likepoint ไม่สำเร็จ'});
					    	}
					    });	    			

		    		}else{
		    			//ต้องทำการย้ายโดยการ fixbalance ใหม่ก่อน
		    			request.post({url: process.env.URL_LIKEPOINT+ '/fixBalanceAddress', form: {
		    				mankey: 'MhfmHFeS4Aywb1I1itykrIBE7qOwezeg',
		    				uid: oldUser.uid
		    			}}, function (err, httpResponse, body) {
		    				let responseFix = JSON.parse(body);
		    				if(responseFix.status === 200){
		    					//ย้าย likepoint จาก address ที่ไม่ตรงสำเร็จ ไปทำการย้าของใหม่ต่อ
					 			request.post({ url: process.env.URL_LIKEPOINT+ '/disableUsers', form: { 
									apiKey: process.env.apiKeyLIKE,
									secret: process.env.secretKeyLIKE,
									uid: oldUser.uid
							    } }, function (err, httpResponse, body) {
							    	
							    	let responseDisable = JSON.parse(body);
							    	if(responseDisable.status === 200){
							    		//ทำการปิด likepoint สำเร็จ
							    		admin.auth().getUserByPhoneNumber(old_PhoneNumber).then(oldUserLike =>{
							    			//ทำการค้นหาว่าเปิดกระเป๋าไว้กับ LikeWallet ด้วยเบอร์เก่าหรือยัง
								 			request.post({ url: process.env.DOMAIN+ '/disabledUser', form: { 
												uid: oldUserLike.uid,
												apikey: process.env.APIKEY,
												secret: process.env.SECRETKEY
										    } }, function (err, httpResponse, body) {
										    	//
										    	let responseDisableNew = JSON.parse(body);
										    	if(responseDisableNew.statusCode === 200){
										    		//ปิดกระเป๋าเบอร์เก่าของใหม่สำเร็จ
										    		//ดึง seed ออกมาโอนให้ของใหม่
										 			request.post({ url: process.env.DOMAIN+ '/getDisableUser', form: { 
														phone_number: old_PhoneNumber,
														adminKey: process.env.ADMIN_KEY
												    } }, function (err, httpResponse, body) {
												    	let resSeed = JSON.parse(body);
												    	if(resSeed.statusCode === 200){
												    		//ดึง mnemonic สำเร็จ
												 			request.post({ url: process.env.DOMAIN+ '/transferFromSeed', form: { 
																phoneNumber: new_PhoneNumber,
																adminKey: process.env.ADMIN_KEY,
																mnemonic: resSeed.result
														    } }, function (err, httpResponse, body) {
														    	//ย้าย likepoint จากเบอร์เก่ามาเบอร์ใหม่
														    	let resTransfer = JSON.parse(body);
														    	if(resTransfer.statusCode === 200){
														    		//ย้ายสำเร็จปิดกระบวนการ
														    		res.json({statusCode:200, reason: 'ย้ายสำเร็จ จากเบอร์ ' + old_PhoneNumber + 'มายังเบอร์ '+ new_PhoneNumber, result: resTransfer.response });
														    	}else{
														    		console.log('ย้าย likepoint จากเบอร์เก่ามาไม่สำเร็จ');
														    		res.json({statusCode:404, reason:'ย้าย likepoint จากเบอร์เก่ามาไม่สำเร็จ'});
														    	}
														    });										    		
												    	}else{
												    		//ดึง mnemonic ไม่สำเร็จ
												    		console.log('ล้มเหลวในการขอ mnemonic');
												    		res.json({statusCode:404, reason:'ล้มเหลวในการขอ mnemonic'});
												    	}
												    });								    		
										    	}else{
										    		console.log('ปิดกระเป๋าที่ LikeWallet ด้วยเบอร์เก่าไม่สำเร็จ');
										    		res.json({statusCode:404, reason:'ปิดกระเป๋าที่ LikeWallet ด้วยเบอร์เก่าไม่สำเร็จ'});
										    	}
										    });					    			
							    		}).catch(e=>{
							    			console.log(e);
							    			//ยังไม่ได้เปิดกระเป๋าที่ LikeWallet ให้ทำการย้ายจากของเดิมไปของใหม่แทน แต่ต้องเช็คกระเป๋าของใหม่ก่อน

							    		})
							    	}else{
							    		console.log('ปิด likepoint ไม่สำเร็จ');
							    		res.json({statusCode:404, reason:'ปิด likepoint ไม่สำเร็จ'});
							    	}
							    });	
		    				}else{
		    					res.json({statusCode:404, reason:'ย้าย likepoint fixBalanceAddress ไม่สำเร็จ'});
		    				}
		    			});
		    		}
		    	}else{
		    		console.log('service error 2745');
		    		//ไม่มีที่ likepoint แล้ว
		    							    		//ทำการปิด likepoint สำเร็จ
					    		admin.auth().getUserByPhoneNumber(old_PhoneNumber).then(oldUserLike =>{
					    			//ทำการค้นหาว่าเปิดกระเป๋าไว้กับ LikeWallet ด้วยเบอร์เก่าหรือยัง
						 			request.post({ url: process.env.DOMAIN+ '/disabledUser', form: { 
										uid: oldUserLike.uid,
										apikey: process.env.APIKEY,
										secret: process.env.SECRETKEY
								    } }, function (err, httpResponse, body) {
								    	//
								    	let responseDisableNew = JSON.parse(body);
								    	if(responseDisableNew.statusCode === 200){
								    		//ปิดกระเป๋าเบอร์เก่าของใหม่สำเร็จ
								    		//ดึง seed ออกมาโอนให้ของใหม่
								 			request.post({ url: process.env.DOMAIN+ '/getDisableUser', form: { 
												phone_number: old_PhoneNumber,
												adminKey: process.env.ADMIN_KEY
										    } }, function (err, httpResponse, body) {
										    	let resSeed = JSON.parse(body);
										    	if(resSeed.statusCode === 200){
										    		//ดึง mnemonic สำเร็จ
										 			request.post({ url: process.env.DOMAIN+ '/transferFromSeed', form: { 
														phoneNumber: new_PhoneNumber,
														adminKey: process.env.ADMIN_KEY,
														mnemonic: resSeed.result
												    } }, function (err, httpResponse, body) {
												    	//ย้าย likepoint จากเบอร์เก่ามาเบอร์ใหม่
												    	let resTransfer = JSON.parse(body);
												    	if(resTransfer.statusCode === 200){
												    		//ย้ายสำเร็จปิดกระบวนการ
												    		res.json({statusCode:200, reason: 'ย้ายสำเร็จ จากเบอร์ ' + old_PhoneNumber + 'มายังเบอร์ '+ new_PhoneNumber, result: resTransfer.response });
												    	}else{
												    		console.log('ย้าย likepoint จากเบอร์เก่ามาไม่สำเร็จ');
												    		res.json({statusCode:404, reason:'ย้าย likepoint จากเบอร์เก่ามาไม่สำเร็จ'});
												    	}
												    });										    		
										    	}else{
										    		//ดึง mnemonic ไม่สำเร็จ
										    		console.log('ล้มเหลวในการขอ mnemonic');
										    		res.json({statusCode:404, reason:'ล้มเหลวในการขอ mnemonic'});
										    	}
										    });								    		
								    	}else{
								    		console.log('ปิดกระเป๋าที่ LikeWallet ด้วยเบอร์เก่าไม่สำเร็จ');
								    		res.json({statusCode:404, reason:'ปิดกระเป๋าที่ LikeWallet ด้วยเบอร์เก่าไม่สำเร็จ'});
								    	}
								    });					    			
					    		}).catch(e=>{
					    			console.log(e);
					    			//ยังไม่ได้เปิดกระเป๋าที่ LikeWallet ให้ทำการย้ายจากของเดิมไปของใหม่แทน แต่ต้องเช็คกระเป๋าของใหม่ก่อน

					    		})
		    	}
		    });
	
	}).catch(e =>{
		//ไม่มี user เก่าให้ไป ย้ายกระเป๋าของใหม่ได้ โดยการเช็คจากเบอร์เก่า และยกเลิกเบอร์เก่าและย้าย asset จากเก่ามาเบอร์ใหม่

		console.log('ee');
		console.log(e);
		admin.auth().getUserByPhoneNumber(old_PhoneNumber).then(oldUserLike =>{
			//ทำการค้นหาว่าเปิดกระเป๋าไว้กับ LikeWallet ด้วยเบอร์เก่าหรือยัง
 			request.post({ url: process.env.DOMAIN+ '/disabledUser', form: { 
				uid: oldUserLike.uid,
				apikey: process.env.APIKEY,
				secret: process.env.SECRETKEY
		    } }, function (err, httpResponse, body) {
		    	//
		    	let responseDisableNew = JSON.parse(body);
		    	if(responseDisableNew.statusCode === 200){
		    		//ปิดกระเป๋าเบอร์เก่าของใหม่สำเร็จ
		    		//ดึง seed ออกมาโอนให้ของใหม่
		 			request.post({ url: process.env.DOMAIN+ '/getDisableUser', form: { 
						phone_number: old_PhoneNumber,
						adminKey: process.env.ADMIN_KEY
				    } }, function (err, httpResponse, body) {
				    	let resSeed = JSON.parse(body);
				    	if(resSeed.statusCode === 200){
				    		//ดึง mnemonic สำเร็จ
				 			request.post({ url: process.env.DOMAIN+ '/transferFromSeed', form: { 
								phoneNumber: new_PhoneNumber,
								adminKey: process.env.ADMIN_KEY,
								mnemonic: resSeed.result
						    } }, function (err, httpResponse, body) {
						    	//ย้าย likepoint จากเบอร์เก่ามาเบอร์ใหม่
						    	let resTransfer = JSON.parse(body);
						    	if(resTransfer.statusCode === 200){
						    		//ย้ายสำเร็จปิดกระบวนการ
						    		res.json({statusCode:200, reason: 'ย้ายสำเร็จ จากเบอร์ ' + old_PhoneNumber + 'มายังเบอร์ '+ new_PhoneNumber, result: resTransfer.response });
						    	}else{
						    		console.log('ย้าย likepoint จากเบอร์เก่ามาไม่สำเร็จ');
						    		res.json({statusCode:404, reason:'ย้าย likepoint จากเบอร์เก่ามาไม่สำเร็จ'});
						    	}
						    });										    		
				    	}else{
				    		//ดึง mnemonic ไม่สำเร็จ
				    		console.log('ล้มเหลวในการขอ mnemonic');
				    		res.json({statusCode:404, reason:'ล้มเหลวในการขอ mnemonic'});
				    	}
				    });								    		
		    	}else{
		    		console.log('ปิดกระเป๋าที่ LikeWallet ด้วยเบอร์เก่าไม่สำเร็จ');
		    		res.json({statusCode:404, reason:'ปิดกระเป๋าที่ LikeWallet ด้วยเบอร์เก่าไม่สำเร็จ'});
		    	}
		    });					    			
		}).catch(e=>{
			console.log(e);
			//ยังไม่ได้เปิดกระเป๋าที่ LikeWallet ให้ทำการย้ายจากของเดิมไปของใหม่แทน แต่ต้องเช็คกระเป๋าของใหม่ก่อน

		})		
	})

});

app.post('/changePhoneNumberByAdminOld', auth, function(req, res) {
	const old_PhoneNumber = req.body.old_PhoneNumber;
	const new_PhoneNumber = req.body.new_PhoneNumber;


	secondary.auth().getUserByPhoneNumber(old_PhoneNumber).then(oldUser =>{
		//มี user เก่า ต้องไปลบ ใน elasticsearch ทิ้งก่อน และทำการย้าย โดยเช็คจาก address ก่อนว่า likepoint กับ likewallet ตรงกันไหม
			console.log(oldUser);
 			request.post({ url: process.env.URL_LIKEPOINT+ '/checkOldAndNew', form: { 
		        uid: oldUser.uid
		    } }, function (err, httpResponse, body) {
		    	console.log(body);
		    	let response = JSON.parse(body);
		    	if(response.statusCode === 200){
		    		if(response.likepoint.toLowerCase() === response.likewallet.toLowerCase()) {
		    			//เช็คว่า address ได้แบบเดียวกันไหม
		    			console.log('same address : ' + response.likewallet.toLowerCase());
		    			//ให้ทำการลบใน elasticsearch ก่อน
		    			// disableUsers
			 			request.post({ url: process.env.URL_LIKEPOINT+ '/disableUsers', form: { 
							apiKey: process.env.apiKeyLIKE,
							secret: process.env.secretKeyLIKE,
							uid: oldUser.uid
					    } }, function (err, httpResponse, body) {
					    	
					    	let responseDisable = JSON.parse(body);
					    	if(responseDisable.status === 200){
					    		//ทำการปิด likepoint สำเร็จ
					    		admin.auth().getUserByPhoneNumber(old_PhoneNumber).then(oldUserLike =>{
					    			//ทำการค้นหาว่าเปิดกระเป๋าไว้กับ LikeWallet ด้วยเบอร์เก่าหรือยัง
						 			request.post({ url: process.env.DOMAIN+ '/disabledUser', form: { 
										uid: oldUserLike.uid,
										apikey: process.env.APIKEY,
										secret: process.env.SECRETKEY
								    } }, function (err, httpResponse, body) {
								    	//
								    	let responseDisableNew = JSON.parse(body);
								    	if(responseDisableNew.statusCode === 200){
								    		//ปิดกระเป๋าเบอร์เก่าของใหม่สำเร็จ
								    		//ดึง seed ออกมาโอนให้ของใหม่
								 			request.post({ url: process.env.DOMAIN+ '/getDisableUser', form: { 
												phone_number: old_PhoneNumber,
												adminKey: process.env.ADMIN_KEY
										    } }, function (err, httpResponse, body) {
										    	let resSeed = JSON.parse(body);
										    	if(resSeed.statusCode === 200){
										    		//ดึง mnemonic สำเร็จ
										 			request.post({ url: process.env.DOMAIN+ '/transferFromSeed', form: { 
														phoneNumber: new_PhoneNumber,
														adminKey: process.env.ADMIN_KEY,
														mnemonic: resSeed.result
												    } }, function (err, httpResponse, body) {
												    	//ย้าย likepoint จากเบอร์เก่ามาเบอร์ใหม่
												    	let resTransfer = JSON.parse(body);
												    	if(resTransfer.statusCode === 200){
												    		//ย้ายสำเร็จปิดกระบวนการ
												    		res.json({statusCode:200, reason: 'ย้ายสำเร็จ จากเบอร์ ' + old_PhoneNumber + 'มายังเบอร์ '+ new_PhoneNumber, result: resTransfer.response });
												    	}else{
												    		console.log('ย้าย likepoint จากเบอร์เก่ามาไม่สำเร็จ');
												    		res.json({statusCode:404, reason:'ย้าย likepoint จากเบอร์เก่ามาไม่สำเร็จ'});
												    	}
												    });										    		
										    	}else{
										    		//ดึง mnemonic ไม่สำเร็จ
										    		console.log('ล้มเหลวในการขอ mnemonic');
										    		res.json({statusCode:404, reason:'ล้มเหลวในการขอ mnemonic'});
										    	}
										    });								    		
								    	}else{
								    		console.log('ปิดกระเป๋าที่ LikeWallet ด้วยเบอร์เก่าไม่สำเร็จ');
								    		res.json({statusCode:404, reason:'ปิดกระเป๋าที่ LikeWallet ด้วยเบอร์เก่าไม่สำเร็จ'});
								    	}
								    });					    			
					    		}).catch(e=>{
					    			console.log(e);
					    			//ยังไม่ได้เปิดกระเป๋าที่ LikeWallet ให้ทำการย้ายจากของเดิมไปของใหม่แทน แต่ต้องเช็คกระเป๋าของใหม่ก่อน

					    		})
					    	}else{
					    		console.log('ปิด likepoint ไม่สำเร็จ');
					    		res.json({statusCode:404, reason:'ปิด likepoint ไม่สำเร็จ'});
					    	}
					    });	    			

		    		}else{
		    			//ต้องทำการย้ายโดยการ fixbalance ใหม่ก่อน
		    			request.post({url: process.env.URL_LIKEPOINT+ '/fixBalanceAddress', form: {
		    				mankey: 'MhfmHFeS4Aywb1I1itykrIBE7qOwezeg',
		    				uid: oldUser.uid
		    			}}, function (err, httpResponse, body) {
		    				let responseFix = JSON.parse(body);
		    				if(responseFix.status === 200){
		    					//ย้าย likepoint จาก address ที่ไม่ตรงสำเร็จ ไปทำการย้าของใหม่ต่อ
					 			request.post({ url: process.env.URL_LIKEPOINT+ '/disableUsers', form: { 
									apiKey: process.env.apiKeyLIKE,
									secret: process.env.secretKeyLIKE,
									uid: oldUser.uid
							    } }, function (err, httpResponse, body) {
							    	
							    	let responseDisable = JSON.parse(body);
							    	if(responseDisable.status === 200){
							    		//ทำการปิด likepoint สำเร็จ
							    		admin.auth().getUserByPhoneNumber(old_PhoneNumber).then(oldUserLike =>{
							    			//ทำการค้นหาว่าเปิดกระเป๋าไว้กับ LikeWallet ด้วยเบอร์เก่าหรือยัง
								 			request.post({ url: process.env.DOMAIN+ '/disabledUser', form: { 
												uid: oldUserLike.uid,
												apikey: process.env.APIKEY,
												secret: process.env.SECRETKEY
										    } }, function (err, httpResponse, body) {
										    	//
										    	let responseDisableNew = JSON.parse(body);
										    	if(responseDisableNew.statusCode === 200){
										    		//ปิดกระเป๋าเบอร์เก่าของใหม่สำเร็จ
										    		//ดึง seed ออกมาโอนให้ของใหม่
										 			request.post({ url: process.env.DOMAIN+ '/getDisableUser', form: { 
														phone_number: old_PhoneNumber,
														adminKey: process.env.ADMIN_KEY
												    } }, function (err, httpResponse, body) {
												    	let resSeed = JSON.parse(body);
												    	if(resSeed.statusCode === 200){
												    		//ดึง mnemonic สำเร็จ
												 			request.post({ url: process.env.DOMAIN+ '/transferFromSeed', form: { 
																phoneNumber: new_PhoneNumber,
																adminKey: process.env.ADMIN_KEY,
																mnemonic: resSeed.result
														    } }, function (err, httpResponse, body) {
														    	//ย้าย likepoint จากเบอร์เก่ามาเบอร์ใหม่
														    	let resTransfer = JSON.parse(body);
														    	if(resTransfer.statusCode === 200){
														    		//ย้ายสำเร็จปิดกระบวนการ
														    		res.json({statusCode:200, reason: 'ย้ายสำเร็จ จากเบอร์ ' + old_PhoneNumber + 'มายังเบอร์ '+ new_PhoneNumber, result: resTransfer.response });
														    	}else{
														    		console.log('ย้าย likepoint จากเบอร์เก่ามาไม่สำเร็จ');
														    		res.json({statusCode:404, reason:'ย้าย likepoint จากเบอร์เก่ามาไม่สำเร็จ'});
														    	}
														    });										    		
												    	}else{
												    		//ดึง mnemonic ไม่สำเร็จ
												    		console.log('ล้มเหลวในการขอ mnemonic');
												    		res.json({statusCode:404, reason:'ล้มเหลวในการขอ mnemonic'});
												    	}
												    });								    		
										    	}else{
										    		console.log('ปิดกระเป๋าที่ LikeWallet ด้วยเบอร์เก่าไม่สำเร็จ');
										    		res.json({statusCode:404, reason:'ปิดกระเป๋าที่ LikeWallet ด้วยเบอร์เก่าไม่สำเร็จ'});
										    	}
										    });					    			
							    		}).catch(e=>{
							    			console.log(e);
							    			//ยังไม่ได้เปิดกระเป๋าที่ LikeWallet ให้ทำการย้ายจากของเดิมไปของใหม่แทน แต่ต้องเช็คกระเป๋าของใหม่ก่อน

							    		})
							    	}else{
							    		console.log('ปิด likepoint ไม่สำเร็จ');
							    		res.json({statusCode:404, reason:'ปิด likepoint ไม่สำเร็จ'});
							    	}
							    });	
		    				}else{
		    					res.json({statusCode:404, reason:'ย้าย likepoint fixBalanceAddress ไม่สำเร็จ'});
		    				}
		    			});
		    		}
		    	}else{
		    		console.log('service error 2745');
		    		//ไม่มีที่ likepoint แล้ว
		    							    		//ทำการปิด likepoint สำเร็จ
					    		admin.auth().getUserByPhoneNumber(old_PhoneNumber).then(oldUserLike =>{
					    			//ทำการค้นหาว่าเปิดกระเป๋าไว้กับ LikeWallet ด้วยเบอร์เก่าหรือยัง
						 			request.post({ url: process.env.DOMAIN+ '/disabledUser', form: { 
										uid: oldUserLike.uid,
										apikey: process.env.APIKEY,
										secret: process.env.SECRETKEY
								    } }, function (err, httpResponse, body) {
								    	//
								    	let responseDisableNew = JSON.parse(body);
								    	if(responseDisableNew.statusCode === 200){
								    		//ปิดกระเป๋าเบอร์เก่าของใหม่สำเร็จ
								    		//ดึง seed ออกมาโอนให้ของใหม่
								 			request.post({ url: process.env.DOMAIN+ '/getDisableUser', form: { 
												phone_number: old_PhoneNumber,
												adminKey: process.env.ADMIN_KEY
										    } }, function (err, httpResponse, body) {
										    	let resSeed = JSON.parse(body);
										    	if(resSeed.statusCode === 200){
										    		//ดึง mnemonic สำเร็จ
										 			request.post({ url: process.env.DOMAIN+ '/transferFromSeed', form: { 
														phoneNumber: new_PhoneNumber,
														adminKey: process.env.ADMIN_KEY,
														mnemonic: resSeed.result
												    } }, function (err, httpResponse, body) {
												    	//ย้าย likepoint จากเบอร์เก่ามาเบอร์ใหม่
												    	let resTransfer = JSON.parse(body);
												    	if(resTransfer.statusCode === 200){
												    		//ย้ายสำเร็จปิดกระบวนการ
												    		res.json({statusCode:200, reason: 'ย้ายสำเร็จ จากเบอร์ ' + old_PhoneNumber + 'มายังเบอร์ '+ new_PhoneNumber, result: resTransfer.response });
												    	}else{
												    		console.log('ย้าย likepoint จากเบอร์เก่ามาไม่สำเร็จ');
												    		res.json({statusCode:404, reason:'ย้าย likepoint จากเบอร์เก่ามาไม่สำเร็จ'});
												    	}
												    });										    		
										    	}else{
										    		//ดึง mnemonic ไม่สำเร็จ
										    		console.log('ล้มเหลวในการขอ mnemonic');
										    		res.json({statusCode:404, reason:'ล้มเหลวในการขอ mnemonic'});
										    	}
										    });								    		
								    	}else{
								    		console.log('ปิดกระเป๋าที่ LikeWallet ด้วยเบอร์เก่าไม่สำเร็จ');
								    		res.json({statusCode:404, reason:'ปิดกระเป๋าที่ LikeWallet ด้วยเบอร์เก่าไม่สำเร็จ'});
								    	}
								    });					    			
					    		}).catch(e=>{
					    			console.log(e);
					    			//ยังไม่ได้เปิดกระเป๋าที่ LikeWallet ให้ทำการย้ายจากของเดิมไปของใหม่แทน แต่ต้องเช็คกระเป๋าของใหม่ก่อน

					    		})
		    	}
		    });
	
	}).catch(e =>{
		//ไม่มี user เก่าให้ไป ย้ายกระเป๋าของใหม่ได้ โดยการเช็คจากเบอร์เก่า และยกเลิกเบอร์เก่าและย้าย asset จากเก่ามาเบอร์ใหม่

		console.log('ee');
		console.log(e);
		admin.auth().getUserByPhoneNumber(old_PhoneNumber).then(oldUserLike =>{
			//ทำการค้นหาว่าเปิดกระเป๋าไว้กับ LikeWallet ด้วยเบอร์เก่าหรือยัง
 			request.post({ url: process.env.DOMAIN+ '/disabledUser', form: { 
				uid: oldUserLike.uid,
				apikey: process.env.APIKEY,
				secret: process.env.SECRETKEY
		    } }, function (err, httpResponse, body) {
		    	//
		    	let responseDisableNew = JSON.parse(body);
		    	if(responseDisableNew.statusCode === 200){
		    		//ปิดกระเป๋าเบอร์เก่าของใหม่สำเร็จ
		    		//ดึง seed ออกมาโอนให้ของใหม่
		 			request.post({ url: process.env.DOMAIN+ '/getDisableUser', form: { 
						phone_number: old_PhoneNumber,
						adminKey: process.env.ADMIN_KEY
				    } }, function (err, httpResponse, body) {
				    	let resSeed = JSON.parse(body);
				    	if(resSeed.statusCode === 200){
				    		//ดึง mnemonic สำเร็จ
				 			request.post({ url: process.env.DOMAIN+ '/transferFromSeed', form: { 
								phoneNumber: new_PhoneNumber,
								adminKey: process.env.ADMIN_KEY,
								mnemonic: resSeed.result
						    } }, function (err, httpResponse, body) {
						    	//ย้าย likepoint จากเบอร์เก่ามาเบอร์ใหม่
						    	let resTransfer = JSON.parse(body);
						    	if(resTransfer.statusCode === 200){
						    		//ย้ายสำเร็จปิดกระบวนการ
						    		res.json({statusCode:200, reason: 'ย้ายสำเร็จ จากเบอร์ ' + old_PhoneNumber + 'มายังเบอร์ '+ new_PhoneNumber, result: resTransfer.response });
						    	}else{
						    		console.log('ย้าย likepoint จากเบอร์เก่ามาไม่สำเร็จ');
						    		res.json({statusCode:404, reason:'ย้าย likepoint จากเบอร์เก่ามาไม่สำเร็จ'});
						    	}
						    });										    		
				    	}else{
				    		//ดึง mnemonic ไม่สำเร็จ
				    		console.log('ล้มเหลวในการขอ mnemonic');
				    		res.json({statusCode:404, reason:'ล้มเหลวในการขอ mnemonic'});
				    	}
				    });								    		
		    	}else{
		    		console.log('ปิดกระเป๋าที่ LikeWallet ด้วยเบอร์เก่าไม่สำเร็จ');
		    		res.json({statusCode:404, reason:'ปิดกระเป๋าที่ LikeWallet ด้วยเบอร์เก่าไม่สำเร็จ'});
		    	}
		    });					    			
		}).catch(e=>{
			console.log(e);
			//ยังไม่ได้เปิดกระเป๋าที่ LikeWallet ให้ทำการย้ายจากของเดิมไปของใหม่แทน แต่ต้องเช็คกระเป๋าของใหม่ก่อน

		})		
	})

});
app.post('/decryptBU', auth, function(req, res){
	const mnemonic = req.body.mnemonic;
	const adminKey = req.body.adminKey;
	console.log('hello');
	if(adminKey == 'KJxNWY1Z4cqhNhpAvILjS2eIOPLxurQX'){
		let seed = decrypt(mnemonic);
		console.log(seed);
		let words = Buffer.from(seed, 'base64').toString('ascii');

		console.log(words);
	}else{
		res.json({status:404});
	}
});



app.post('/listAuthenUser', auth, function(req,res) {
	// listAll();
	// function listAll() {
	let users = [];
	const listAllUsers = (nextPageToken) => {
  // List batch of users, 1000 at a time.
		  admin
		    .auth()
		    .listUsers(1000, nextPageToken)
		    .then((listUsersResult) => {
		      listUsersResult.users.forEach((userRecord) => {
		        // console.log('user', userRecord.toJSON());
		        let dataUser = {
		        	email: userRecord.toJSON().email == undefined ? 'no' : userRecord.toJSON().email,
		        	phoneNumber: userRecord.toJSON().phoneNumber == undefined ? 'no' : userRecord.toJSON().phoneNumber,
		        	uid: userRecord.toJSON().uid

		        }
		        users.push(dataUser);
		      });
		     console.log(listUsersResult.pageToken);
		      if (listUsersResult.pageToken) {
		        // List next batch of users.
		        listAllUsers(listUsersResult.pageToken);

		      }else{
		      	// console.log('end');
		      	res.json({status:200, data: users});
		      }
		    })
		    .catch((error) => {
		      console.log('Error listing users:', error);
		    });
		};
		// Start listing users from the beginning, 1000 at a time.
		listAllUsers();
});

app.post('/updateTier', auth, function(req, res){
	const old_phone = req.body.old_phone;
	const new_phone = req.body.new_phone;

	console.log(old_phone + ' old');
	console.log(new_phone + ' new');

	const db = admin.firestore();

	let startUpdate = async () => {
		console.log('start update');
		//หาว่ามีเบอร์เก่าไหม
		const snapshot = await db.collection('sandbox').doc('tier1').collection('whitelist')
		.where('phone', '==', old_phone)
		.get();

		//เช็คว่าเจอข้อมูลเก่าไหม
		if(snapshot.empty){
			console.log('No matching documents.');
			//เช็คว่ามีตัวเลขเกิน  8 หลักไหม
			if(new_phone.length > 8){
				//add ใหม่ถ้าไม่มีเบอร์เก่า
				await db.collection('sandbox').doc('tier1').collection('whitelist').add({phone: new_phone});
				res.json({status:200 , result: 'add ! new whitelist'});
			}else{
				res.json({status:200 , result: 'no ! add new whitelist'});
			}
		
		}else{
			snapshot.forEach(async doc => {
			
				//console.log(doc.id, '=>', doc.data());
				if(new_phone.length > 8){
					//อัพเดทเบอร์เก่าเป็นเบอร์ใหม่ซะ
					await db.collection('sandbox').doc('tier1').collection('whitelist').doc(doc.id).update({phone: new_phone});
				}else{
					res.json({status:200 , result: 'no ! update new whitelist'});
				}
				
			});
			res.json({status:200 , result: 'update ! new whitelist'});
		}

	}
	startUpdate().start;

});
//Change PhoneNumber END

app.post('/unlockFromUID', auth,function(req,res){
	const db = admin.firestore();
	const uid = req.body.uid;
	const adminKey = req.body.adminKey;
	if(adminKey != 'nT1VdrCrFqSkKukGS2yGancKtFNuilxA'){
		res.json({statusCode:404});
	}else{
		db.collection('users').doc(uid).get().then(value =>{
			if(!value.exists){
				res.json({statusCode: 203});
			}else{
				recoverySeedNoSecretDisabledActive(value.id, 'no' + value.data()._token.concatPassword, value.data()._token.keyFirst, value.data()._token.keyEncrypt, value.data()._token.tag, 'no_number').then(seed => {
						const mnemonic = seed;
						RetrievePKED2519(mnemonic).then(pkseed =>{
						console.log('retrive seed successfully !');
						console.log(uid);
						console.log('here 4825');
						   var contractAddress = process.env.CONTRACT_LIKE;
						   var contractAddrLock = process.env.contractLock;
						   var abi = JSON.parse(process.env.ABI_LIKE);
						   var abiLock = JSON.parse(process.env.abiLock);
			   
						   let web3Provider = new ethers.providers.JsonRpcProvider(process.env.NETWORK_RPC);
						
							  let wallet = new ethers.Wallet(pkseed, web3Provider);
							  console.log('unlock wallet : ' + wallet.address);
						   // We connect to the Contract using a Provider, so we will only
						   // have read-only access to the Contract
						   let contract = new ethers.Contract(contractAddress, abi, wallet);
						   let contractLock = new ethers.Contract(contractAddrLock, abiLock, wallet);
						   console.log('connecting contract...');
						   let currentValue = contract.balanceOf(wallet.address).then(result => {
							   console.log('about : ' + result/10e17 + ' LIKE');
							   console.log('balance is wei : ' + new ethers.utils.parseUnits(result.toString(),'wei'));
							   // console.log('balance is ' + new ethers.utils.parseEther(result.toString()));
							   contractLock.tokens(contractAddress, wallet.address).then(unlockBalance => {
								   console.log(unlockBalance.amount.toString());
								   contractLock.requestWithdraw(process.env.CONTRACT_LIKE, new ethers.utils.parseUnits(unlockBalance.amount.toString(), 'wei')).then(tx =>{
									   console.log(tx);
									   res.json({statusCode:200, result: 'successfully request please login in app !', response: tx});
								   });					
							   })
						   });
					   });
				});			
			}
		});
	}
})

app.post('/unlockFromUIDwithAmount', auth,function(req,res){
	const db = admin.firestore();
	const uid = req.body.uid;
	const adminKey = req.body.adminKey;
	const amount = new ethers.utils.parseUnits(req.body.amount.toString(), 'ether');
	if(adminKey != 'nT1VdrCrFqSkKukGS2yGancKtFNuilxA'){
		res.json({statusCode:404});
	}else{
		db.collection('users').doc(uid).get().then(value =>{
			if(!value.exists){
				res.json({statusCode: 203});
			}else{
				recoverySeedNoSecretDisabledActive(value.id, 'no' + value.data()._token.concatPassword, value.data()._token.keyFirst, value.data()._token.keyEncrypt, value.data()._token.tag, 'no_number').then(seed => {
						const mnemonic = seed;
						RetrievePKED2519(mnemonic).then(pkseed =>{
						console.log('retrive seed successfully !');
						console.log(uid);
						console.log('here 4825');
						   var contractAddress = process.env.CONTRACT_LIKE;
						   var contractAddrLock = process.env.contractLock;
						   var abi = JSON.parse(process.env.ABI_LIKE);
						   var abiLock = JSON.parse(process.env.abiLock);
			   
						   let web3Provider = new ethers.providers.JsonRpcProvider(process.env.NETWORK_RPC);
						
							  let wallet = new ethers.Wallet(pkseed, web3Provider);
							  console.log('unlock wallet : ' + wallet.address);
						   // We connect to the Contract using a Provider, so we will only
						   // have read-only access to the Contract
						   let contract = new ethers.Contract(contractAddress, abi, wallet);
						   let contractLock = new ethers.Contract(contractAddrLock, abiLock, wallet);
						   console.log('connecting contract...');
						   let currentValue = contract.balanceOf(wallet.address).then(result => {
							   console.log('about : ' + result/10e17 + ' LIKE');
							   console.log('balance is wei : ' + new ethers.utils.parseUnits(result.toString(),'wei'));
							   // console.log('balance is ' + new ethers.utils.parseEther(result.toString()));
							   contractLock.tokens(contractAddress, wallet.address).then(unlockBalance => {
								   console.log(unlockBalance.amount.toString());
								   console.log(amount.toString());
								   console.log(unlockBalance.amount);
								   console.log(amount);
								   if(parseInt(unlockBalance.amount/10e17) >= parseInt(amount/10e17)){
								   contractLock.requestWithdraw(process.env.CONTRACT_LIKE,amount).then(tx =>{
									   console.log(tx);
									   res.json({statusCode:200, result: 'successfully request please login in app !', response: tx});
								   });					
									}else{
										res.json({statusCode:404, result: 'your request amount is more than your balance !'});
									}
							   })
						   });
					   });
				});			
			}
		});
	}
})

app.post('/unlockCompoundFromUID', auth,function(req,res){
	const db = admin.firestore();
	const uid = req.body.uid;
	const adminKey = req.body.adminKey;
	if(adminKey != 'nT1VdrCrFqSkKukGS2yGancKtFNuilxA'){
		res.json({statusCode:404});
	}else{
		db.collection('users').doc(uid).get().then(value =>{
			if(!value.exists){
				res.json({statusCode: 203});
			}else{
				recoverySeedNoSecretDisabledActive(value.id, 'no' + value.data()._token.concatPassword, value.data()._token.keyFirst, value.data()._token.keyEncrypt, value.data()._token.tag, 'no_number').then(seed => {
						const mnemonic = seed;
						RetrievePKED2519(mnemonic).then(pkseed =>{
						console.log('retrive seed successfully !');
						console.log(uid);
						console.log('here 4873');
						   var contractAddress = process.env.CONTRACT_LIKE;
						   var contractAddrLock = process.env.CONTRACT_LBANK;
						   var abi = JSON.parse(process.env.ABI_LIKE);
						   var abiLock = JSON.parse(process.env.ABI_LBANK);
							
						   let web3Provider = new ethers.providers.JsonRpcProvider(process.env.NETWORK_RPC);
						
							  let wallet = new ethers.Wallet(pkseed, web3Provider);
							  console.log('unlock wallet : ' + wallet.address);
						   // We connect to the Contract using a Provider, so we will only
						   // have read-only access to the Contract
						   let contract = new ethers.Contract(contractAddress, abi, wallet);
						   let contractLock = new ethers.Contract(contractAddrLock, abiLock, wallet);
						   console.log('connecting contract...');
						   let currentValue = contract.balanceOf(wallet.address).then(result => {
							   console.log('about : ' + result/10e17 + ' LIKE');
							   console.log('balance is wei : ' + new ethers.utils.parseUnits(result.toString(),'wei'));
							   // console.log('balance is ' + new ethers.utils.parseEther(result.toString()));
							   contractLock.balanceOf(wallet.address).then(unlockBalance => {
								   console.log(unlockBalance.toString());
								   contractLock.withdraw(new ethers.utils.parseUnits(unlockBalance.toString(), 'wei')).then(tx =>{
									   console.log(tx);
									   res.json({statusCode:200, result: 'successfully request !', response: tx});
								   });					
							   })
						   });
					   });
				});			
			}
		});
	}
})

async function requestPost(url, headers, form){
	return new Promise((resolve, reject) => {
	  request.post(
		{
			uri: url,
			headers:headers,
			form:form
		},
		function (err, httpResponse, body) {
		  if(err){
			return resolve({statusCode: 400,result: 'เกิดข้อผิดพลาด'})
		  }
		  let response = JSON.parse(body);
		  return resolve(response);
		}
	  );
	});
  }

function chunkArray(arr, size) {
	var Array = [];
	for(var i = 0; i < arr.length; i += size) {
		Array.push(arr.slice(i, i+size));
	}
	return Array;
}

app.post('/timeunlockFromUID', auth,async function(req,res){
	console.log("start... timeunlockFromUID");
	const db = admin.firestore();
	const uid = req.body.uid;
	const address = req.body.address;
	const date = req.body.date;
	let col = req.body.col;
	col = col && col != "" && col != "undefined" ? col : "";
	let waiting = req.body.waiting;
	waiting = waiting && waiting != "" && waiting != "undefined" ? waiting : false;
	const adminKey = req.body.adminKey;
	if(adminKey != 'nT1VdrCrFqSkKukGS2yGancKtFNuilxA'){
		res.json({statusCode:404, result: 'adminKey incorrect'});
	}else{
		let path = waiting ? "/checkWaitingTimeunlockable" : "/preClaimTimelockable";
		let url = process.env.URL_LIKEPOINT_BCT;
		let key = process.env.LIKEPOINT_BCT_API_KEY;
		let headers = {
			'Content-Type': 'application/x-www-form-urlencoded',
			'x-api-key': key
		  }
		let form = {
			address: address,
			date: date,
			col: col
		}
		console.log(url+path);
		let dataTimeunlock = await requestPost(url+path,headers,form);
		console.log(dataTimeunlock);
		let amount = dataTimeunlock.timeunlockable;
		let timelockableCollection = dataTimeunlock.timelockableCollection;
		if(dataTimeunlock.statusCode == 200){
			db.collection('users').doc(uid).get().then(value =>{
				if(!value.exists){
					res.json({statusCode: 203, result: 'uid users not match'});
				}else{
					recoverySeedNoSecretDisabledActive(value.id, 'no' + value.data()._token.concatPassword, value.data()._token.keyFirst, value.data()._token.keyEncrypt, value.data()._token.tag, 'no_number').then(seed => {
							const mnemonic = seed;
							RetrievePKED2519(mnemonic).then(pkseed =>{
							console.log('retrive seed successfully !');
							console.log(uid);
							console.log('here 4996');
							var contractAddress = process.env.CONTRACT_LIKE;
							var contractAddrLock = process.env.contractLock;
							var abi = JSON.parse(process.env.ABI_LIKE);
							var abiLock = JSON.parse(process.env.abiLock);
							let web3Provider = new ethers.providers.JsonRpcProvider(process.env.NETWORK_RPC);
								let wallet = new ethers.Wallet(pkseed, web3Provider);
								console.log('unlock wallet : ' + wallet.address);
							// We connect to the Contract using a Provider, so we will only
							// have read-only access to the Contract
							let contractLock = new ethers.Contract(contractAddrLock, abiLock, wallet);
							try {
								contractLock.withdrawTimeLock(contractAddress, new ethers.utils.parseEther(amount.toString())).then(async tx =>{
									console.log(tx);
									let allCol = chunkArray(timelockableCollection,500);
									console.log(`total `+timelockableCollection.length);
									let updatePath = '/batchUpdateFirestore'
									let updateText = JSON.stringify({
										timeunlock_status: 'unlock',
										timeunlock_tx: tx.hash
									});
									for(let i in allCol){
										let updateForm = {
											col: JSON.stringify(allCol[i]),
											data: updateText
										}
										await requestPost(url+updatePath,headers,updateForm)
										console.log('timelock '+ allCol[i].length +' updated');
									}
									res.json({statusCode:200, result: 'successfully', response: tx});
								});
							} catch (error) {
								console.log(error);
								res.json({statusCode:404, result: 'error transfer', response: error.message});
							}
						});
					});			
				}
			});
		}else{
			res.json(dataTimeunlock);
		}
	}
})

function notifyError(message, note){

        request({
            method: 'POST',
            uri: 'https://notify-api.line.me/api/notify',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            auth: {
                'bearer': 'WSLlqfeNjtPixu6wqYDr7MhKez7VupswwsURbUwrm9d'
            },
            form: {
                message:message
            }
        }, (err, httpResponse, body) => {
            if (err) {
                console.log(err);
            } else {

            }
        });       

}

};