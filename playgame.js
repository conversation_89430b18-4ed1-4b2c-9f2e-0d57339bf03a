

function getGame(){
	return new Promise((resolve, reject) =>{
		 var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	     
	    con.connect();

	    const sql = "SELECT * FROM game WHERE status = ? ORDER BY running";
	    con.query(sql, [1], function (err, result, fields) {
	    
	      if (err) {
	        console.log(err);
	        $log('listQuickpayShop : '+ err);
	        con.end();
	        reject();
	      	// return resolve(getListQuickpayShop());
	      }
			const callback = {
			 	result: result
			}
	 		resolve(callback);
	
		});
	   


	});
}
exports.modules.listGame = function (req, res){
	const APIKEY = req.body.apiKey;
	const SECRETKEY = req.body.secretKey;

	if(process.env.APIKEY === APIKEY && process.env.SECRETKEY === SECRETKEY){
	getGame().then(data =>{
		let shop = [];
		let result = [];
		
			for(let i=0;i<data.result.length;i++){
	    	
				let shopAdd = {
					'title': data.result[i].title,
					'logo': data.result[i].logo,
					'details': data.result[i].details,
					'bootrank': data.result[i].bootrank,
					'groupShop': data.result[i].groupShop,
					'running': data.result[i].running,
					'address': data.result[i].address
				};
				shop.push(shopAdd);
		}
		res.json({group: result, result:shop});
	}).catch(e=>{
		res.json({group: 'error', result:'error'});
	})



	}else{
		const result = {
			'statusCode':404,
			'result':'Authenticated failed'
		}
		res.json(result);
	}
}
