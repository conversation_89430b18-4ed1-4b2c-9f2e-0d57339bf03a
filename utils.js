const admin = require('firebase-admin');
const secrets = require('secret-sharing.js');
const dotenv = require('dotenv');
const mysql = require('mysql');
const request = require('request');
dotenv.config();
var bip39 = require('bip39')
var ethers = require('ethers');
var crypto = require('crypto'),
  algorithm = 'aes-256-gcm',
  password = process.env.ENCRYPTION_KEY;
var iv = process.env.IV;
var algorithmAdmin = 'aes-256-ctr';
var passwordAdmin = 'A$127855sd';


var serviceAccountOld = require("./oldlikepoint.json");
var secondaryAppConfig = {
    credential: admin.credential.cert(serviceAccountOld),
    databaseURL: "https://likecoin-2d12d.firebaseio.com/"
};
var secondary = admin.initializeApp(secondaryAppConfig, "four");

const openpgp = require('openpgp');
const { resolve } = require("path");
let sleep = (time) => new Promise((resolve) => setTimeout(resolve, time))

const pubkey = `-----BEGIN PGP PUBLIC KEY BLOCK-----
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=tQeo
-----END PGP PUBLIC KEY BLOCK-----
`;
let decrypt = function(text) {

  var decipher = crypto.createDecipheriv(algorithm, password, iv)
  decipher.setAuthTag(text.tag);
  var dec = decipher.update(text.content, 'hex', 'utf8')
  dec += decipher.final('utf8');
  return dec;
}


const findUserByAddress = async function(address) { 
  return new Promise(async (resolve, reject) => {

    const db = admin.firestore();
    const addr = await db.collection('addressDNS').where('address', '==', address.toLowerCase()).get();
    let uid = '';
    if(addr.size > 0) {
       addr.forEach(e=>{
        uid = e.id;
        // resolve(uid);
        console.log('e.id');
        // console.log(e);
         admin.auth().getUser(uid).then(function(userRecord) {
           resolve({email: userRecord.toJSON().email, phoneNumber: userRecord.toJSON().phoneNumber});
         }).catch(e=>{
           resolve('no');
         });
      });
   
     } else {
        resolve('no');
     }

  }).catch(e=>{
    resolve('no');
  });
}
let recoveryMasterKey = function(uid, keyDecrypt){
  return new Promise(async (resolve, reject)=>{
 
    const db = admin.firestore();
          db.collection('man').doc('manyu').get().then(key =>{

           let data = {
             content: key.data().content,
             tag: key.data().tag
           }
             console.log('recoveryMasterKey');

              request.post({ url: 'https://checkairdrop.prachakij.com/getyubi', form: { 
                apikey: process.env.API_CHECK,
                secret: process.env.SECRET_CHECK
            } }, async function (err, httpResponse, body) {
            
              let parse = JSON.parse(body);
            
              let pass = Buffer.from(parse.result, 'base64').toString('ascii');
 
               let x = await decryptWithPassword(data, pass);
               
                  const passphrase = x;
                  let db = admin.firestore();
                
                    const { keys: [privateKey] } = await openpgp.key.readArmored(passphrase);
                    await privateKey.decrypt(process.env.passM)
                 
                   const { data: decrypted } = await openpgp.decrypt({
                        message: await openpgp.message.readArmored(keyDecrypt),              // parse armored message
                        publicKeys: (await openpgp.key.readArmored(pubkey)).keys, // for verification (optional)
                        privateKeys: [privateKey]                                           // for decryption
                    });
                   
                    resolve(decrypted);
                  
            
            });     
         });
  });
}

let recoverySeedNoSecret = function(uid, secret, userNoencrypt, userEncrypt, tag, wallet_number) {
  return new Promise((resolve, reject) => {
    console.log('recovery');
    console.log(wallet_number);
    console.log(uid);
    let db = admin.firestore();
    var cyphermines = db.collection('cyphermines').doc(wallet_number === 'no_number' ? uid : uid+wallet_number);
    var masterKeyEncrypted = db.collection('masterKeyEncrypted').doc(wallet_number === 'no_number' ? uid : uid+wallet_number);

    masterKeyEncrypted
    .get()
    .then(key =>{
      console.log(' foun key');
      recoveryMasterKey(wallet_number === 'no_number' ? uid : uid+wallet_number, key.data().key).then(masterkey =>{
        cyphermines.get().then(cypher => {
          if (!cypher.exists) {
            console.log('No such document!');
          } else {
            try{
              var comb = secrets.combine([userNoencrypt, masterkey, cypher.data().key.toString()]);
              comb = secrets.hex2str(comb);
              let content = {
                content: comb,
                tag: tag
              }
              var finalSeed = decrypt(content);
           
              // getFeeFromNosecret(finalSeed);
              resolve(finalSeed);

            }catch(e){
              reject();
            }
          
          }

        }).catch(e => {
          console.log(e);
          reject(e);
        });
      });

    }).catch(e=>{
      console.log(e);
      res.json({statusCode:404, result:e});
    })

  });
}

let getFeeFromNosecret = function(seed){
  RetrievePKED2519(seed).then(pkseed =>{
   let addressOwner = retrieveAddress(pkseed); 
    console.log(addressOwner);
           request({
            method: 'POST',
            uri: 'https://getfeetest.prachakij.com/sendEth',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            form: {
               address: addressOwner
            }
        }, (err, httpResponse, body) => {
            if (err) {
                console.log(err);
            } else {
              console.log(body);
               return true;
            }
        });
  })  
}
let retrieveAddress = function(pk){
    let wallet = new ethers.Wallet(pk);
    return wallet.address;
}
let RetrievePKED2519 = function(seed) {
  return new Promise((resolve, reject) => {
    try{

      const hexSeed = bip39.mnemonicToSeedSync(seed).toString('hex');
      let path = "m/44'/60'/0'/0/0";
      let mnemonicWallet = ethers.Wallet.fromMnemonic(seed, path);
      resolve(mnemonicWallet.privateKey);     

      resolve(mnemonicWallet.privateKey);
    } catch(e) {
      reject(e);
    }
       
  });
}

let decryptWithPassword = function(text, pass) {
    try{
      var decipher = crypto.createDecipheriv(algorithm, pass, iv)
      decipher.setAuthTag(text.tag);
      var dec = decipher.update(text.content, 'hex', 'utf8')
      dec += decipher.final('utf8');
      return dec;
    }catch(e){
      return 'failed';
    }

}

const transfer = function(to, pk, amount) {
  return new Promise((resolve, reject) =>{
    let url = process.env.NETWORK_RPC;
    let customHttpProvider = new ethers.providers.JsonRpcProvider(url);
    const abi = process.env.ABI_LIKE;
    const contractAddress = process.env.CONTRACT_LIKE;
    let wallet = new ethers.Wallet(pk, customHttpProvider);
    let contract = new ethers.Contract(contractAddress, abi, customHttpProvider);

    let overrides = {
      gasLimit: 90000,
      gasPrice: ethers.utils.parseUnits('4.0', 'gwei'),
    };
    let contractWithSigner = contract.connect(wallet, overrides);
    console.log(wallet.address);
    try{
        console.log('transfer!');
        // console.log(to);
        console.log(amount.toString());
        // console.log(pk);
        contractWithSigner.transfer(to, ethers.utils.parseEther(amount.toString())).then(tx =>{
          console.log(tx);
          resolve(tx);
        }).catch(e =>{
          return sleep(3000).then(() =>{
             console.log('retry sent again 273');
            return resolve(transfer(to, pk, amount));
          });
        });
    }catch(e) {
      console.log(e);
      return sleep(3000).then(() =>{
         // notifyError('payment error utils.js 277');
         console.log('retry sent again 281');
        return resolve(transfer(to, pk, amount));
      });
    };
  
  });
}


const getLottoLock = function(c_lock, address) {
  return new Promise((resolve, reject) => {
            c_lock.maxOption().then(max=>{
              let count = 0;
              let sumLock = 0;
              for(let i=0;i<max;i++){
                c_lock.timelockhold(address, i).then((value , a=i)=>{
                  // console.log('option '+ a + ' = '+ value[4]/10e17);
                  // console.log(value.expire.toString());
                  count++;
                  sumLock += value[4]/10e17;
                  if(count == max) {
                    resolve(sumLock);
                  }
                  
                })
              }
            })    
  })
}
const getAutocompound = function(address) {
  return new Promise((resolve, reject) => {
    // console.log(address);
    // resolve(0);
    let url = process.env.NETWORK_RPC;

    // console.log(url);
    let customHttpProvider = new ethers.providers.JsonRpcProvider(url);
    const abi = process.env.ABI_LOCK_LOTTO;
    const abiLBANK = process.env.ABI_LBANK;
    const contractAddress = process.env.CONTRACT_LOCK_LOTTO;
    const contractAddressLBANK = process.env.CONTRACT_LBANK;
    // console.log(contractAddress);
    // console.log(contractAddressLBANK);


    // const c_lock = new ethers.Contract(contractAddress, abi, customHttpProvider);
    const contractLBANK = new ethers.Contract(contractAddressLBANK, abiLBANK, customHttpProvider);

      contractLBANK.balanceOf(address).then(shared =>{
        // console.log(shared);

        contractLBANK.totalSupply().then(totalSupply=>{
          // console.log(totalSupply);
          contractLBANK.totalLIKE().then(async (totalLIKE) => {
            const supply = totalSupply/10e17;
            const like = totalLIKE/10e17;
            const share = shared/10e17;

            // getLottoLock(c_lock, address).then(lotto_locked =>{ 
              let totalLock = (like/supply*share);
              resolve(totalLock);
            // });

            
          })
          // resolve(totalSupply);
        })
      }).catch(e =>{
        // reject(e);
        console.log(e);
      });      
  });
}

const newGetAutocompound = function(address) {
  return new Promise((resolve, reject) => {
    let url = process.env.NETWORK_RPC;
    let promiseList = [];
    let customHttpProvider = new ethers.providers.JsonRpcProvider(url);
    const abiLBANK = process.env.ABI_LBANK;
    const contractAddressLBANK = process.env.CONTRACT_LBANK;
    const contractLBANK = new ethers.Contract(contractAddressLBANK, abiLBANK, customHttpProvider);
    let shared = contractLBANK.balanceOf(address);
    let totalSupply = contractLBANK.totalSupply();
    let totalLIKE = contractLBANK.totalLIKE();
    promiseList.push(shared);
    promiseList.push(totalSupply);
    promiseList.push(totalLIKE);

    Promise.all(promiseList).then((results) => {
      const share = results[0]/10e17;
      const supply = results[1]/10e17;
      const like = results[2]/10e17;
      let totalLock = (like/supply*share);
      return resolve(totalLock);
    }).catch(e =>{
      // reject(e);
      console.log(e);
      return 0;
    });      
  })
}

const newGetLPCU = function(address) {
  return new Promise((resolve, reject) => {
    let url = process.env.NETWORK_RPC;
    let promiseList = [];
    let customHttpProvider = new ethers.providers.JsonRpcProvider(url);
    const abi = process.env.abi_lpcu;
    const contract = process.env.contract_lpcu;
    const contractLPCU = new ethers.Contract(contract, abi, customHttpProvider);
    let total = contractLPCU.totalLIKE();
    let supply = contractLPCU.totalSupply();
    let shared = contractLPCU.Borrower(address);
    promiseList.push(total);
    promiseList.push(supply);
    promiseList.push(shared);

    Promise.all(promiseList).then((results) => {
      const total = results[0]/10e17;
      const supply = results[1]/10e17;
      const shared = results[2];
      if(shared.lpcu/10e17 === 0){
        return resolve(0);
      }else{
        let lpcu = (total/10e17)/(supply/10e17)*(shared.lpcu/10e17);
        return resolve(lpcu);
      } 
    }).catch(e =>{
      console.log(e);
      return resolve(0);
    });     
  }) 
}


const getLPCU = function(address) {
  return new Promise((resolve, reject) => {
    // console.log(address);
    // resolve(0);
    let url = process.env.NETWORK_RPC;

    // console.log(url);
    let customHttpProvider = new ethers.providers.JsonRpcProvider(url);
    const abi = process.env.abi_lpcu;
    const contract = process.env.contract_lpcu;
    // console.log(contractAddress);
    // console.log(contractAddressLBANK);



    const contractLPCU = new ethers.Contract(contract, abi, customHttpProvider);
      contractLPCU.totalLIKE().then(total=>{
          // console.log(total/10e17);
          contractLPCU.totalSupply().then(supply=>{
            // console.log(supply/10e17);
             contractLPCU.Borrower(address).then(shared =>{
               // console.log(shared.lpcu/10e17);
               if(shared.lpcu/10e17 === 0){
                 resolve(0);
               }else{
                 resolve((total/10e17)/(supply/10e17)*(shared.lpcu/10e17));
               }
              
            }).catch(e =>{
              // reject(e);
              console.log(e);
            });          
          })
      })
  });
}


const getShareRate = function() {
  return new Promise((resolve, reject) => {

    let url = process.env.NETWORK_RPC;


    let customHttpProvider = new ethers.providers.JsonRpcProvider(url);
    const abiLBANK = process.env.ABI_LBANK;
    const contractAddressLBANK = process.env.CONTRACT_LBANK;
    const contractLBANK = new ethers.Contract(contractAddressLBANK, abiLBANK, customHttpProvider);

    contractLBANK.totalLIKE().then(like =>{
      contractLBANK.totalSupply().then(total => {
        resolve((like/10e17)/(total/10e17));
      })
    })
     
  });
}


const getAPR = function() {
  return new Promise((resolve, reject) => {
    var con = mysql.createConnection({
      host     : process.env.RDS_HOST,
      user     : process.env.RDS_USERNAME,
      password : process.env.RDS_PASSWORD,
      database : process.env.RDS_DATABASE
    });
     
    con.connect();

    con.query("SELECT total_locked, rewards FROM community_rewards ORDER BY running DESC LIMIT 1", function(err, result) {
      if(err) {
        con.end();
        reject();
      }else{
        con.end();
        resolve(result[0].rewards / result[0].total_locked * 365 * 100);
      }
    });

  })
}

const getHistoryRewards = function() {
  return new Promise((resolve, reject) => {
    var con = mysql.createConnection({
      host     : process.env.RDS_HOST,
      user     : process.env.RDS_USERNAME,
      password : process.env.RDS_PASSWORD,
      database : process.env.RDS_DATABASE
    });
     
    con.connect();

    con.query("SELECT total_locked, rewards, create_time FROM community_rewards ORDER BY running DESC LIMIT 30", function(err, result) {
      if(err) {
        con.end();
        reject();
      }else{
        let apyArray = [];
        for(let i=0;i<result.length;i++){
          let apr = (result[i].rewards / result[i].total_locked * 365 * 100).toFixed(2);
          apyArray.push({apr:apr.toString()+' %', date: result[i].create_time.toString().substring(0, 10)});
        }
        con.end();
        resolve(apyArray);
      }
    });

  })
}

const getHistoryRewardsPercentage = function() {
  return new Promise((resolve, reject) => {
    var con = mysql.createConnection({
      host     : process.env.RDS_HOST,
      user     : process.env.RDS_USERNAME,
      password : process.env.RDS_PASSWORD,
      database : process.env.RDS_DATABASE
    });
     
    con.connect();

    con.query("SELECT total_locked, rewards, round_time FROM community_rewards WHERE status = 2 ORDER BY running DESC LIMIT 30", function(err, result) {
      if(err) {
        con.end();
        reject();
      }else{
        let apyArray = [];
        for(let i=0;i<result.length;i++){
          let apr = (result[i].rewards / result[i].total_locked);
          apyArray.push({apr: apr, date: result[i].round_time.toString().substring(0, 10)});
        }
        con.end();
        resolve(apyArray);
      }
    });

  })
}
const verifyToken = function(token) {
  return new Promise((resolve, reject) => {
    admin.auth().verifyIdToken(token)
          .then(function (decodedToken){
            // console.log(decodedToken);
           resolve(decodedToken);
      }).catch(e => {
        reject(e);
      });
    });
}


const queryRankingLock = function() {
   return new Promise((resolve, reject) => {
    var con = mysql.createConnection({
      host     : process.env.RDS_HOST,
      user     : process.env.RDS_USERNAME,
      password : process.env.RDS_PASSWORD,
      database : process.env.RDS_DATABASE
    });
     
    con.connect();

    con.query("SELECT sender, amount FROM locked_likepoint WHERE amount > 0 ORDER BY amount DESC", function(err, result) {
      if(err) {
        con.end();
        reject();
      }else{
        con.end();
        let user = [];
        for(let i =0;i<result.length;i++){
          user.push({sender: result[i].sender, amount: result[i].amount/10e17});
        }
        resolve(user);
      }
    });

  }) 
}





const queryRakingAutoCompound = function() {
  return new Promise(async (resolve, reject) =>{
    const db = secondary.firestore();

    const userLock = await db.collection("locked_autocompound")
                          .where("amount", ">", "0")
                          .orderBy("amount", "desc")
                          .get();
    const rate = await getShareRate();
    var user = [];
    userLock.forEach(docs=>{
      user.push({sender: docs.data().sender, amount: docs.data().amount/10e17*rate});
    });
    resolve(user);

  });

}

const getRanking = function() {
  return new Promise(async (resolve, reject) =>{
    const LockReward = await queryRankingLock();
    const AutoCompound = await queryRakingAutoCompound();
    let data = [];
    data.push(LockReward);
    data.push(AutoCompound);
    resolve(data);
  })
}


// queryRakingAutoCompound().then(data=>{
//   console.log(data);
// })
// getAutocompound('0xce5080f601656670ea06c92eba9632506fe54d14').then(result =>{
//   console.log(result);
// });

module.exports.decrypt = decrypt;
module.exports.getAutocompound = getAutocompound;
module.exports.recoverySeedNoSecret = recoverySeedNoSecret;
module.exports.recoveryMasterKey = recoveryMasterKey;
module.exports.getFeeFromNosecret = getFeeFromNosecret;
module.exports.RetrievePKED2519 = RetrievePKED2519;
module.exports.decryptWithPassword = decryptWithPassword;
module.exports.retrieveAddress = retrieveAddress;
module.exports.transfer = transfer;
module.exports.getShareRate = getShareRate;
module.exports.verifyToken = verifyToken
module.exports.findUserByAddress = findUserByAddress;
module.exports.getLPCU = getLPCU;
module.exports.getAPR = getAPR;
module.exports.getHistoryRewards = getHistoryRewards;
module.exports.getRanking = getRanking;
module.exports.getHistoryRewardsPercentage = getHistoryRewardsPercentage;
module.exports.newGetAutocompound = newGetAutocompound;
module.exports.newGetLPCU = newGetLPCU;