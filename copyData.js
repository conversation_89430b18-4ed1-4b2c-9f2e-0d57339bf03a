'use strict';


var admin = require("firebase-admin");

var serviceAccountOld = require("./oldlikepoint.json");

const dotenv = require('dotenv');
dotenv.config();
const express = require('express')
const app = express()
const bodyParser = require('body-parser');
var session = require('express-session')
var cryptico = require('cryptico');
var generator = require('generate-password');
var Promise = require('promise');
var await = require('await')``
var moment = require('moment-timezone');
// var moment = require('moment');
var elasticsearch = require('elasticsearch');
var await = require('await');
var request = require('request');
var bip39 = require('bip39')
var btcLib = require('bitcoinjs-lib');
var bs58check = require('bs58check');
const twilio = require('twilio');
var crypto = require('crypto'),
  algorithm = 'aes-256-gcm',
  password = process.env.ENCRYPTION_KEY;
//yep
const algorithmToFront = 'aes-256-cbc';
var algorithmToFrontGCM = 'aes-256-gcm';
var serviceAccount = require("./likecoin-2d12d-firebase-adminsdk-l8rpq-4eb4b82772.json");
var serviceTest = require("./likewalletTest.json");
const passwordToFront = process.env.passwordToFront;
const ivToFront = process.env.ivToFront;
// const passwordToFront = process.env.ENCRYPTION_KEY_FRONT;
// const ivToFront = process.env.IV_FRONT; 
const $log = require('log');
const bcrypt = require('bcrypt');
const IV_LENGTH = 16;
const ethers = require('ethers');
const accountSid = process.env.TWILIO_KEY;
const authToken = process.env.TWILIO_SECRET;
const clientTwilio = require('twilio')(accountSid, authToken);
const secrets = require('secret-sharing.js');
const uuidv4 = require('uuid/v4');
const randomstring = require("randomstring");
const uuidAPIKey = require('uuid-apikey');
const aws = require('aws-sdk');
const ed25519 = require('ed25519-hd-key')
const mysql      = require('mysql');



admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

var secondaryAppConfig = {
    credential: admin.credential.cert(serviceTest)
};

// Initialize another app with a different config
var secondary = admin.initializeApp(secondaryAppConfig, "secondary");

let db = admin.firestore();
let db2 = secondary.firestore();
let citiesRef = db.collection('exchangeFiat');

let allCities = citiesRef.get()
  .then(snapshot => {
    snapshot.forEach(doc => {
      console.log(doc.id, '=>', doc.data());
      let testDB = db2.collection('exchangeFiat')
      	.doc(doc.id)
      	.set(doc.data());
    });
  })
  .catch(err => {
    console.log('Error getting documents', err);
  });
