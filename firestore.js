'use strict';


var admin = require("firebase-admin");
var serviceAccountOld = require("./oldlikepoint.json");

const dotenv = require('dotenv');
dotenv.config();
const express = require('express')
const app = express()
const bodyParser = require('body-parser');
var session = require('express-session')
var cryptico = require('cryptico');
var generator = require('generate-password');
var Promise = require('promise');
var await = require('await')``
var moment = require('moment-timezone');
// var moment = require('moment');
var elasticsearch = require('elasticsearch');
var await = require('await');
var request = require('request');
var bip39 = require('bip39')
var btcLib = require('bitcoinjs-lib');
var bs58check = require('bs58check');
const twilio = require('twilio');
var jwt = require('jsonwebtoken');
var crypto = require('crypto'),
  algorithm = 'aes-256-gcm',
  password = process.env.ENCRYPTION_KEY;

  //ipfs
const ipfsClient = require('ipfs-http-client')
const ipfsCluster = require('ipfs-cluster-api-remove-name')
const fs = require('fs')
//yep
const algorithmToFront = 'aes-256-cbc';
var algorithmToFrontGCM = 'aes-256-gcm';
var serviceAccount = require("./"+process.env.FIREBASE);
const passwordToFront = process.env.passwordToFront;
const ivToFront = process.env.ivToFront;
// const passwordToFront = process.env.ENCRYPTION_KEY_FRONT;
// const ivToFront = process.env.IV_FRONT; 
const $log = require('log');
const bcrypt = require('bcrypt');
const IV_LENGTH = 16;
const ethers = require('ethers');
const accountSid = process.env.TWILIO_KEY;
const authToken = process.env.TWILIO_SECRET;
const clientTwilio = require('twilio')(accountSid, authToken);
const secrets = require('secret-sharing.js');
const uuidv4 = require('uuid/v4');
const randomstring = require("randomstring");
const uuidAPIKey = require('uuid-apikey');
const aws = require('aws-sdk');
const web3 = require('web3');
const ed25519 = require('ed25519-hd-key')
const mysql      = require('mysql');
const kms = new aws.KMS({
    accessKeyId: '********************', //credentials for your IAM user
    secretAccessKey: 'XtBvGx6pX+UssPhxbCc1xSrITpNf9IPQZuTIn/2a', //credentials for your IAM user
    region: 'ap-southeast-1'
});

// import { rateLimiterUsingThirdParty } from './middlewares';
var rateLimiterUsingThirdParty = require('./middlewares');
// console.log(JSON.stringify());

var convertlikepoint = require('./convertlikepoint.js');
var userManagement = require('./userManagement.js');
var utilitiesService = require('./utilities.js');
var getHistory = require('./getHistory.js');
var quickpayshop = require('./quickpayshop.js');
// var thaiqr = require('./thaiqr.js');
var ipfs_control = require('./ipfs_control.js');
var apiBank = require('./apiBank.js');
var notifications = require('./notifications.js');
var like_connect = require('./likewallet_connection.js');
var mqtt = require('./mqtt_publish.js');
const auth = require('./middlewares/auth.js');
const thaiqrNew = require('./thaiqrNew.js');
const serviceLike = require('./service.js');

 // mqtt.publishToKPLUS();
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.json());
app.use(rateLimiterUsingThirdParty);
const contractAddressSmartContract = process.env.CONTRACT_LIKE;
const abiSmartContract = process.env.ABI_LIKE;


app.use(function (req, res, next) {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept");
  next();
});

var con = mysql.createConnection({
    host: "cyphermines.ckizogijupke.ap-southeast-1.rds.amazonaws.com",
    user: "likewallet",
    password: "o4iJcN2GLNVdCrMl",
    database: "likewallet",
    timezone: "UTC+0"
});

var listenPort = process.env.PORT;

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

var secondaryAppConfig = {
    credential: admin.credential.cert(serviceAccountOld),
    databaseURL: "https://likecoin-2d12d.firebaseio.com/"
};

// Initialize another app with a different config
var secondary = admin.initializeApp(secondaryAppConfig, "secondary");



const db = admin.firestore();

let bulkDATA = [];


db.collection('addressDNS').get().then(value=>{
	// console.log(value);

	value.forEach(e =>{
		// console.log(e.data());
    let data = [
            e.data().name != undefined ? e.data().name : 'no',
            e.data().address != undefined ? e.data().address.toLowerCase() : 'no',
            e.data().locale != undefined ? e.data().locale : 'no',
            e.data().FCMtoken != undefined ? e.data().FCMtoken : 'no'
        ];
        bulkDATA.push(data);

	})
  bulkSQL(bulkDATA);
})

function bulkSQL(data) {
    console.log(data);
          let sql = "INSERT INTO users (fullname, address, locale, FCMtoken) VALUES ?";
      
             
                    con.query(sql, [data], function (err) {
                        if (err) throw err;
                        console.log('success');
                        con.end();
                        process.exit()
                    });

         
}

// admin.auth().getUserByPhoneNumber('+8562093505664').then(data =>{
// 	console.log('new');
// 	console.log(data);
// secondary.auth().getUserByPhoneNumber('+8562093505664').then(datax =>{
// 		console.log('old');
// 	console.log(datax);
// })	
// });





