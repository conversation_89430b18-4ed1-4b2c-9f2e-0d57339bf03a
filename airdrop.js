'use strict';

const ethers = require('ethers');
const web3 = require('web3');
const dotenv = require('dotenv');
const moment = require('moment-timezone');
dotenv.config();
function checkClaim(address){
	return new Promise((resolve, reject) =>{
		let addressLike = address;
	    var contractAddress = process.env.contractLock;
	    var abi = JSON.parse(process.env.abiLock);
	  

	    // let currentProvider = new web3.providers.HttpProvider(process.env.NETWORK_RPC);
	    // let web3Provider = new ethers.providers.Web3Provider(currentProvider);
		let url = process.env.NETWORK_RPC;
		var web3Provider = new ethers.providers.JsonRpcProvider(url);

		// We connect to the Contract using a Provider, so we will only
		// have read-only access to the Contract
		let contract = new ethers.Contract(contractAddress, abi, web3Provider);

		contract.tokens(process.env.CONTRACT_LIKE, addressLike).then(result =>{
			resolve(result);
		})	
	})
	//cc
}


// checkClaim('******************************************').then(res=>{
// 	console.log(res.start);
// 	console.log(moment.unix(res.start).format("MM/DD/YYYY HH:mm:ss"));
// });


module.exports.checkClaim = checkClaim;
