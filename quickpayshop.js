
module.exports = function (
	 app,
	 admin,
	 serviceAccount,
	 dotenv,
	 cryptico,
	 generator,
	 moment,
	 elasticsearch,
	 request,
	 bip39,
	 btcLib,
	 bs58check,
	 twilio,
	 crypto,
	 algorithm,
	 password,
	 algorithmToFront,
	 $log,
	 bcrypt,
	 IV_LENGTH,
	 ethers,
	 accountSid,
	 authToken,
	 clientTwilio,
	 secrets,
	 uuidv4,
	 randomstring,
	 uuidAPIKey,
	 aws,
	 ed25519,
	 mysql,
	 kms,
	 decryptToFrontInternal,
	 encrypt,
	 randomText,
	 recoverySeed,
	 encryptToFront,
	 encryptWithPassword,
	 openpgp,
	 pubkey,
	 secondary,
	 selectMysql
	){


function getListQuickpayShop(){
	return new Promise((resolve, reject) =>{
		 var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	     
	    con.connect();

	    const sql = "SELECT * FROM quickpayshop WHERE status = ? ORDER BY bootrank";
 		const sqlorderGroup = "SELECT groupShop FROM quickpayshop WHERE status = ? ORDER BY groupRank";
	    con.query(sql, [1], function (err, result, fields) {
	    
	      if (err) {
	        console.log(err);
	        $log('listQuickpayShop : '+ err);
	        con.end();
	        reject();
	      	// return resolve(getListQuickpayShop());
	      }
	      //sort groupRank
   			con.query(sqlorderGroup, [1], function (err2, result2, fields2) {
   			     if (err2) {
			        console.log(err2);
			        $log('listQuickpayShop : '+ err2);
			        con.end();
			        reject();
			      	// return resolve(getListQuickpayShop());
			      }
   				 con.end();
   				 const callback = {
   				 	result: result,
   				 	group: result2
   				 }
	     		 resolve(callback);
   			});
	
		});
	   


	});
}

function getListSpendpayShop(){
	return new Promise((resolve, reject) =>{
		 var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	     
	    con.connect();

	    const sql = "SELECT * FROM spendpayshop WHERE status = ? ORDER BY bootrank";
 		const sqlorderGroup = "SELECT groupShop FROM spendpayshop WHERE status = ? ORDER BY groupRank";
	    con.query(sql, [1], function (err, result, fields) {
	    
	      if (err) {
	        console.log(err);
	        $log('listQuickpayShop : '+ err);
	        con.end();
	        reject();
	      	// return resolve(getListQuickpayShop());
	      }
	      //sort groupRank
   			con.query(sqlorderGroup, [1], function (err2, result2, fields2) {
   			     if (err2) {
			        console.log(err2);
			        $log('listQuickpayShop : '+ err2);
			        con.end();
			        reject();
			      	// return resolve(getListQuickpayShop());
			      }
   				 con.end();
   				 const callback = {
   				 	result: result,
   				 	group: result2
   				 }
	     		 resolve(callback);
   			});
	
		});
	   


	});
}

function getListQuickpayShopTest(){
	return new Promise((resolve, reject) =>{
		 var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	     
	    con.connect();

	    const sql = "SELECT * FROM quickpayshopTest WHERE status = ? ORDER BY bootrank";
 		const sqlorderGroup = "SELECT groupShop FROM quickpayshop WHERE status = ? ORDER BY groupRank";
	    con.query(sql, [1], function (err, result, fields) {
	    
	      if (err) {
	        console.log(err);
	        $log('listQuickpayShop : '+ err);
	        con.end();
	        reject();
	      	// return resolve(getListQuickpayShop());
	      }
	      //sort groupRank
   			con.query(sqlorderGroup, [1], function (err2, result2, fields2) {
   			     if (err2) {
			        console.log(err2);
			        $log('listQuickpayShop : '+ err2);
			        con.end();
			        reject();
			      	// return resolve(getListQuickpayShop());
			      }
   				 con.end();
   				 const callback = {
   				 	result: result,
   				 	group: result2
   				 }
	     		 resolve(callback);
   			});
	
		});
	   


	});
}
app.post('/listQucikpayShop', function(req, res){
	const APIKEY = req.body.apiKey;
	const SECRETKEY = req.body.secretKey;

	if(process.env.APIKEY === APIKEY && process.env.SECRETKEY === SECRETKEY){
	getListQuickpayShop().then(data =>{
		let shop = [];
		let result = [];
		
			for(let a=0;a<data.group.length;a++){
				if (result.indexOf(data.group[a].groupShop) === -1) {
			        result.push(data.group[a].groupShop)
			    }			
			}
			for(let i=0;i<data.result.length;i++){
	    	
				let shopAdd = {
					'title': data.result[i].title,
					'logo': data.result[i].logo,
					'details': data.result[i].details,
					'bootrank': data.result[i].bootrank,
					'groupShop': data.result[i].groupShop,
					'running': data.result[i].running,
					'address': data.result[i].address,
					'callFunction':data.result[i].callFunction,
					'contract':data.result[i].contract,
					'abi':data.result[i].abi
				};
				shop.push(shopAdd);
		}
		res.json({group: result, result:shop});
	}).catch(e=>{
			res.json({group: 'error', result:'error'});
	})



	}else{
		const result = {
			'statusCode':404,
			'result':'Authenticated failed'
		}
		res.json(result);
	}
});
app.post('/listSpendkpayShop', function(req, res){
	const APIKEY = req.body.apiKey;
	const SECRETKEY = req.body.secretKey;

	if(process.env.APIKEY === APIKEY && process.env.SECRETKEY === SECRETKEY){
	getListSpendpayShop().then(data =>{
		let shop = [];
		let result = [];
		
			for(let a=0;a<data.group.length;a++){
				if (result.indexOf(data.group[a].groupShop) === -1) {
			        result.push(data.group[a].groupShop)
			    }			
			}
			for(let i=0;i<data.result.length;i++){
	    	
				let shopAdd = {
					'title': data.result[i].title,
					'logo': data.result[i].logo,
					'details': data.result[i].details,
					'bootrank': data.result[i].bootrank,
					'groupShop': data.result[i].groupShop,
					'running': data.result[i].running,
					'address': data.result[i].address,
					'callFunction':data.result[i].callFunction,
					'contract':data.result[i].contract,
					'abi':data.result[i].abi
				};
				shop.push(shopAdd);
		}
		res.json({group: result, result:shop});
	}).catch(e=>{
			res.json({group: 'error', result:'error'});
	})



	}else{
		const result = {
			'statusCode':404,
			'result':'Authenticated failed'
		}
		res.json(result);
	}
});


app.post('/listQucikpayShopTest', function(req, res){
	const APIKEY = req.body.apiKey;
	const SECRETKEY = req.body.secretKey;

	if(process.env.APIKEY === APIKEY && process.env.SECRETKEY === SECRETKEY){
	getListQuickpayShopTest().then(data =>{
		let shop = [];
		let result = [];
		
			for(let a=0;a<data.group.length;a++){
				if (result.indexOf(data.group[a].groupShop) === -1) {
			        result.push(data.group[a].groupShop)
			    }			
			}
			for(let i=0;i<data.result.length;i++){
	    	
				let shopAdd = {
					'title': data.result[i].title,
					'logo': data.result[i].logo,
					'details': data.result[i].details,
					'bootrank': data.result[i].bootrank,
					'groupShop': data.result[i].groupShop,
					'running': data.result[i].running,
					'address': data.result[i].address,
					'callFunction':data.result[i].callFunction,
					'contract':data.result[i].contract,
					'abi':data.result[i].abi
				};
				shop.push(shopAdd);
		}
		res.json({group: result, result:shop});
	}).catch(e=>{
			res.json({group: 'error', result:'error'});
	})



	}else{
		const result = {
			'statusCode':404,
			'result':'Authenticated failed'
		}
		res.json(result);
	}
});



}

