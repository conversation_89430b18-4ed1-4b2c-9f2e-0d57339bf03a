const ethers = require('ethers');
const decrypt = 'cHJpbnQgdW5sb2NrIG93bmVyIGZsYXZvciBraXR0ZW4gbmFtZSBsb3lhbCB5YXJkIGZpZ3VyZSBjaGFyZ2UgdG9wIHNpeA';
const mnemonic = Buffer.from(decrypt, 'base64').toString('ascii');

const wallet = ethers.Wallet.fromMnemonic('print unlock owner flavor kitten name loyal yard figure charge top six');
// const wallet = ethers.Wallet.fromMnemonic(mnemonic);
// const wallet = ethers.Wallet.createRandom();

// console.log(mnemonic);
console.log(wallet.address);
console.log(wallet.privateKey);
console.log(wallet.mnemonic);
// tooth visual clog knee entire try scale skin this endorse edit extend

// const decrypt



//harbor measure rose crater minute retire cereal anchor true render pull nothing


//0xf1755611eed27476d922d39750311cd9c276ba1cd136014e03605272c487c7fb
