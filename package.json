{"name": "newlikewallet", "version": "1.0.0", "description": "", "main": "likewallet.js", "scripts": {"test": "mocha", "start": "node likewallet.js"}, "author": "prapat polchan", "license": "ISC", "dependencies": {"@ethersproject/experimental": "^5.7.0", "@vonage/server-sdk": "^3.19.0", "atob": "^2.1.2", "await": "^0.2.6", "aws-sdk": "^2.1692.0", "bcrypt": "^5.1.1", "bip39": "^3.1.0", "bitcoinjs-lib": "^6.1.6", "core-js": "3.39.0", "bitcore-mnemonic": "^10.5.3", "bl": "^6.0.16", "body-parser": "^1.20.3", "bs58check": "^4.0.0", "chai": "^5.1.2", "chai-http": "^5.1.1", "cors": "^2.8.5", "create-hash": "^1.2.0", "cryptico": "^1.0.2", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "date-and-time": "^3.6.0", "dotenv": "^16.4.5", "ed25519": "^0.0.5", "ed25519-hd-key": "^1.3.0", "elasticsearch": "^16.7.3", "errorhandler": "^1.5.1", "ethereum-encryption": "0.0.5", "ethers": "^6.13.4", "express": "^4.21.1", "express-rate-limit": "^7.4.1", "express-session": "^1.18.1", "firebase-admin": "^12.7.0", "generate-password": "^1.7.1", "ipfs-api": "^26.1.2", "ipfs-cluster-api-remove-name": "0.0.9", "ipfs-http-client": "^60.0.1", "jsonwebtoken": "^9.0.2", "kue": "^0.11.6", "log": "^6.3.2", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "mosca": "^2.8.3", "mqtt": "^5.10.1", "mysql": "^2.18.1", "node-2fa": "^2.0.3", "nodemailer": "^6.9.16", "openpgp": "^6.0.0", "promise": "^8.3.0", "randomstring": "^1.3.0", "request": "^2.88.2", "secret-sharing.js": "^1.3.1", "serverless": "^4.4.7", "serverless-http": "^3.2.0", "stream-chat": "^8.42.0", "twilio": "^5.3.5", "uuid": "^11.0.2", "uuid-apikey": "^1.5.3", "web3": "4.15.0"}, "devDependencies": {"mocha": "10.8.2", "rewire": "7.0.0", "supertest": "7.0.0"}}