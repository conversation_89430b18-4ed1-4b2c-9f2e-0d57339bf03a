{"name": "newlikewallet", "version": "1.0.0", "description": "", "main": "likewallet.js", "scripts": {"test": "mocha", "start": "node likewallet.js"}, "author": "prapat polchan", "license": "ISC", "dependencies": {"@ethersproject/experimental": "^5.4.0", "@vonage/server-sdk": "^2.10.10", "atob": "^2.1.2", "await": "^0.2.6", "aws-sdk": "^2.570.0", "bcrypt": "^5.0.0", "bip39": "^3.0.2", "bitcoinjs-lib": "^5.1.6", "bitcore-mnemonic": "^8.6.0", "bl": "^4.0.1", "body-parser": "^1.19.0", "bs58check": "^2.1.2", "chai": "^4.2.0", "chai-http": "^4.3.0", "cors": "^2.8.5", "create-hash": "^1.2.0", "cryptico": "^1.0.2", "crypto": "^1.0.1", "crypto-js": "^3.1.9-1", "date-and-time": "^2.1.0", "dotenv": "^8.1.0", "ed25519": "^0.0.5", "ed25519-hd-key": "^1.0.0", "elasticsearch": "^16.7.1", "errorhandler": "^1.5.1", "ethereum-encryption": "0.0.5", "ethers": "^5.4.0", "express": "^4.17.1", "express-rate-limit": "^5.1.3", "express-session": "^1.16.2", "firebase-admin": "^9.4.2", "generate-password": "^1.4.2", "ipfs-api": "^26.1.2", "ipfs-cluster-api-remove-name": "0.0.9", "ipfs-http-client": "^42.0.0", "jsonwebtoken": "^8.5.1", "kue": "^0.11.6", "log": "^6.0.0", "moment": "^2.24.0", "moment-timezone": "^0.5.26", "mosca": "^2.8.3", "mqtt": "^3.0.0", "mysql": "^2.17.1", "node-2fa": "^2.0.2", "nodemailer": "^6.4.6", "openpgp": "^4.6.2", "promise": "^8.0.3", "randomstring": "^1.1.5", "request": "^2.88.0", "secret-sharing.js": "^1.3.1", "serverless": "^1.56.1", "serverless-http": "^2.3.0", "stream-chat": "^5.4.0", "twilio": "^3.34.0", "uuid": "^3.3.3", "uuid-apikey": "^1.4.6", "web3": "1.7.0", "websocket": "^1.0.34"}, "devDependencies": {"mocha": "1.20.1", "rewire": "4.0.1", "supertest": "0.13.0"}}