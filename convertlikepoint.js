
module.exports = function (
	 app,
	 admin,
	 serviceAccount,
	 dotenv,
	 cryptico,
	 generator,
	 moment,
	 elasticsearch,
	 request,
	 bip39,
	 btcLib,
	 bs58check,
	 twilio,
	 crypto,
	 algorithm,
	 password,
	 algorithmToFront,
	 $log,
	 bcrypt,
	 IV_LENGTH,
	 ethers,
	 accountSid,
	 authToken,
	 clientTwilio,
	 secrets,
	 uuidv4,
	 randomstring,
	 uuidAPIKey,
	 aws,
	 ed25519,
	 mysql,
	 kms,
	 decryptToFrontInternal,
	 encrypt,
	 randomText,
	 recoverySeed,
	 encryptToFront,
	 encryptWithPassword,
	 openpgp,
	 pubkey,
	 secondary,
	 RetrievePKED2519,
	 retrieveAddress,
	 transfer
	){

app.post('/checkOld', function(req, res) {
	const _token = req.body._token;

  	admin.auth().verifyIdToken(_token)
    .then(function (decodedToken){
		const phone_number = decodedToken.phone_number;

	    request.post({ url: process.env.URL_LIKEPOINT+'/checkExistLikepoint', form: { apiKey: process.env.apiKeyLIKE, secret: process.env.secretKeyLIKE, phone_number: phone_number } }, function (err, httpResponse, body) {
	        if (!err) {
	            // res.json({ success: body, status: 200 });
	            let resultToFront = {
	                'result': 'success',
	                'type': 'checkUser',
	                'data': JSON.parse(body).result
	            };
	            res.json({ "status": 200, "result": resultToFront });
	        } else {
	            let resultToFront = {
	                'result': 'error',
	                'type': 'checkUser',
	                'data': err
	            };
	            console.log(err);
	            res.json({ "status": 404, "result": resultToFront.result });
	            // res.json({ success: err, status: 404 });
	        }
	    });	
	});
});
app.post('/retrieveToken', function(req, res){
//decrypt data
	const _token = req.body._token;
	const pass = req.body.password;
	var countPassword = pass.length;
    var curPassword = 32 - countPassword;
    const keyEncrypt = req.body.keyEncrypt;
    const ivEncrypt = req.body.ivEncrypt;
    const removeOld = req.body.removeOld;
    const db = admin.firestore();

    const dbOld = secondary.firestore();


	// console.log(_token);
	admin.auth().verifyIdToken(_token)
    	.then(function (decodedToken){
    	//	console.log(decodedToken);
    admin.auth().getUser(decodedToken.uid)
	  .then(function(userRecord) {
	    // See the UserRecord reference doc for the contents of userRecord.
	    console.log('Successfully fetched user data:', userRecord.toJSON());
    secondary.auth().getUserByPhoneNumber(userRecord.toJSON().phoneNumber)
		.then(function(userRecord) {
			//console.log(userRecord);
			console.log('retrieveToken');
    		// console.log(decodedToken);
			const phone_number = userRecord.toJSON().phoneNumber;
			console.log('here 95');
		    request.post({ url: process.env.URL_LIKEPOINT+'/retriveOldDataUser',
		     form: { 
		     	apiKey: process.env.apiKeyLIKE, 
		     	secret: process.env.secretKeyLIKE, 
		     	phone_number: userRecord.toJSON().phoneNumber, 
		     	uid: userRecord.uid
		     } 
		     }, function (err, httpResponse, body) {
		        if (!err) {

					const bodyDecode = JSON.parse(body);
					if(bodyDecode.statusCode == 200){

				console.log('here 107');
					const decrypt = decryptToFrontInternal(bodyDecode.encrypted, Buffer.from(bodyDecode.auth))
					const seed = encrypt(Buffer.from(decrypt, 'base64').toString('ascii'));
					// console.log(seed);

					var pw = seed.content;
					var pwHex = secrets.str2hex(pw);
				    let data = {
				    	_token: _token,
				    	pass: pass,
				    	countPassword: countPassword,
				    	curPassword: curPassword,
				    	wallet_number: 'no_number',
				    	seed: seed,
				    	pw: pw,
				    	pwHex: pwHex,
				    	keyEncrypt: keyEncrypt,
				    	ivEncrypt: ivEncrypt,
				    	db: db
				    }					
					convertToNewSeed(data).then(result => {
						// console.log(result);
						console.log('here 128');

						if(removeOld === 'Y'){
							console.log('Yes remove');
							res.json(result);
							//this line will be remove 
						    // request.post({ url: process.env.URL_LIKEPOINT+'/deleteOldLikepoint', form: { apiKey: process.env.apiKeyLIKE, secret: process.env.secretKeyLIKE, phone_number: phone_number } }, function (err, httpResponse, body) {
						    //     if (!err) {
						    //         // res.json({ success: body, status: 200 });
						    //         let resultToFront = {
						    //             'result': 'success',
						    //             'type': 'checkUser',
						    //             'data': JSON.parse(body)
						    //         };
						    //         res.json({ "status": 200, "result": result });
						    //     } else {
						    //         let resultToFront = {
						    //             'result': 'error',
						    //             'type': 'checkUser',
						    //             'data': err
						    //         };
						    //         console.log(err);
						    //         res.json({ "status": 404, "result": result });
						    //         // res.json({ success: err, status: 404 });
						    //     }
						    // });							
						}else{
							console.log('No remove');
							// console.log(result);
							res.json(result);
						}
					
						
					}).catch(e => {
						res.json(result);
					});
				}else{
					let resultToFront = {
		                'result': 'error',
		                'type': 'retrieveToken',
		                'data': err
		            };
		            console.log(err);
		            res.json({ "status": 404, "result": resultToFront.result });	
				}
		        } else {	
		            let resultToFront = {
		                'result': 'error',
		                'type': 'retrieveToken',
		                'data': err
		            };
		            console.log(err);
		            res.json({ "status": 404, "result": resultToFront.result });
		            // res.json({ success: err, status: 404 });
		        }
		    });	

    	}).catch(e =>{
    		console.log(e);
    	});	    
	  })
	  .catch(function(error) {
	    console.log('Error fetching user data:', error);
	  });

    });
});
app.post('/retrieveTokenNew', function(req, res){
//decrypt data
	const _token = req.body._token;
	const pass = req.body.password;
	var countPassword = pass.length;
    var curPassword = 32 - countPassword;
    const keyEncrypt = req.body.keyEncrypt;
    const ivEncrypt = req.body.ivEncrypt;
    const removeOld = req.body.removeOld;
    const db = admin.firestore();

    const dbOld = secondary.firestore();


	// console.log(_token);
	admin.auth().verifyIdToken(_token)
    	.then(function (decodedToken){
    	db.collection('users').doc(decodedToken.uid).get().then(snapshotLike =>{

            recoverySeedNoSecret(ecodedToken.uid, 'no' + snapshotLike.data()._token.concatPassword, snapshotLike.data()._token.keyFirst, snapshotLike.data()._token.keyEncrypt, snapshotLike.data()._token.tag, 'no_number').then(seedNewLike => {
					RetrievePKED2519(seedNewLike).then(pkNewLike =>{
						let addressLike = retrieveAddress(pkNewLike);
		                var contractAddress = process.env.CONTRACT_LIKE;
		                var abi = JSON.parse(process.env.ABI_LIKE);
		                console.log(contractAddress);
		                console.log(abi);
		                console.log(addressLike);
						
						let url = process.env.NETWORK_RPC;
						var web3Provider = new ethers.providers.JsonRpcProvider(url);
				        // let currentProvider = new web3.providers.HttpProvider(process.env.NETWORK_RPC);
					    // let web3Provider = new ethers.providers.Web3Provider(currentProvider);
			

						// We connect to the Contract using a Provider, so we will only
						// have read-only access to the Contract
						let contract = new ethers.Contract(contractAddress, abi, web3Provider);

						let currentValue = contract.balanceOf(addressLike).then(result => {
							let balanceLike = result;
							

							admin.auth().getUser(decodedToken.uid)
							  .then(function(userRecord) {
							    // See the UserRecord reference doc for the contents of userRecord.
								    //console.log('Successfully fetched user data:', userRecord.toJSON());
							    secondary.auth().getUserByPhoneNumber(userRecord.toJSON().phoneNumber)
									.then(function(userRecord) {
										//console.log(userRecord);
							    		// console.log(decodedToken);
										const phone_number = userRecord.toJSON().phoneNumber;
										console.log('here 95');
									    request.post({ url: process.env.URL_LIKEPOINT+'/retriveOldDataUser',
									     form: { 
									     	apiKey: process.env.apiKeyLIKE, 
									     	secret: process.env.secretKeyLIKE, 
									     	phone_number: userRecord.toJSON().phoneNumber, 
									     	uid: userRecord.uid
									     } 
									     }, function (err, httpResponse, body) {
									        if (!err) {

												const bodyDecode = JSON.parse(body);
												if(bodyDecode.statusCode == 200){

											console.log('here 107');
												const decrypt = decryptToFrontInternal(bodyDecode.encrypted, Buffer.from(bodyDecode.auth))
												const seed = encrypt(Buffer.from(decrypt, 'base64').toString('ascii'));
												const seedX = Buffer.from(decrypt, 'base64').toString('ascii');
												RetrievePKED2519(seedX).then(pkOld =>{

 													const addressConvert = retrieveAddress(pkOld);
 													transfer(addressConvert, pkNewLike, new ethers.utils.parseEther(balanceLike.toString(), 'wei')).then(tx =>{
														var pw = seed.content;
														var pwHex = secrets.str2hex(pw);
													    let data = {
													    	_token: _token,
													    	pass: pass,
													    	countPassword: countPassword,
													    	curPassword: curPassword,
													    	wallet_number: 'no_number',
													    	seed: seed,
													    	pw: pw,
													    	pwHex: pwHex,
													    	keyEncrypt: keyEncrypt,
													    	ivEncrypt: ivEncrypt,
													    	db: db
													    }					
														convertToNewSeed(data).then(result => {
															// console.log(result);
															console.log('here 128');

															if(removeOld === 'Y'){
																console.log('Yes remove');
																res.json(result);
																//this line will be remove 
															    // request.post({ url: process.env.URL_LIKEPOINT+'/deleteOldLikepoint', form: { apiKey: process.env.apiKeyLIKE, secret: process.env.secretKeyLIKE, phone_number: phone_number } }, function (err, httpResponse, body) {
															    //     if (!err) {
															    //         // res.json({ success: body, status: 200 });
															    //         let resultToFront = {
															    //             'result': 'success',
															    //             'type': 'checkUser',
															    //             'data': JSON.parse(body)
															    //         };
															    //         res.json({ "status": 200, "result": result });
															    //     } else {
															    //         let resultToFront = {
															    //             'result': 'error',
															    //             'type': 'checkUser',
															    //             'data': err
															    //         };
															    //         console.log(err);
															    //         res.json({ "status": 404, "result": result });
															    //         // res.json({ success: err, status: 404 });
															    //     }
															    // });							
															}else{
																console.log('No remove');
																// console.log(result);
																res.json(result);
															}
														
															
														}).catch(e => {
															res.json(result);
														});
 													});

												});


											}else{
												let resultToFront = {
									                'result': 'error',
									                'type': 'retrieveToken',
									                'data': err
									            };
									            console.log(err);
									            res.json({ "status": 404, "result": resultToFront.result });	
											}
									        } else {	
									            let resultToFront = {
									                'result': 'error',
									                'type': 'retrieveToken',
									                'data': err
									            };
									            console.log(err);
									            res.json({ "status": 404, "result": resultToFront.result });
									            // res.json({ success: err, status: 404 });
									        }
									    });	

							    	}).catch(e =>{
							    		console.log(e);
							    	});	    
								  })
								  .catch(function(error) {
								    console.log('Error fetching user data:', error);
								  });		
							
							})
				    });							
		               
						})						
					});

    	});

});

function moveBackup(data) {
	return new Promise((resolve, reject) => {
		const cyphermines = data.db.collection('cyphermines').doc(data.uid);
		const disabledCyphermines = data.db.collection('disabledCyphermines');
   		const users = data.db.collection('users').doc(data.uid);
      	const disabledUsers = data.db.collection('disabledUsers');
		const masterData = data.db.collection('masterKeyEncrypted').doc(data.uid);      
		const disabledMasterData = data.db.collection('disabledMasterData');      	
		cyphermines.get()
		  .then(doc => {
		    if (!doc.exists) {
		      console.log('No such document!');
		    } else {
		      // console.log('Document data:', doc.data());
		      let dataCypher = doc.data();
		      dataCypher.uid = data.uid;
		      disabledCyphermines.add(dataCypher).then(result => {
	  			users.get()
			      .then(doc2 => {
			      	let dataUser = doc2.data();
		     	 	dataUser.uid = data.uid;
			      	disabledUsers.add(dataUser).then(result2 => {
				      	masterData.get()
				      	.then(doc3 =>{
					      	let dataMaster = doc3.data();
				     	 	dataMaster.uid = data.uid;				      		
				      		console.log(doc3);
				      		disabledMasterData.add(dataMaster).then(result3 =>{
				      			resolve(true);
				      		});
				      	}).catch(err=>{
				      		$log(err);
				      		return resolve(moveBackup(data));
				      	})
			      	}).catch(err =>{
			      		$log(err);
			      		return resolve(moveBackup(data));
			      	});
			      }).catch(err => {
			      	$log(err);
			      	 return resolve(moveBackup(data));
			      })		      	
		      }).catch(err =>{
		      	$log(err);
		      	return resolve(moveBackup(data));
		      });
		
		    }
		  })
		  .catch(err => {
		  	$log(err);
		    console.log('Error getting document', err);
		  });		

	});
}

//this function convert likepoint seed to likewallet seed
function convertToNewSeed(data){
  return new Promise((resolve, reject) => {
    admin.auth().verifyIdToken(data._token)
      .then(function (userDetail) {
      	let xData = {
      		db: data.db,
      		uid: userDetail.uid
      	};
      	moveBackup(xData).then(result => {
      		var numResult;
      		if(result){
      			console.log('moveBackup success');
		         randomText(data.curPassword).then(passConcat => {
		   
		          //แบ่ง seed เป็น 4 ส่วน ปลดล็อคด้วย 3 ใน 4
		          	var shares = secrets.share(data.pwHex, 4, 3);
		          //คีย์ ที่ 1
		          	var userNoencrypt = shares[0];
		          // key ที่ 1 เข้ารหัสด้วย secret ลับของผู้ใช้เอง
		          	var userEncrypt = encryptWithPassword(shares[1], data.curPassword > 0 ? data.pass + passConcat : data.pass);
		          // console.log('userEncrypt :', userEncrypt);
		          //cyphermines เก็บ
		          	var cypherminesKeep = shares[2];
		          //master key ดอกที่ 4
		          	var masterKey = shares[3];
		  			var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789$&!?';
					var charactersLength = characters.length;
					for (var i = 0; i < 9; i++) {
						numResult += characters.charAt(Math.floor(Math.random() * charactersLength));
					}             	
					console.log('before updateUser');
		                      //save password to db

		                        const users = data.db.collection('users').doc(userDetail.uid);
		                        const conPass = passConcat.length > 0 ? data.pass+passConcat : data.pass;

		                        users.update({
		                          _token: {
		                            keyFirst: userNoencrypt,
		                            keyEncrypt: userEncrypt,
		                            tag: data.seed.tag,
		                            concatPassword: passConcat
		                          }
		                        }).then(d => {
		                  
		                          const cyphermines = data.db.collection('cyphermines').doc(userDetail.uid);

		                          cyphermines.set({
		                            key: cypherminesKeep
		                          }).then(x => {
		                          //keep key 4 
		                          //masterKey
		                          const encryptDecryptFunction = async() => {
		                              const options = {
		                                  message: openpgp.message.fromText(masterKey),       // input as Message object
		                                  publicKeys: (await openpgp.key.readArmored(pubkey)).keys, // for encryption
		                                  // privateKeys: [privKeyObj]                                 // for signing (optional)
		                              }
		                           
		                              openpgp.encrypt(options).then(ciphertext => {
		                                  var encrypted = ciphertext.data // '-----BEGIN PGP MESSAGE ... END PGP MESSAGE-----'
		  
		                                  const masterData = data.db.collection('masterKeyEncrypted').doc(userDetail.uid);                    
		                                    masterData.set({
		                                      key: encrypted
		                                    }).then(uidMaster => {
		                                      //generate seed
		                                        recoverySeed(userDetail.uid, conPass, userNoencrypt, userEncrypt, data.seed.tag, data.wallet_number).then(seed => {
		                                          console.log('here ercovery');
		                                           let result = {
		                                            statusCode: 200,
		                                            result: 'Successfully created new user',
		                                            seed: encryptToFront(seed, data.keyEncrypt, data.ivEncrypt)
		                                          }
		                                          resolve(result);                                         
		                                        });
		                                      //end generate seed    
		                                      }).catch(err => {
		                                        console.log(err);
		                                        reject(false);
		                                      }); 
		                              })
		                            };
		                          const masterKeyPGP = encryptDecryptFunction();

		         
		                                       
		                       
		  
		                          })
		  
		                    });
		  
		        });	//randomText	     			
			}//if
      	});
	

      });
  });
}
};