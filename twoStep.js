'use strict';


const Utils = require('./utils');
const admin = require("firebase-admin");
const twofactor = require("node-2fa");



const checkTwoFA = function(token) {
	return new Promise((resolve, reject) => {
		Utils.verifyToken(token).then(async (callback) => {
			const decodedToken = callback;
			const uid = decodedToken.uid;
			const db = admin.firestore();

			const user = await db.collection('users').doc(uid).get();

			if(user.data().isTwoStep == true) {
				let callback = {
					result: true,
					newSecret: {
						secret:'',
						uri:'',
						qr:''
					}
				}
				resolve(callback);
			}else {
				// await db.collection('users').doc(uid).update({isTwoStep: false});
				const newSecret = twofactor.generateSecret({ name: 'LikeWallet', account: decodedToken.uid });
				let callback = {
					result: false,
					newSecret: newSecret
				}

				await db.collection('twofa_settings').doc(uid).set({
					secret: newSecret,
					status: 0
				});

				resolve(callback);
			}
		}).catch(e=>{
			reject({result: false});
			console.log(e);
		})
	})
}

const enableTwoFA = function(token, secret, otp) {
	return new Promise((resolve, reject) => {
		Utils.verifyToken(token).then(async (callback) => {
			const decodedToken = callback;
			const uid = decodedToken.uid;
			const db = admin.firestore();

			const user2FA = await db.collection('twofa_settings').doc(uid).get();

			if(user2FA.data().secret.secret != undefined) {
				const secret = user2FA.data().secret.secret;
				const result = await twofactor.verifyToken(secret, otp);
				let callback;
				if(result == null) {
					callback = {
						result: false,
						note:'wrong otp'
					};
					resolve(callback);
				}else if (result.delta.toString() == '0'){
					await db.collection('users').doc(uid).update({isTwoStep: true});
					await db.collection('twofa_settings').doc(uid).update({status:1});

					callback = {
						result: true
					}
					resolve(callback);
				}else {
					callback = {
						result: false,
						note:'wrong otp'
					};
					resolve(callback);	
				}
			}else {
				reject();
			}

		}).catch(e=>{
			reject({result: false});
			console.log(e);
		});
	});
}

const disableTwoFA = function(token, secret, otp) {
	return new Promise((resolve, reject) => {
		Utils.verifyToken(token).then(async (callback) => {
			const decodedToken = callback;
			const uid = decodedToken.uid;
			const db = admin.firestore();

			const user2FA = await db.collection('twofa_settings').doc(uid).get();

			if(user2FA.data().secret.secret != undefined) {
				const secret = user2FA.data().secret.secret;
				const result = await twofactor.verifyToken(secret, otp);
				let callback;
				if(result == null) {
					callback = {
						result: false,
						note:'wrong otp'
					};
					resolve(callback);
				}else if (result.delta.toString() == '0'){
					await db.collection('users').doc(uid).update({isTwoStep: false});
					await db.collection('twofa_settings').doc(uid).update({status:0});

					callback = {
						result: true
					}
					resolve(callback);
				}else {
					callback = {
						result: false,
						note:'wrong otp'
					};
					resolve(callback);	
				}
			}else {
				reject();
			}

		}).catch(e=>{
			reject({result: false});
			console.log(e);
		});
	});
}


const verifyOTP = function(token, secret, otp) {
	return new Promise((resolve, reject) => {
		Utils.verifyToken(token).then(async (callback) => {
			const decodedToken = callback;
			const uid = decodedToken.uid;
			const db = admin.firestore();

			const user2FA = await db.collection('twofa_settings').doc(uid).get();

			if(user2FA.data().secret.secret != undefined) {
				const secret = user2FA.data().secret.secret;
				const result = await twofactor.verifyToken(secret, otp);
				let callback;
				if(result == null) {
					callback = {
						result: false,
						note:'wrong otp'
					};
					resolve(callback);
				}else if (result.delta.toString() == '0'){

					callback = {
						result: true
					}
					resolve(callback);
				}else {
					callback = {
						result: false,
						note:'wrong otp'
					};
					resolve(callback);	
				}
			}else {
				reject();
			}

		}).catch(e=>{
			reject({result: false});
			console.log(e);
		});
	});
}

module.exports = {
	checkTwoFA,
	enableTwoFA,
	disableTwoFA,
	verifyOTP
}