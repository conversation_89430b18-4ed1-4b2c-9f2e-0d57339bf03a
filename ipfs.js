const ipfsCluster = require('ipfs-cluster-api-remove-name')
const express = require('express');
const fs = require('fs');
const app = express();
"use strict";
const ipfsClient = require('ipfs-http-client')
// /ip4/***************/tcp/4001/p2p/QmVDWypLBEwrMxusDpQn451meBDAVbXXJD5qEYyCwXioak
// connect to ipfs daemon API server
const ipfs = ipfsClient('http://***************') // (the default in Node.js)
const identity = ipfs.id().then((data)=>{
	console.log(data);
})

const cluster = ipfsCluster('***************', '9094', { protocol: 'http' }) // leaving out the arguments will default to these values

//Reading file from computer
let testFile = fs.readFileSync("./testdd.txt");
//Creating buffer for ipfs function to add file to the system
let testBuffer = new Buffer(testFile);

//Addfile router for adding file a local file to the IPFS network without any local node
app.get('/addfile', function(req, res) {

cluster.add(testBuffer, (err, result) => {
  err ? console.error(err) : console.log(result)
})

})

let getFile = async function() {
	const BufferList = require('bl/BufferList')
	const cid = 'QmVFMEcXtJsCuRyc9XymhvFZQn57ynn6VSaFnNfut2Thox'

	for await (const file of ipfs.get(cid)) {
	  console.log(file.path)

	  const content = new BufferList()
	  for await (const chunk of file.content) {
	    content.append(chunk)
		fs.appendFile('data.txt', chunk, (err) => {
		  if (err) throw err;
		  console.log('The "data to append" was appended to file!');
		});			    
	  }

	  // console.log(content.toString())
	}

}
getFile();
//Getting the uploaded file via hash code.
app.get('/getfile', function(req, res) {
    // QmddchiYMQGZYLZf86jhyhkxRqrGfpBNr53b4oiV76q6aq
//get file form cid in cluster or ipfs


})

app.listen(10020, () => console.log('App listening on port 10020!'))