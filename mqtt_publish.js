

module.exports.publishToKPLUS = function() {
	return new Promise((resolve, reject)=>{
	var mqtt = require('mqtt')
	var client  = mqtt.connect("mqtt://mosquitto.likepoint.io");
	console.log('publishToKPLUS');
	client.on('connect', function () {
    client.publish('withdrawThai', 'withdrawThai')

    client.end();
    resolve();
	})
	});

}


module.exports.publishTransferBankBranchAAM = function() {
	return new Promise((resolve, reject)=>{
	var mqtt = require('mqtt')
	var client  = mqtt.connect("mqtt://mosquitto.likepoint.io");
	console.log('publishToKPLUS');
	client.on('connect', function () {
    client.publish('transferBankBranchAAM', 'transferBankBranchAAM')

    client.end();
    resolve();
	})
	});

}

module.exports.publishTransferBankBranchPMS = function() {
	return new Promise((resolve, reject)=>{
	var mqtt = require('mqtt')
	var client  = mqtt.connect("mqtt://mosquitto.likepoint.io");
	console.log('publishToKPLUS');
	client.on('connect', function () {
    client.publish('transferBankBranchPMS', 'transferBankBranchPMS')

    client.end();
    resolve();
	})
	});

}


module.exports.publishTransferBankBranchPMG = function() {
	return new Promise((resolve, reject)=>{
	var mqtt = require('mqtt')
	var client  = mqtt.connect("mqtt://mosquitto.likepoint.io");
	console.log('publishToKPLUS');
	client.on('connect', function () {
    client.publish('transferBankBranchPMG', 'transferBankBranchPMG')

    client.end();
    resolve();
	})
	});

}

module.exports.publishTransferBankBranchPCC = function() {
	return new Promise((resolve, reject)=>{
	var mqtt = require('mqtt')
	var client  = mqtt.connect("mqtt://mosquitto.likepoint.io");
	console.log('publishToKPLUS');
	client.on('connect', function () {
    client.publish('transferBankBranchPCC', 'transferBankBranchPCC')

    client.end();
    resolve();
	})
	});

}

module.exports.publishTransferBankBranchAMH = function() {
	return new Promise((resolve, reject)=>{
	var mqtt = require('mqtt')
	var client  = mqtt.connect("mqtt://mosquitto.likepoint.io");
	console.log('publishToKPLUS');
	client.on('connect', function () {
    client.publish('transferBankBranchAMH', 'transferBankBranchAMH')

    client.end();
    resolve();
	})
	});

}

module.exports.publishTransferBankBranchPBP = function() {
	return new Promise((resolve, reject)=>{
	var mqtt = require('mqtt')
	var client  = mqtt.connect("mqtt://mosquitto.likepoint.io");
	console.log('publishToKPLUS');
	client.on('connect', function () {
    client.publish('transferBankBranchPBP', 'transferBankBranchPBP')

    client.end();
    resolve();
	})
	});

}