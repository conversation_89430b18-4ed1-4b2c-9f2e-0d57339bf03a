const atob = require('atob');
const {address} = require("bitcoinjs-lib");
module.exports = function (
    app,
    admin,
    serviceAccount,
    dotenv,
    cryptico,
    generator,
    moment,
    elasticsearch,
    request,
    bip39,
    btcLib,
    bs58check,
    twilio,
    crypto,
    algorithm,
    password,
    algorithmToFront,
    $log,
    bcrypt,
    IV_LENGTH,
    ethers,
    accountSid,
    authToken,
    clientTwilio,
    secrets,
    uuidv4,
    randomstring,
    uuidAPIKey,
    aws,
    ed25519,
    mysql,
    kms,
    decryptToFrontInternal,
    encrypt,
    randomText,
    recoverySeed,
    encryptToFront,
    encryptWithPassword,
    openpgp,
    pubkey,
    secondary,
    selectMysql,
    web3,
    auth,
    decrypt,
    decryptAdmin,
    RetrievePKED2519,
    recoverySeedNoSecretDisabledActive,
    likeUtils,
    selectMysqlOpenAPI
) {

    const fiat = ["VND", "LAK", "USD"];
    let sleep = (time) => new Promise((resolve) => setTimeout(resolve, time))

    function requestExchangeRate(options, i) {
        return new Promise((resolve, reject) => {
            request(options, function (error, response, body) {
                if (error) return console.error('Failed: %s', error.message);
                let currencyTHBtoUSD = JSON.parse(body).result;
                console.log('Success: ', body);
                const db = admin.firestore();
                console.log(fiat[i]);
                const THB = db.collection('exchangeFiat').doc('THB-' + fiat[i]);
                //   console.log(fiat[i]);
                //   console.log(1/currencyTHBtoUSD.data.data_detail[0].selling);
                //   if(fiat[i]==='USD'){
                //   						request('https://metals-api.com/api/latest?access_key=2u84986is3f326iscdscy09e6v4asf6c3t59d400uqn6htp2x0b7h493odg5z46t&base=USD&symbols=XAU', function (error, response, body) {
                // 	  console.log('error:', error); // Print the error if one occurred
                // 	  console.log('statusCode:', response && response.statusCode); // Print the response status code if a response was received
                // 	  console.log('body:', body); // Print the HTML for the Google homepage.
                // 	  let gold = JSON.parse(body);
                // 	  console.log(gold.rates.XAU/28.3495*currencyTHBtoUSD.data.data_detail[0].selling);
                // 	});
                //   }

                // resolve(true);

                THB.set({
                    'main': 'THB',
                    'to': fiat[i],
                    'rate': 1 / currencyTHBtoUSD.data.data_detail[0].selling,
                    'updated': moment().format()
                }).then(data => {
                    if (fiat[i] === 'USD') {
                        request('https://metals-api.com/api/latest?access_key=2u84986is3f326iscdscy09e6v4asf6c3t59d400uqn6htp2x0b7h493odg5z46t&base=USD&symbols=XAU', function (error, response, body) {
                            console.log('error:', error); // Print the error if one occurred
                            console.log('statusCode:', response && response.statusCode); // Print the response status code if a response was received
                            console.log('body:', body); // Print the HTML for the Google homepage.
                            let gold = JSON.parse(body);
                            const db = admin.firestore();
                            const goldRoger = db.collection('exchangeFiat').doc('THB-GOLD');
                            //change gold oz to grams and update
                            goldRoger.set({
                                'main': 'THB',
                                'to': 'GOLD',
                                'rate': gold.rates.XAU / 28.3495 * currencyTHBtoUSD.data.data_detail[0].selling,
                                'updated': moment().format()
                            }).then(data => {
                                resolve(true);
                            });
                        });
                    } else {
                        resolve(true);
                    }
                });


            });
        });
    }

    function signInWithTokenByWeb(token) {
        return new Promise((resolve, reject) => {
            request({
                method: 'POST',
                uri: 'https://new.likepoint.io/signInWithTokenByWeb',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                form: {
                    token: token
                }
            }, (err, httpResponse, body) => {
                if (err) {
                    console.log(err);
                } else {
                    // console.log(body);
                    return resolve(JSON.parse(body));
                }
            });
        })
    }

    function randomIntFromInterval(min, max) {
        // min and max included
        console.log("interval");
        return Math.floor(Math.random() * (max - min + 1) + min);
    };

    app.post('/buyticket', async function (req, res) {

        var url = process.env.NETWORK_RPC;
        // var url_parts = url.parse(req.url, true);
        let hdPath = "m/44'/60'/0'/0/0";
        var apiKey = req.body.apiKey;
        var secretKey = req.body.secretKey;
        var token = req.body.token;
        let tricketNumber = [];
        let ticketUser = 0;

        if (process.env.APIKEY === apiKey && process.env.SECRETKEY === secretKey) {
            var response = await signInWithTokenByWeb(token);
            var finalData = response.seed.replace(/\\/g, "");
            var mnemonic = atob(finalData);
            // console.log(mnemonic);
            let provider = new ethers.providers.JsonRpcProvider(url);
            //    console.log(provider);

            let walletMnemonic = ethers.Wallet.fromMnemonic(
                mnemonic,
                "m/44'/60'/0'/0/0"
            );
            //   console.log(walletMnemonic);

            let wallet = new ethers.Wallet(walletMnemonic.privateKey).connect(
                provider
            );


            const lottery = new ethers.Contract(process.env.CONTRACT_LOTTO, process.env.ABI_LOTTO, wallet);
            const c_erc20 = new ethers.Contract(process.env.CONTRACT_LIKE, process.env.ABI_LIKE, wallet);
            const c_lock = new ethers.Contract(process.env.CONTRACT_LOCK_LOTTO, process.env.ABI_LOCK_LOTTO, wallet);
            // console.log(lottery);
            //   console.log(c_lock);

            lottery.issueIndex().then(async (issueIndex) => {
                // console.log(parseInt(issueIndex.toString()));
                c_lock.getAmountLottery(wallet.address).then((getTicket) => {

                    if (parseInt(getTicket.toString()) > 0) {
                        lottery.countTicket(wallet.address, issueIndex).then((ticket) => {
                            if (parseInt(ticket.leftTicket.toString()) > 0) {
                                ticketUser = parseInt(ticket.leftTicket.toString());
                            } else {
                                if (parseInt(ticket.initTicket.toString()) > 0) {
                                    ticketUser = 0;
                                } else {
                                    ticketUser = parseInt(getTicket.toString());
                                }
                            }
                            // console.log(parseInt(ticketUser.toString()));
                            if (ticketUser > 0) {
                                c_erc20.balanceOf(wallet.address).then((walletBalance) => {
                                    //   console.log(walletBalance / 10e17);

                                    if (walletBalance / 10e17 >= parseInt(ticketUser.toString())) {
                                        this.messageLoading = "กำลังโหลดข้อมูล";
                                        for (let i = 0; i < parseInt(ticketUser.toString()); i++) {
                                            let number1 = randomIntFromInterval(1, 9);
                                            let number2 = randomIntFromInterval(1, 9);
                                            let number3 = randomIntFromInterval(1, 9);
                                            let number4 = randomIntFromInterval(1, 9);
                                            let x = [number1, number2, number3, number4];
                                            tricketNumber.push(x);
                                        }
                                        // res.json({statusCode:200, result:'SUCCESS',tx:'tx'});
                                        lottery
                                            .multiBuyLock(
                                                ethers.utils.parseEther("1"),
                                                tricketNumber,
                                                {gasLimit: 30000000}
                                            )
                                            .then((tx) => {
                                                res.json({statusCode: 200, result: 'SUCCESS', tx: tx});
                                            });
                                    } else {
                                        //alert('คุณต้องมี Like ขั้นต่ำในกระเป๋า มากกว่า' + this.amountBuy + ' LIKE');
                                        res.json({statusCode: 200, result: 'NEED_LIKEPOINT_IN_WALLET'});
                                    }
                                });
                            } else {
                                res.json({statusCode: 200, result: 'USED_TICKET'});
                            }
                        });
                    } else {
                        console.log('NO_TICKET');
                        res.json({statusCode: 200, result: 'NO_TICKET'});
                    }
                });
            });
            //   return


            //   console.log(wallet.address);

            //   console.log("wallet"+wallet);
            // //   const c_lock = new ethers.Contract(process.env.CONTRACT_LOCK_LOTTO, process.env.ABI_LOCK_LOTTO, wallet);
            // const lottery = new ethers.Contract(
            // 		process.env.CONTRACT_LOCK_LOTTO,
            // 		process.env.ABI_LOTTO,
            // 		wallet
            // 	  );
            //   const c_erc20 = new ethers.Contract(
            // 	process.env.CONTRACT_LIKE,
            // 	process.env.ABI_LIKE,
            // 	wallet
            //   );
            //   console.log(c_erc20);
            //
            //   const c_NFT = new ethers.Contract(process.env.CONTRACT_NFT,process.env.ABI_NFT, wallet);


        } else {
            res.json({statusCode: 404, result: 'Unauthorized'});
        }
        // res.json({statusCode:200, result:'successfuly'});
    });
    app.post('/chooseBuyTicket', async function (req, res) {
 console.log('chooseBuyTicket');
        var url = process.env.NETWORK_RPC;
        // var url_parts = url.parse(req.url, true);
        let hdPath = "m/44'/60'/0'/0/0";
        var apiKey = req.body.apiKey;
        var secretKey = req.body.secretKey;
        var token = req.body.token;
        let tricketNumber = JSON.parse(req.body.tokenList);
        let ticketUser = 0;
        console.log(tricketNumber);
        if (process.env.APIKEY === apiKey && process.env.SECRETKEY === secretKey) {
            var response = await signInWithTokenByWeb(token);
            var finalData = response.seed.replace(/\\/g, "");
            var mnemonic = atob(finalData);
            // console.log(mnemonic);
            let provider = new ethers.providers.JsonRpcProvider(url);
            //    console.log(provider);

            let walletMnemonic = ethers.Wallet.fromMnemonic(
                mnemonic,
                "m/44'/60'/0'/0/0"
            );
            //   console.log(walletMnemonic);

            let wallet = new ethers.Wallet(walletMnemonic.privateKey).connect(
                provider
            );


            const lottery = new ethers.Contract(process.env.CONTRACT_LOTTO, process.env.ABI_LOTTO, wallet);
            const c_erc20 = new ethers.Contract(process.env.CONTRACT_LIKE, process.env.ABI_LIKE, wallet);
            const c_lock = new ethers.Contract(process.env.CONTRACT_LOCK_LOTTO, process.env.ABI_LOCK_LOTTO, wallet);
            // console.log(lottery);
            //   console.log(c_lock);

            lottery.issueIndex().then(async (issueIndex) => {
                // console.log(parseInt(issueIndex.toString()));
                c_lock.getAmountLottery(wallet.address).then((getTicket) => {

                    if (parseInt(getTicket.toString()) > 0) {
                        lottery.countTicket(wallet.address, issueIndex).then((ticket) => {
                            if (parseInt(ticket.leftTicket.toString()) > 0) {
                                ticketUser = parseInt(ticket.leftTicket.toString());
                            } else {
                                if (parseInt(ticket.initTicket.toString()) > 0) {
                                    ticketUser = 0;
                                } else {
                                    ticketUser = parseInt(getTicket.toString());
                                }
                            }
                            // console.log(parseInt(ticketUser.toString()));
                            if (ticketUser > 0) {
                                c_erc20.balanceOf(wallet.address).then((walletBalance) => {
                                    //   console.log(walletBalance / 10e17);

                                    if (walletBalance / 10e17 >= parseInt(ticketUser.toString())) {
                                        this.messageLoading = "กำลังโหลดข้อมูล";
                                        // for (let i = 0; i < parseInt(ticketUser.toString()); i++) {
                                        //   let number1 = randomIntFromInterval(1, 9);
                                        //   let number2 = randomIntFromInterval(1, 9);
                                        //   let number3 = randomIntFromInterval(1, 9);
                                        //   let number4 = randomIntFromInterval(1, 9);
                                        //   let x = [number1, number2, number3, number4];
                                        //   tricketNumber.push(x);
                                        // }
                                        // res.json({statusCode:200, result:'SUCCESS',tx:'tx'});
                                        lottery
                                            .multiBuyLock(
                                                ethers.utils.parseEther("1"),
                                                tricketNumber,
                                                {gasLimit: 30000000}
                                            )
                                            .then((tx) => {
                                                let response = {statusCode: 200, result: 'SUCCESS'};
                                                if (tx) {
                                                    response.tx = tx;
                                                    res.json(response);
                                                } else {
                                                    response.result = 'NEED_LIKEPOINT_IN_WALLET';
                                                    res.json(response);
                                                }
                                            });
                                    } else {
                                        //alert('คุณต้องมี Like ขั้นต่ำในกระเป๋า มากกว่า' + this.amountBuy + ' LIKE');
                                        res.json({statusCode: 200, result: 'NEED_LIKEPOINT_IN_WALLET'});
                                    }
                                });
                            } else {
                                res.json({statusCode: 200, result: 'USED_TICKET'});
                            }
                        });
                    } else {
                        console.log('NO_TICKET');
                        res.json({statusCode: 200, result: 'NO_TICKET'});
                    }
                });
            });
            //   return


            //   console.log(wallet.address);

            //   console.log("wallet"+wallet);
            // //   const c_lock = new ethers.Contract(process.env.CONTRACT_LOCK_LOTTO, process.env.ABI_LOCK_LOTTO, wallet);
            // const lottery = new ethers.Contract(
            // 		process.env.CONTRACT_LOCK_LOTTO,
            // 		process.env.ABI_LOTTO,
            // 		wallet
            // 	  );
            //   const c_erc20 = new ethers.Contract(
            // 	process.env.CONTRACT_LIKE,
            // 	process.env.ABI_LIKE,
            // 	wallet
            //   );
            //   console.log(c_erc20);
            //
            //   const c_NFT = new ethers.Contract(process.env.CONTRACT_NFT,process.env.ABI_NFT, wallet);


        } else {
            res.json({statusCode: 404, result: 'Unauthorized'});
        }
        // res.json({statusCode:200, result:'successfuly'});
    });

    app.post('/approveBuy', async function (req, res) {
        var token = req.body.token
        var url = process.env.NETWORK_RPC;
        // var url_parts = url.parse(req.url, true);
        let hdPath = "m/44'/60'/0'/0/0";
        var apiKey = req.body.apiKey;
        var secretKey = req.body.secretKey;
        var token = req.body.token
        if (process.env.APIKEY === apiKey && process.env.SECRETKEY === secretKey) {
            var response = await signInWithTokenByWeb(token);
            var finalData = response.seed.replace(/\\/g, "");
            var mnemonic = atob(finalData);
            let provider = new ethers.providers.JsonRpcProvider(url);
            let walletMnemonic = ethers.Wallet.fromMnemonic(
                mnemonic,
                "m/44'/60'/0'/0/0"
            );
            let wallet = new ethers.Wallet(walletMnemonic.privateKey).connect(
                provider
            );
            let address = process.env.CONTRACT_LOTTO;
            const c_erc20 = new ethers.Contract(process.env.CONTRACT_LIKE, process.env.ABI_LIKE, wallet);

            c_erc20
                .allowance(wallet.address, process.env.CONTRACT_LOTTO)
                .then(async (allowance) => {
                    // console.log(allowance / 10e17);

                    if ((allowance / 10e17) > 0) {
                        res.json({statusCode: 200, result: 'ALREADY_APPROVE'});
                    } else {
                        let rawTransaction = await c_erc20.populateTransaction
                            .approve(
                                address,
                                "0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"
                            );
                        console.log(rawTransaction);
                        await sleep(4000);
                        let data = await c_erc20
                            .approve(
                                address,
                                "0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff",
                            );
                        console.log(data);
                        if (rawTransaction && data.hash) {
                            res.json({statusCode: 200, result: 'APPROVED'});
                        }
                    }
                });

        } else {
            res.json({statusCode: 404, result: 'Unauthorized'});
        }
    });

    app.post('/checkMultiClaim', async function (req, res) {
        try {
            var url = process.env.NETWORK_RPC;
            var multiClaim = false;
            // var url_parts = url.parse(req.url, true);
            let hdPath = "m/44'/60'/0'/0/0";
            var apiKey = req.body.apiKey;
            var secretKey = req.body.secretKey;
            var token = req.body.token
            const tokenNumber = JSON.parse(req.body.tokenNumber);
            if (process.env.APIKEY === apiKey && process.env.SECRETKEY === secretKey) {
                var response =
                    await signInWithTokenByWeb(token);
                var finalData = response.seed.replace(/\\/g, "");
                var mnemonic = atob(finalData);
                let provider = new ethers.providers.JsonRpcProvider(url);
                let walletMnemonic = ethers.Wallet.fromMnemonic(
                    mnemonic,
                    "m/44'/60'/0'/0/0"
                );
                let wallet = new ethers.Wallet(walletMnemonic.privateKey).connect(
                    provider
                );

                const c_NFT = new ethers.Contract(process.env.CONTRACT_NFT, process.env.ABI_NFT, wallet);

                for (let i = 0; i < tokenNumber.length; i++) {
                    let claimed = await c_NFT
                        .getClaimStatus(tokenNumber[i].tokendId);
                    console.log(tokenNumber[i].tokendId + " " + claimed + " " + tokenNumber[i].reward);
                    if (claimed) {
                        tokenNumber[i].reward = 0;
                        // multiClaim = false;
                    }
                    if (tokenNumber[i].reward > 0) {
                        // console.log("multi true");
                        multiClaim = true;
                    }
                }
                res.json({statusCode: 200, result: multiClaim});
            } else {
                res.json({statusCode: 404, result: 'Unauthorized'});
            }
        } catch (e) {
            res.json({statusCode: 403, result: e});
        }
    });

    app.post('/multipleClaimButton', async function (req, res) {
        try {
            var url = process.env.NETWORK_RPC;
            let hdPath = "m/44'/60'/0'/0/0";
            var apiKey = req.body.apiKey;
            var secretKey = req.body.secretKey;
            var token = req.body.token
            const tokenList = JSON.parse(req.body.tokenList);
            if (process.env.APIKEY === apiKey && process.env.SECRETKEY === secretKey) {
                var response =
                    await signInWithTokenByWeb(token);
                var finalData = response.seed.replace(/\\/g, "");
                var mnemonic = atob(finalData);
                let provider = new ethers.providers.JsonRpcProvider(url);
                let walletMnemonic = ethers.Wallet.fromMnemonic(
                    mnemonic,
                    "m/44'/60'/0'/0/0"
                );
                let wallet = new ethers.Wallet(walletMnemonic.privateKey).connect(
                    provider
                );

                const lottery = new ethers.Contract(process.env.CONTRACT_LOTTO, process.env.ABI_LOTTO, wallet);

                lottery
                    .multiClaim(tokenList, {gasPrice: 10e9, gasLimit: 1000000})
                    .then((tx) => {
                        console.log(tx);
                        console.log('claim success');
                        if (tx) {
                            res.json({statusCode: 200, result: tx});
                        } else {
                            res.json({statusCode: 404, result: 'Error'});
                        }
                    });

            } else {
                res.json({statusCode: 404, result: 'Unauthorized'});
            }
        } catch (e) {
            res.json({statusCode: 403, result: e});
        }
    });

    app.get('/updateCurrency', function (req, res) {
        var url = require('url');
        var url_parts = url.parse(req.url, true);
        var query = url_parts.query;
        var apiKey = req.query.apiKey;
        var secretKey = req.query.secretKey;
        if (process.env.APIKEY === apiKey && process.env.SECRETKEY) {

            let mainExchange = async function () {
                for (let i = 0; i < fiat.length; i++) {

                    var options = {
                        method: 'GET',
                        url: 'https://apigw1.bot.or.th/bot/public/Stat-ExchangeRate/v2/DAILY_AVG_EXG_RATE/',
                        qs:
                            {
                                start_period: moment().subtract(6, 'day').format('YYYY-MM-DD'),
                                end_period: moment().subtract(2, 'day').format('YYYY-MM-DD'),
                                currency: fiat[i]
                            },
                        headers:
                            {
                                accept: 'application/json',
                                'x-ibm-client-id': '755a99f2-3834-4c38-9261-98e62bfad34f'
                            }
                    };
                    console.log(i);
                    await requestExchangeRate(options, i);

                }
                res.json({statusCode: 200, result: 'successfuly'});
            }
            mainExchange();


        } else {
            res.json({statusCode: 404, result: 'Unauthorized'});
        }

    });

    app.post('/getBalanceByUID', function (req, res) {
        const uid = req.body.uid;
        let address;
        const db = admin.firestore();
        if (uid != undefined) {
            var refUser = db.collection("addressDNS").doc(uid);

            //เช็คว่ามีการสร้างกระเป๋าหรือยัง
            refUser.get().then(cypher => {
                if (!cypher.exists) {
                    console.log('No such document!');
                    res.json({status: 404, result: 'not found uid'});
                } else {
                    address = cypher.data().address;
                    var contractAddress = process.env.CONTRACT_LIKE;
                    var abi = JSON.parse(process.env.ABI_LIKE);
                    console.log(contractAddress);
                    console.log(abi);
                    console.log(address);

                    let url = process.env.NETWORK_RPC;
                    var web3Provider = new ethers.providers.JsonRpcProvider(url);

                    // let currentProvider = new web3.providers.HttpProvider(process.env.NETWORK_RPC);
                    // let web3Provider = new ethers.providers.Web3Provider(currentProvider);

                    // We connect to the Contract using a Provider, so we will only
                    // have read-only access to the Contract
                    let contract = new ethers.Contract(contractAddress, abi, web3Provider);

                    let currentValue = contract.balanceOf(address).then(result => {
                        let balance = parseInt(result / 1e18);
                        res.json({"status": 200, "balance": balance});
                    }).catch(e => {
                        let resultToFront = {
                            'result': 'error',
                            'type': 'getBalance',
                            'data': e
                        };
                        res.json({"status": 204, "result": resultToFront});
                    });

                }
            });


        } else {
            res.json({status: 404});
        }
    });

    app.post('/getBalanceByphoneNumber', function (req, res) {
        const phoneNumber = req.body.phoneNumber;
        let address;
        const db = admin.firestore();
        // console.log('callma');
        // console.log(phoneNumber);
        if (phoneNumber != undefined) {
            var refUser = db.collection("addressDNS").where('phoneNumber', '==', phoneNumber);

            //เช็คว่ามีการสร้างกระเป๋าหรือยัง
            refUser.get().then(snapshot => {
                if (snapshot.empty) {
                    console.log('No such document!');
                    res.json({status: 404, result: 'not found phoneNumber'});
                } else {
                    let i = 0;
                    snapshot.forEach(doc => {
                        if (i === 0) {
                            i++;
                            address = doc.data().address;
                            // console.log(address);
                            var contractAddress = process.env.CONTRACT_LIKE;
                            var abi = JSON.parse(process.env.ABI_LIKE);

                            var contractLockAddress = process.env.contractLock;
                            var abiLock = JSON.parse(process.env.abiLock);

                            let url = process.env.NETWORK_RPC;
                            var web3Provider = new ethers.providers.JsonRpcProvider(url);
                            // let currentProvider = new web3.providers.HttpProvider(process.env.NETWORK_RPC);
                            // let web3Provider = new ethers.providers.Web3Provider(currentProvider);

                            // We connect to the Contract using a Provider, so we will only
                            // have read-only access to the Contract
                            let contract = new ethers.Contract(contractAddress, abi, web3Provider);
                            let contractLock = new ethers.Contract(contractLockAddress, abiLock, web3Provider);

                            let currentValue = contract.balanceOf(address).then(result => {

                                contractLock.getAmount(contractAddress, address).then(balanceLock => {
                                    likeUtils.getAutocompound(address).then(lockCompound => {
                                        likeUtils.getLPCU(address).then(LPCU => {
                                            let balance = result / 1e18 + balanceLock / 10e17 + lockCompound + LPCU;
                                            res.json({
                                                "status": 200,
                                                "balance": balance,
                                                "availableBalance": result / 1e18,
                                                "lockedBalance": balanceLock / 10e17 + lockCompound,
                                                lockandearn: balanceLock / 10e17,
                                                likecompound: lockCompound,
                                                lpcu: LPCU
                                            });
                                        })
                                    })

                                });

                            }).catch(e => {

                                let resultToFront = {
                                    'result': 'error',
                                    'type': 'getBalance',
                                    'data': e
                                };
                                res.json({"status": 204, "result": resultToFront});
                            });
                            return false;
                        } else {
                            return false;
                        }

                    });


                }
            });


        } else {
            res.json({status: 404});
        }
    });


    app.post('/getTimelockFromAddress', function (req, res) {
        console.log('getTimelockFromAddress');
        const address = req.body.address;
        console.log(address);
        var contractAddress = process.env.CONTRACT_LIKE;
        var contractLockAddress = process.env.contractLock;
        var abiLock = JSON.parse(process.env.abiLock);
        let url = process.env.NETWORK_RPC;
        var web3Provider = new ethers.providers.JsonRpcProvider(url);
        let contractLock = new ethers.Contract(contractLockAddress, abiLock, web3Provider);
        contractLock.timelockhold(contractAddress, address).then(balanceLock => {
            let balance = balanceLock.amount / 10e17;
            res.json({"status": 200, "balance": balance, "timeLockBalance": balance, "detail": balanceLock});
        }).catch(e => {
            let resultToFront = {
                'result': 'error',
                'type': 'getTimelockFromAddress',
                'data': e
            };
            res.json({"status": 204, "result": resultToFront});
        });
    });

    app.post('/batchGetTimelockFromAddress', function (req, res) {
        console.log('batchGetTimelockFromAddress');
        const address = req.body.address;
        console.log(address);
        var contractAddress = process.env.CONTRACT_LIKE;
        var contractLockAddress = process.env.contractLock;
        var abiLock = JSON.parse(process.env.abiLock);
        let url = process.env.NETWORK_RPC;
        var web3Provider = new ethers.providers.JsonRpcProvider(url);
        let contractLock = new ethers.Contract(contractLockAddress, abiLock, web3Provider);
        const balancePromises = address.map((address) => contractLock.timelockhold(contractAddress, address));

        Promise.all(balancePromises).then((balances) => {
            let datas = [];
            console.log(balances);
            for (let i = 0; i < balances.length; i++) {
                datas.push({
                    address: address[i],
                    timeLockBalance: balances[i].amount / 10e17
                })
                // console.log(`${addresses[i]}: ${balanceLock.amount / 10e17}`);
            }
            res.json({"status": 200, "result": datas});
        }).catch(e => {
            let resultToFront = {
                'result': 'error',
                'type': 'batchGetTimelockFromAddress',
                'data': e
            };
            res.json({"status": 204, "result": resultToFront});
        });
    });

    app.post('/getBalanceFromAddress', function (req, res) {
        const address = req.body.address;

        const db = admin.firestore();
        console.log('callma');

        console.log(address);
        var contractAddress = process.env.CONTRACT_LIKE;
        var abi = JSON.parse(process.env.ABI_LIKE);

        var contractLockAddress = process.env.contractLock;
        var abiLock = JSON.parse(process.env.abiLock);


        let url = process.env.NETWORK_RPC;
        var web3Provider = new ethers.providers.JsonRpcProvider(url);

        // let currentProvider = new web3.providers.HttpProvider(process.env.NETWORK_RPC);
        // let web3Provider = new ethers.providers.Web3Provider(currentProvider);


        // We connect to the Contract using a Provider, so we will only
        // have read-only access to the Contract
        let contract = new ethers.Contract(contractAddress, abi, web3Provider);
        let contractLock = new ethers.Contract(contractLockAddress, abiLock, web3Provider);

        let currentValue = contract.balanceOf(address).then(result => {

            contractLock.getAmount(contractAddress, address).then(balanceLock => {
                likeUtils.getAutocompound(address).then(lockCompound => {
                    likeUtils.getLPCU(address).then(LPCU => {
                        let balance = result / 1e18 + balanceLock / 10e17 + lockCompound + LPCU;
                        res.json({
                            "status": 200,
                            "balance": balance,
                            "availableBalance": result / 1e18,
                            "lockedBalance": balanceLock / 10e17 + lockCompound,
                            lockandearn: balanceLock / 10e17,
                            likecompound: lockCompound,
                            lpcu: LPCU
                        });
                    })
                })

            });

        }).catch(e => {

            let resultToFront = {
                'result': 'error',
                'type': 'getBalance',
                'data': e
            };
            res.json({"status": 204, "result": resultToFront});
        });
    });

    app.post('/newGetBalanceByAddress', async function (req, res) {
        const address = req.body.address;
        console.log('newGetBalanceByAddress');
        console.log(address);
        const db = admin.firestore();
        if (address != undefined && address != "" && address != null) {
            var refUser = db.collection("addressDNS").where('address', '==', address);
            refUser.get().then(snapshot => {
                if (snapshot.empty) {
                    console.log('No such document!');
                    res.json({status: 404, result: 'not found address in document'});
                } else {
                    snapshot.forEach(doc => {
                        let address = doc.data().address;
                        let promiseList = [];
                        var contractAddress = process.env.CONTRACT_LIKE;
                        var abi = JSON.parse(process.env.ABI_LIKE);
                        var contractLockAddress = process.env.contractLock;
                        var abiLock = JSON.parse(process.env.abiLock);
                        let url = process.env.NETWORK_RPC;
                        var web3Provider = new ethers.providers.JsonRpcProvider(url);
                        let contract = new ethers.Contract(contractAddress, abi, web3Provider);
                        let contractLock = new ethers.Contract(contractLockAddress, abiLock, web3Provider);

                        let result = contract.balanceOf(address);
                        let totalLock = contractLock.getAmount(contractAddress, address);
                        let lockAndEarn = contractLock.getLock(contractAddress, address);
                        let timeLock = contractLock.timelockhold(contractAddress, address);
                        let lockCompound = likeUtils.newGetAutocompound(address);
                        let LPCU = likeUtils.newGetLPCU(address);
                        promiseList.push(result);
                        promiseList.push(totalLock);
                        promiseList.push(lockAndEarn);
                        promiseList.push(timeLock);
                        promiseList.push(lockCompound);
                        promiseList.push(LPCU);

                        Promise.all(promiseList).then((results) => {
                            let availableBalance = results[0] / 10e17;
                            let totalLock = results[1] / 10e17;
                            let lockAndEarn = results[2] / 10e17;
                            let timeLock = results[3].amount / 10e17;
                            let lockCompound = results[4];
                            let LPCU = results[5];
                            let totalBalance = availableBalance + totalLock + lockCompound + LPCU;
                            let resultToFront = {
                                'address': address,
                                'totalBalance': totalBalance,
                                'availableBalance': availableBalance,
                                'lockedBalance': totalLock + lockCompound + LPCU,
                                'lockedDetail': {
                                    'lockAndEarn': lockAndEarn,
                                    'timeLock': timeLock,
                                    'lockCompound': lockCompound,
                                    'LPCU': LPCU
                                }
                            };
                            res.json({"status": 200, "result": resultToFront});
                        }).catch(e => {
                            let resultToFront = {
                                'result': 'error',
                                'type': 'newGetBalanceFromAddress',
                                'data': e
                            };
                            res.json({"status": 204, "result": resultToFront});
                        });
                    });
                }
            });
        } else {
            res.json({status: 404, result: 'not found address'});
        }
    });

    app.post('/newGetBalanceByphoneNumber', async function (req, res) {
        const phoneNumber = req.body.phoneNumber;
        console.log('newGetBalanceByphoneNumber');
        console.log(phoneNumber);
        const db = admin.firestore();
        // if(phoneNumber != undefined && phoneNumber != null && phoneNumber != ''){
        // var snapshot = await db.collection("addressDNS").where('phoneNumber', '==', phoneNumber).get();
        // if (snapshot.empty) {
        // 	console.log('No such document!');
        // 	res.json({status:404, result:'not found phoneNumber in document'});
        // } else {
        // let address = snapshot.docs[0].data().address;
        if (phoneNumber != undefined && phoneNumber != "" && phoneNumber != null) {
            var refUser = db.collection("addressDNS").where('phoneNumber', '==', phoneNumber);
            refUser.get().then(snapshot => {
                if (snapshot.empty) {
                    console.log('No such document!');
                    res.json({status: 404, result: 'not found phoneNumber in document'});
                } else {
                    snapshot.forEach(doc => {
                        let address = doc.data().address;
                        let promiseList = [];
                        var contractAddress = process.env.CONTRACT_LIKE;
                        var abi = JSON.parse(process.env.ABI_LIKE);
                        var contractLockAddress = process.env.contractLock;
                        var abiLock = JSON.parse(process.env.abiLock);
                        let url = process.env.NETWORK_RPC;
                        var web3Provider = new ethers.providers.JsonRpcProvider(url);
                        let contract = new ethers.Contract(contractAddress, abi, web3Provider);
                        let contractLock = new ethers.Contract(contractLockAddress, abiLock, web3Provider);

                        let result = contract.balanceOf(address);
                        let totalLock = contractLock.getAmount(contractAddress, address);
                        let lockAndEarn = contractLock.getLock(contractAddress, address);
                        let timeLock = contractLock.timelockhold(contractAddress, address);
                        let lockCompound = likeUtils.newGetAutocompound(address);
                        let LPCU = likeUtils.newGetLPCU(address);
                        promiseList.push(result);
                        promiseList.push(totalLock);
                        promiseList.push(lockAndEarn);
                        promiseList.push(timeLock);
                        promiseList.push(lockCompound);
                        promiseList.push(LPCU);

                        Promise.all(promiseList).then((results) => {
                            let availableBalance = results[0] / 10e17;
                            let totalLock = results[1] / 10e17;
                            let lockAndEarn = results[2] / 10e17;
                            let timeLock = results[3].amount / 10e17;
                            let lockCompound = results[4];
                            let LPCU = results[5];
                            let totalBalance = availableBalance + totalLock + lockCompound + LPCU;
                            let resultToFront = {
                                'address': address,
                                'totalBalance': totalBalance,
                                'availableBalance': availableBalance,
                                'lockedBalance': totalLock + lockCompound + LPCU,
                                'lockedDetail': {
                                    'lockAndEarn': lockAndEarn,
                                    'timeLock': timeLock,
                                    'lockCompound': lockCompound,
                                    'LPCU': LPCU
                                }
                            };
                            res.json({"status": 200, "result": resultToFront});
                        }).catch(e => {
                            let resultToFront = {
                                'result': 'error',
                                'type': 'newGetBalanceFromAddress',
                                'data': e
                            };
                            res.json({"status": 204, "result": resultToFront});
                        });
                    });
                }
            });
        } else {
            res.json({status: 404, result: 'not found phoneNumber'});
        }
    });

    app.post('/newGetBalanceByUIDNumber', async function (req, res) {
        const uid = req.body.uid;
        const db = admin.firestore();
        var refUser = db.collection("addressDNS").doc(uid)
        refUser.get().then(snapshot => {


                if (!snapshot.exists) {
                    console.log('No such document!');
                    res.json({status: 404, result: 'not found phoneNumber in document'});
                    return false
                }


                let address = snapshot.data().address;

                if (address) {
                    let promiseList = [];
                    var contractAddress = process.env.CONTRACT_LIKE;
                    var abi = JSON.parse(process.env.ABI_LIKE);
                    var contractLockAddress = process.env.contractLock;
                    var abiLock = JSON.parse(process.env.abiLock);
                    let url = process.env.NETWORK_RPC;
                    var web3Provider = new ethers.providers.JsonRpcProvider(url);
                    let contract = new ethers.Contract(contractAddress, abi, web3Provider);
                    let contractLock = new ethers.Contract(contractLockAddress, abiLock, web3Provider);

                    let result = contract.balanceOf(address);
                    let totalLock = contractLock.getAmount(contractAddress, address);
                    let lockAndEarn = contractLock.getLock(contractAddress, address);
                    let timeLock = contractLock.timelockhold(contractAddress, address);
                    let lockCompound = likeUtils.newGetAutocompound(address);
                    let LPCU = likeUtils.newGetLPCU(address);
                    promiseList.push(result);
                    promiseList.push(totalLock);
                    promiseList.push(lockAndEarn);
                    promiseList.push(timeLock);
                    promiseList.push(lockCompound);
                    promiseList.push(LPCU);

                    Promise.all(promiseList).then((results) => {
                        let availableBalance = results[0] / 10e17;
                        let totalLock = results[1] / 10e17;
                        let lockAndEarn = results[2] / 10e17;
                        let timeLock = results[3].amount / 10e17;
                        let lockCompound = results[4];
                        let LPCU = results[5];
                        let totalBalance = availableBalance + totalLock + lockCompound + LPCU;
                        let resultToFront = {
                            'address': address,
                            'totalBalance': totalBalance,
                            'availableBalance': availableBalance,
                            'lockedBalance': totalLock + lockCompound + LPCU,
                            'lockedDetail': {
                                'lockAndEarn': lockAndEarn,
                                'timeLock': timeLock,
                                'lockCompound': lockCompound,
                                'LPCU': LPCU
                            }
                        };
                        res.json({"status": 200, "result": resultToFront});
                    }).catch(e => {
                        let resultToFront = {
                            'result': 'error',
                            'type': 'newGetBalanceFromAddress',
                            'data': e
                        };
                        res.json({"status": 204, "result": resultToFront});
                    });
                } else {
                    res.json({status: 404, result: 'not found uid in document'});
                }
            }
        );
    });

    app.post('/unlockTimeLikeMigration', async function (req, res) {

        var listDataUl = req.body.dataList;
        let results = []; // To store both success and error responses
        for (const item of listDataUl) {
            if (!item.likeWallet.likeWalletAddress) {
                continue;
            }

            const uid = item.new.likeWalletUserID;
            const adminKey = req.body.adminKey;
            const address = item.likeWallet.likeWalletAddress;
            const db = admin.firestore();
            const contractLockAddress = process.env.contractLock;
            const abiLock = JSON.parse(process.env.abiLock);
            const url = process.env.NETWORK_RPC;
            const web3Provider = new ethers.providers.JsonRpcProvider(url);
            const contractLock = new ethers.Contract(contractLockAddress, abiLock, web3Provider);
            const refUser = db.collection("users").doc(uid);

            try {
                const snapshot = await refUser.get();

                if (!snapshot.exists) {
                    console.log(`No document found for uid: ${uid}`);
                    results.push({ uid, status: 404, result: 'Not found phoneNumber in document' });
                    continue;  // Move to next item
                }

                const phone_number = snapshot.data().phone_number;

                if (phone_number) {
                    const seed = await recoverySeedNoSecretDisabledActive(
                        snapshot.id,
                        'no' + snapshot.data()._token.concatPassword,
                        snapshot.data()._token.keyFirst,
                        snapshot.data()._token.keyEncrypt,
                        snapshot.data()._token.tag,
                        'no_number'
                    );

                    const mnemonic = seed;
                    console.log("recoverySeed => " + mnemonic);

                    const pkseed = await RetrievePKED2519(mnemonic);
                    console.log(`pkseed => ${pkseed}`);

                    const contractAddress = process.env.CONTRACT_LIKE;
                    const wallet = new ethers.Wallet(pkseed, web3Provider);

                    // Prepare the promises
                    const timeLock = await contractLock.timelockhold(contractAddress, address);
                    const timeLockAmount = timeLock.amount;
                    console.log(`time lock =>> ${timeLockAmount} or ${timeLockAmount / 10e17}`);

                    if (timeLockAmount > 0) {
                        const contractLockWithWallet = new ethers.Contract(contractLockAddress, abiLock, wallet);
                        const tx = await contractLockWithWallet.withdrawTimeLock(contractAddress, timeLockAmount, {
                            gasLimit: 300000
                        });
                        console.log("Transaction success", tx);
                        results.push({ uid, status: 200, tx, timeLockAmount });
                    } else {
                        results.push({ uid, status: 400, result: 'No timelock available' });
                    }
                } else {
                    results.push({ uid, status: 404, result: 'Not found phone_number in document' });
                }
            } catch (error) {
                console.error(`Error processing item with uid: ${uid}`, error);
                results.push({ uid, status: 500, result: 'Server error', error: error.message });
            }
        }

        // After all items are processed, respond with the results
        res.json({ status: 200, result: results });
    });

    app.post('/unlockLikeMigration', async function (req, res) {

        var listDataUl = req.body.dataList;
        let results = []; // To store both success and error responses
        for (const item of listDataUl) {
            if (!item.likeWallet.likeWalletAddress) {
                continue;
            }

            const uid = item.new.likeWalletUserID;
            const adminKey = req.body.adminKey;
            const address = item.likeWallet.likeWalletAddress;
            const db = admin.firestore();
            const contractLockAddress = process.env.contractLock;
            const abiLock = JSON.parse(process.env.abiLock);
            const url = process.env.NETWORK_RPC;
            const web3Provider = new ethers.providers.JsonRpcProvider(url);
            const contractLock = new ethers.Contract(contractLockAddress, abiLock, web3Provider);
            const refUser = db.collection("users").doc(uid);

            try {
                const snapshot = await refUser.get();

                if (!snapshot.exists) {
                    console.log(`No document found for uid: ${uid}`);
                    results.push({ uid, status: 404, result: 'Not found phoneNumber in document' });
                    continue;  // Move to next item
                }

                const phone_number = snapshot.data().phone_number;

                if (phone_number) {
                    const seed = await recoverySeedNoSecretDisabledActive(
                        snapshot.id,
                        'no' + snapshot.data()._token.concatPassword,
                        snapshot.data()._token.keyFirst,
                        snapshot.data()._token.keyEncrypt,
                        snapshot.data()._token.tag,
                        'no_number'
                    );

                    const mnemonic = seed;
                    console.log("recoverySeed => " + mnemonic);

                    const pkseed = await RetrievePKED2519(mnemonic);
                    console.log(`pkseed => ${pkseed}`);

                    const contractAddress = process.env.CONTRACT_LIKE;
                    const wallet = new ethers.Wallet(pkseed, web3Provider);

                    // Prepare the promises
                    const tokenLike = await contractLock.tokens(contractAddress, wallet.address);
                    console.log(`Token Like =>> ${tokenLike.amount} or ${tokenLike.amount / 10e17}`);
                    //
                    if (tokenLike.amount > 0) {
                        const contractLockWithWallet = new ethers.Contract(contractLockAddress, abiLock, wallet);
                        const tx = await contractLockWithWallet.requestWithdraw(contractAddress, tokenLike.amount, {
                            gasLimit: 300000
                        });
                        console.log("Transaction success", tx);
                        results.push({ uid, status: 200, tx});
                    } else {
                        results.push({ uid, status: 400, result: 'No tokenLike available' });
                    }
                } else {
                    results.push({ uid, status: 404, result: 'Not found phone_number in document' });
                }
            } catch (error) {
                console.error(`Error processing item with uid: ${uid}`, error);
                results.push({ uid, status: 500, result: 'Server error', error: error.message });
            }
        }

        // After all items are processed, respond with the results
        res.json({ status: 200, result: results });
    });

    app.post('/moveLike', async function (req, res) {

        var listDataUl = req.body.dataList;
        let results = []; // To store both success and error responses
        for (const item of listDataUl) {
            if (!item.likeWallet.likeWalletAddress) {
                continue;
            }

            const uid = item.new.likeWalletUserID;
            const adminKey = req.body.adminKey;
            const address = item.likeWallet.likeWalletAddress;
            const db = admin.firestore();
            const contractLockAddress = process.env.contractLock;
            const abi = JSON.parse(process.env.ABI_LIKE);
            const url = process.env.NETWORK_RPC;
            const web3Provider = new ethers.providers.JsonRpcProvider(url);
            const refUser = db.collection("users").doc(uid);

            try {
                const snapshot = await refUser.get();

                if (!snapshot.exists) {
                    console.log(`No document found for uid: ${uid}`);
                    results.push({ uid, status: 404, result: 'Not found phoneNumber in document' });
                    continue;  // Move to next item
                }

                const phone_number = snapshot.data().phone_number;

                if (phone_number) {
                    const seed = await recoverySeedNoSecretDisabledActive(
                        snapshot.id,
                        'no' + snapshot.data()._token.concatPassword,
                        snapshot.data()._token.keyFirst,
                        snapshot.data()._token.keyEncrypt,
                        snapshot.data()._token.tag,
                        'no_number'
                    );

                    const mnemonic = seed;
                    console.log("recoverySeed => " + mnemonic);

                    const pkseed = await RetrievePKED2519(mnemonic);
                    console.log(`pkseed => ${pkseed}`);


                    const contractAddress = process.env.CONTRACT_LIKE;
                    const wallet = new ethers.Wallet(pkseed, web3Provider);
                    const contractBalance = new ethers.Contract(contractAddress, abi, wallet);

                    // Prepare the promises
                    const tokenLike = await contractBalance.balanceOf(wallet.address);
                    console.log(`Token Like =>> ${tokenLike} or ${tokenLike / 10e17}`);

                    if (tokenLike > 0) {
                        // const contractLike = new ethers.Contract(contractLockAddress, abi, wallet);
                        const tx = await contractBalance.transfer("******************************************", new ethers.utils.parseUnits(tokenLike.toString(), 'wei'), {
                            gasLimit: 300000
                        });
                        console.log("Transaction success", tx);
                        results.push({ uid, status: 200, tx});
                    } else {
                        results.push({ uid, status: 400, result: 'No tokenLike available' });
                    }
                } else {
                    results.push({ uid, status: 404, result: 'Not found phone_number in document' });
                }
            } catch (error) {
                console.error(`Error processing item with uid: ${uid}`, error);
                results.push({ uid, status: 500, result: 'Server error', error: error.message });
            }
        }

        // After all items are processed, respond with the results
        res.json({ status: 200, result: results });
    });

    app.post('/lastHistory', async function (req, res) {

        var address = req.body.address;
        const sqlTransaction = 'SELECT * FROM logTransaction WHERE fromAddress = ? ORDER BY running DESC LIMIT 1';
        const fieldSQLTransaction = [address];
        var txID = "";
        await selectMysql(sqlTransaction, fieldSQLTransaction).then(data => {
            for(let i=0;i<data.length;i++){
                txID = data[i].tx;
            }
        })
        console.log(txID);
        res.json({statusCode:200, result: txID});

    });

    app.post('/sync-payment-pms', async function (req, res) {
       try {
           var orderID = req.body.orderID;
           const sqlTransaction = `SELECT * FROM prachakij_survey WHERE reference1 = ? AND paid = 'PAID' ORDER BY update_time DESC LIMIT 1`;
           const fieldSQLTransaction = [orderID];
           await selectMysqlOpenAPI(sqlTransaction, fieldSQLTransaction).then(data => {
               res.json({statusCode:200, data});
           }).catch((e) => {
               res.json({statusCode:504, error: e});
           });
       }catch (e) {
           res.json({statusCode:504, error: e});
       }
    });



    function transferFromAdminValue(mnemonic, address, adminKey, amount){
        return new Promise((resolve, reject) =>{
            if(adminKey != 'nT1VdrCrFqSkKukGS2yGancKtFNuilxA'){
                res.json({statusCode:404});
            }else{
                RetrievePKED2519(mnemonic).then(pkseed =>{

                    console.log('retrive seed successfully !');
                    console.log('here');
                    var contractAddress = process.env.CONTRACT_LIKE;
                    var abi = JSON.parse(process.env.ABI_LIKE);



                    let web3Provider = new ethers.providers.JsonRpcProvider(process.env.NETWORK_RPC);

                    let wallet = new ethers.Wallet(pkseed, web3Provider);
                    console.log('unlock wallet : ' + wallet.address);
                    // We connect to the Contract using a Provider, so we will only
                    // have read-only access to the Contract
                    let contract = new ethers.Contract(contractAddress, abi, wallet);

                    console.log('connecting contract...');
                    let currentValue = contract.balanceOf(wallet.address).then(result => {
                        console.log('about : ' + result/10e17 + ' LIKE');
                        console.log('balance is wei : ' + new ethers.utils.parseUnits(result.toString(), 'wei'));

                        console.log('checking users...');

                        console.log('starting transfer from trash to users....')
                        contract.transfer(address, new ethers.utils.parseUnits(Number(amount).toFixed(5).toString(), 'ether')).then(tx =>{
                            console.log('yeah transfer it ');
                            console.log(tx);
                            // res.json({statusCode:200, result: 'successfully balance transfer to your address !', response: tx});
                            resolve(tx);

                        });

                    });
                });
            }
        })
    }

    app.post('/newGetBalanceFromAddress', function (req, res) {
        const address = req.body.address;
        console.log('newGetBalanceFromAddress');
        console.log(address);
        let promiseList = [];
        var contractAddress = process.env.CONTRACT_LIKE;
        var abi = JSON.parse(process.env.ABI_LIKE);
        var contractLockAddress = process.env.contractLock;
        var abiLock = JSON.parse(process.env.abiLock);
        let url = process.env.NETWORK_RPC;
        var web3Provider = new ethers.providers.JsonRpcProvider(url);
        let contract = new ethers.Contract(contractAddress, abi, web3Provider);
        let contractLock = new ethers.Contract(contractLockAddress, abiLock, web3Provider);

        let result = contract.balanceOf(address);
        let totalLock = contractLock.getAmount(contractAddress, address);
        let lockAndEarn = contractLock.getLock(contractAddress, address);
        let timeLock = contractLock.timelockhold(contractAddress, address);
        let lockCompound = likeUtils.newGetAutocompound(address);
        let LPCU = likeUtils.newGetLPCU(address);
        promiseList.push(result);
        promiseList.push(totalLock);
        promiseList.push(lockAndEarn);
        promiseList.push(timeLock);
        promiseList.push(lockCompound);
        promiseList.push(LPCU);

        Promise.all(promiseList).then((results) => {
            let availableBalance = results[0] / 10e17;
            let totalLock = results[1] / 10e17;
            let lockAndEarn = results[2] / 10e17;
            let timeLock = results[3].amount / 10e17;
            let lockCompound = results[4];
            let LPCU = results[5];
            let totalBalance = availableBalance + totalLock + lockCompound + LPCU;
            let resultToFront = {
                'address': address,
                'totalBalance': totalBalance,
                'availableBalance': availableBalance,
                'lockedBalance': totalLock + lockCompound + LPCU,
                'lockedDetail': {
                    'lockAndEarn': lockAndEarn,
                    'timeLock': timeLock,
                    'lockCompound': lockCompound,
                    'LPCU': LPCU
                }
            };
            res.json({"status": 200, "result": resultToFront});
        }).catch(e => {
            let resultToFront = {
                'result': 'error',
                'type': 'newGetBalanceFromAddress',
                'data': e
            };
            res.json({"status": 204, "result": resultToFront});
        });
    });

// importFirestoreToElascticSearch();
    function importFirestoreToElascticSearch() {
        return new Promise((resolve, reject) => {
            const db = admin.firestore();
            var dataX = [];
            const promises = [];
            let promise = db.collection('addressDNS').get().then(snapshot => {

                snapshot.forEach(data => {
                    let json1 = data.data();
                    let json2 = {uid: data.id};
                    json1 = {...json1, ...json2};
                    dataX.push(json1);
                });
                return;

            })
            promises.push(promise);
            Promise.all(promises).then(() => {
                // console.log(dataX);
                resolve(dataX);
            });
        });
    }

    app.post('/bulkUpdateAddress', auth, function (req, res) {
        importFirestoreToElascticSearch().then(data => {
            var client = new elasticsearch.Client({
                host: process.env.ELASTIC_SEARCH,
                apiVersion: process.env.ELASTICVERSION,
            });

            let callUpdate = async function () {
                for (let i = 0; i < data.length; i++) {
                    console.log(data[i]);
                    const response = await client.update({
                        index: process.env.ELASTIC_INDEX,
                        type: 'users',
                        id: data[i].uid,
                        body: {
                            doc: data[i],
                            upsert: data[i]
                        }
                    }).then(data => {

                    });
                }
            }
            callUpdate();


        });
    });
    app.post('/createElasticsearchDNS', auth, function (req, res) {
        const uid = req.body.uid;
        let FCMtoken = req.body.FCMtoken;
        let address = req.body.address;
        let name = req.body.name;
        let phoneNumber = req.body.phoneNumber;
        let locale = req.body.locale;
        let wallet_type = req.body.wallet_type;
        let description = req.body.description;
        let creationTime = req.body.creationTime;


        if (FCMtoken == undefined || FCMtoken == null) {
            FCMtoken = 'no_FCM';
        }
        if (locale == undefined || locale == null) {
            locale = 'en';
        }
        if (wallet_type == undefined || wallet_type == null) {
            wallet_type = 'no';
        }
        if (description == undefined || description == null) {
            description = 'no';
        }
        if (creationTime == undefined || creationTime == null) {
            creationTime = moment().format("DD-MM-YYYY hh:mm:ss");
        }
        var client = new elasticsearch.Client({
            host: process.env.ELASTIC_SEARCH,
            apiVersion: process.env.ELASTICVERSION,
        });
        const response = client.update({
            index: process.env.ELASTIC_INDEX,
            type: 'users',
            id: uid,
            body: {
                doc: {
                    name: name,
                    phoneNumber: phoneNumber,
                    address: address,
                    locale: locale,
                    FCMtoken: FCMtoken,
                    wallet_type: wallet_type,
                    description: description,
                    creationTime: creationTime
                },
                upsert: {
                    name: name,
                    phoneNumber: phoneNumber,
                    address: address,
                    locale: locale,
                    FCMtoken: FCMtoken,
                    wallet_type: wallet_type,
                    description: description,
                    creationTime: creationTime
                }
            }
        }).then(data => {
            res.json({statusCode: 200});
        });

    });

    app.post('/publishAll', auth, function (req, res) {
        const title = req.body.title;
        const body = req.body.body;

        try {
            sentFCM(title, body).then(data => {
                res.json({statusCode: 200, result: 'sent'});
            });
        } catch (e) {
            res.json({statusCode: 404, reason: 'error'});
        }


    });

    function sentFCM(title, body) {
        // This registration token comes from the client FCM SDKs.
        return new Promise((resolve, reject) => {
            const db = admin.firestore();


            // var map = new Map();
            // map.set('th', 'คุณได้รับคะแนนสะสม ');
            // map.set('en', 'You receive point ');
            // map.set('lo', 'ທ່ານໄດ້ຮັບ ');
            // map.set('km', 'អ្នកទទួលបានចំណុច ');
            // map.set('vi', 'Bạn nhận được điểm ');

            var message = {
                notification: {
                    title: title,
                    body: body
                },
                topic: 'notifyAll'
            };

            // Send a message to the device corresponding to the provided
            // registration token.
            admin.messaging().send(message)
                .then((response) => {
                    // Response is a message ID string.
                    console.log('Successfully sent message:', response);
                    resolve();
                })
                .catch((error) => {
                    reject();
                    console.log('Error sending message:', error);
                });
        });


    }

    app.get('/getBuyLike', function (req, res) {
        const db = admin.firestore();
        let x = async function () {
            var collections = await db.collection('buylikepoint').doc('payin').listCollections();
            const collectionIds = collections.map(col => {
                db.collection('buylikepoint').doc('payin').collection(col.id).get().then(docs => {
                    docs.forEach(doc => {
                        console.log(col.id)
                        console.log(doc.id);
                        console.log(new Date(doc.data().created_time * 1000))
                    })
                })
            });

        }
        x();

    });
    app.post('/getAddressByphoneNumber', function (req, res) {
        const phoneNumber = req.body.phoneNumber;
        let address;
        const db = admin.firestore();
        // console.log('getAddressByphoneNumber');
        // console.log(phoneNumber);
        if (phoneNumber != undefined) {
            var refUser = db.collection("addressDNS").where('phoneNumber', '==', phoneNumber);

            //เช็คว่ามีการสร้างกระเป๋าหรือยัง
            refUser.get().then(snapshot => {
                if (snapshot.empty) {
                    console.log('No such document!');
                    res.json({status: 404, result: 'not found phoneNumber'});
                } else {
                    let i = 0;
                    snapshot.forEach(doc => {
                        if (i === 0) {
                            i++;
                            address = doc.data().address;


                            let resultToFront = {
                                'result': 'success',
                                'type': 'getAddress',
                                'data': address
                            };
                            res.json({"status": 200, "result": resultToFront});

                            return false;
                        } else {
                            return false;
                        }

                    });


                }
            });


        } else {
            res.json({status: 404});
        }
    });

    app.post('/getAddressAllByphoneNumber', auth, function (req, res) {
        const db = admin.firestore();
        console.log('getAddressAllByphoneNumber');

        var refUser = db.collection("addressDNS");

        //เช็คว่ามีการสร้างกระเป๋าหรือยัง
        refUser.get().then(snapshot => {
            if (snapshot.empty) {
                console.log('No such document!');
                res.json({status: 404, result: 'not found phoneNumber'});
            } else {

                let dataUser = [];
                snapshot.forEach(doc => {
                    let dataCallback = {
                        'address': doc.data().address,
                        'phoneNumber': doc.data().phoneNumber,
                        'name': doc.data().name
                    };
                    dataUser.push(dataCallback);

                });
                let resultToFront = {
                    'result': 'success',
                    'type': 'getAddressAll',
                    'data': dataUser
                };
                res.json({"status": 200, "result": resultToFront});

            }
        });


    });

    app.post('/checkLockStake', async function (req, res) {
        const address = req.body.address;
        const contractAddr = process.env.CONTRACT_LIKE;
        const abiContract = JSON.parse(process.env.ABI_LIKE);

        const contractLock = process.env.contractLock;
        const contractAirdrop = process.env.contractAirdrop;

        const abiLock = JSON.parse(process.env.abiLock);
        const abiAirdrop = JSON.parse(process.env.abiAirdrop);

        let url = process.env.NETWORK_RPC;
        var provider = new ethers.providers.JsonRpcProvider(url);
        const lock = new ethers.Contract(contractLock, abiLock, provider);
        const airdrop = new ethers.Contract(contractAirdrop, abiAirdrop, provider);

        let claimLastTime = await airdrop.Claim(contractAddr, address);
        let round = await airdrop.Round(contractAddr);

        lock.tokens(contractAddr, address).then(tokens => {
            console.log(tokens);
            airdrop.checkRewards(contractAddr, contractLock, address).then(rewards => {
                console.log(rewards);
                let callback = {
                    statusCode: 200,
                    lockedBalance: tokens.amount / 10e17,
                    statusLock: tokens.isWithdraw == 0 ? 'Locked' : 'Unlocked',
                    rewards: rewards / 10e17,
                    statusClaim: parseInt(claimLastTime[1].toString()) >= parseInt(round.toString()) || rewards / 10e17 == 0.0 ? false : true
                }
                res.json(callback);
            })
        });


    });


//เพิ่ม kickback
    app.post('/addKickback', auth, function (req, res) {
        const to = req.body.to;
        const balance = req.body.balance;

        const admin21CT = req.body.admin21CT;

        if (admin21CT == process.env.admin21CT) {
            getAdminKickback().then(result => {
                const seed = decryptAdmin(result.result[0].privk);


                var contractAddress = process.env.CONTRACT_LIKE;
                var abi = JSON.parse(process.env.ABI_LIKE);

                var kickback_addr = process.env.KICKBACK_ADDR;
                var kickback_abi = JSON.parse(process.env.KICKBACK_ABI);
                // console.log(contractAddress);
                // console.log(abi);
                let path = "m/44'/60'/0'/0/0";
                let mnemonicWallet = ethers.Wallet.fromMnemonic(seed, path);

                const pk = mnemonicWallet.privateKey;


                let web3Provider = new ethers.providers.JsonRpcProvider(process.env.NETWORK_RPC);

                let wallet = new ethers.Wallet(pk, web3Provider);

                // We connect to the Contract using a Provider, so we will only
                // have read-only access to the Contract
                let contract = new ethers.Contract(contractAddress, abi, web3Provider);

                let contractKICKBACK = new ethers.Contract(kickback_addr, kickback_abi, web3Provider);
                let contractWithSigner = contractKICKBACK.connect(wallet);
                let contractLikeSigner = contract.connect(wallet);

                let currentValue = contract.balanceOf(wallet.address).then(result => {
                    let balancex = parseInt(result / 1e18);

                    if (balance <= balancex) {
                        contract.allowance(wallet.address, process.env.KICKBACK_ADDR).then(approve => {
                            // console.log(approve/10e17);
                            if (approve / 10e17 === 0) {
                                web3Provider.getTransactionCount(wallet.address).then(nonce => {
                                    console.log(nonce);

                                    contractLikeSigner.approve(process.env.KICKBACK_ADDR, '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff', {nonce: nonce}).then(approve => {
                                        contractWithSigner.depositToken(process.env.CONTRACT_LIKE, new ethers.utils.parseEther(balance.toString()), to, 'kickback', {nonce: nonce + 1}).then(deposit => {
                                            console.log(deposit);
                                            res.json({statusCode: 200, result: 'success', tx: deposit});
                                        });
                                    })
                                });
                            } else {
                                web3Provider.getTransactionCount(wallet.address).then(nonce => {
                                    contractWithSigner.depositToken(process.env.CONTRACT_LIKE, new ethers.utils.parseEther(balance.toString()), to, 'kickback', {nonce: nonce}).then(deposit => {
                                        console.log(deposit);
                                        res.json({statusCode: 200, result: 'success', tx: deposit});
                                    });
                                });
                            }
                        })
                    } else {
                        res.json({"status": 200, "reason": "balance ไม่พอ"});
                    }
                }).catch(e => {
                    console.log(e);
                    let resultToFront = {
                        'result': 'error',
                        'type': 'getBalance',
                        'data': e
                    };
                    res.json({"status": 204, "result": resultToFront});
                });
            });

        } else {
            res.json({statusCode: 404});
        }
    });

    function getAdminKickback() {
        return new Promise((resolve, reject) => {
            var con = mysql.createConnection({
                host: process.env.RDS_HOST,
                user: process.env.RDS_USERNAME,
                password: process.env.RDS_PASSWORD,
                database: process.env.RDS_DATABASE
            });

            con.connect();

            const sql = "SELECT * FROM adminBCT WHERE BU = ?";
            con.query(sql, ['KICKBACK21CT'], function (err, result, fields) {

                if (err) {
                    console.log(err);
                    $log('kickback : ' + err);
                    con.end();
                    reject();

                }
                const callback = {
                    result: result
                }
                resolve(callback);

            });


        });
    }

    app.post('/addWhitelist', async function (req, res) {
        let name = req.body.name;
        let phone = req.body.phone;
        let tier = req.body.tier;
        let level = req.body.level;
        let pkg_ref = req.body.pkg_ref;
        let pkg_ref_name = req.body.pkg_ref_name;
        let pkg_email = req.body.pkg_email;
        let policy = req.body.policy;
        let bank = req.body.bank;
        let bank_name = req.body.bank_name;
        let bookbank = req.body.bookbank;

        console.log("running.. addWhitelist");
        let db = admin.firestore();
        let sandbox = db.collection("sandbox");
        let now = new Date().getTime();
        let datetime = moment.tz(now, "Asia/Bangkok").format("DD-MM-YYYY hh:mm:ss");
        let snapshot = await sandbox.doc(tier).collection("whitelist").where("phone", "==", phone).get();
        if (snapshot.empty) {
            let data = {
                name: name,
                phone: phone,
                pkg_ref: pkg_ref,
                pkg_ref_name: pkg_ref_name,
                pkg_email: pkg_email,
                policy: policy,
                bank: bank,
                bank_name: bank_name,
                bookbank: bookbank,
                timestamp: now,
                create_time: datetime,
                level: level
            }
            try {
                let add = await sandbox.doc(tier).collection("whitelist").add(data);
                data["id"] = add.id;
                res.json({"status": 200, result: data});
            } catch (e) {
                console.log(e)
                res.json({"status": 404, result: e});
            }
        } else {
            // console.log("มีเบอร์นี้แล้ว")
            res.json({"status": 204, result: "หมายเลข " + phone + " นี้อยู่ใน " + tier + " แล้ว"});
        }
    });

    app.post('/removeWhitelist', async function (req, res) {
        let uid = req.body.uid;
        let tier = req.body.tier;
        let detail = req.body.detail;

        console.log("running.. removeWhitelist");
        let db = admin.firestore();
        let sandbox = db.collection("sandbox");
        let snapshot = await sandbox.doc(tier).collection("whitelist").doc(uid).get();
        if (snapshot.exists) {
            let data = snapshot.data();
            data["detail"] = detail;
            try {
                await sandbox.doc(tier).collection("disable").doc(uid).set(data);
                await sandbox.doc(tier).collection("whitelist").doc(uid).delete();
                res.json({"status": 200, result: "success"});
            } catch (e) {
                console.log(e)
                res.json({"status": 404, result: e});
            }
        } else {
            // console.log("มีเบอร์นี้แล้ว")
            res.json({"status": 204, result: "ไม่พบหมายเลข " + uid + " นี้ใน " + tier});
        }
    });

    app.post('/removeBlacklist', async function (req, res) {
        let uid = req.body.uid;
        let collection = req.body.collection;
        let detail = req.body.detail;

        console.log("running.. removeBlacklist");
        let db = admin.firestore();
        let sandbox = db.collection(collection);
        let colDis = collection.replace('/blacklist', '/disable');
        let disable = db.collection(colDis);
        let snapshot = await sandbox.doc(uid).get();
        if (snapshot.exists) {
            let data = snapshot.data();
            data["detail"] = detail;
            try {
                await disable.doc(uid).set(data);
                await sandbox.doc(uid).delete();
                res.json({"status": 200, result: "success"});
            } catch (e) {
                console.log(e)
                res.json({"status": 404, result: e});
            }
        } else {
            // console.log("มีเบอร์นี้แล้ว")
            res.json({"status": 204, result: "ไม่พบหมายเลข " + uid + " นี้ใน " + tier});
        }
    });

    app.post('/addWhitelistEmail', async function (req, res) {
        let name = req.body.name;
        let email = req.body.email;
        let tier = req.body.tier;
        let pkg_ref = req.body.pkg_ref;
        let pkg_ref_name = req.body.pkg_ref_name;
        let pkg_email = req.body.pkg_email;
        let policy = req.body.policy;

        console.log("running.. addWhitelistEmail");
        let db = admin.firestore();
        let sandbox = db.collection("sandbox");
        let now = new Date().getTime();
        let datetime = moment.tz(now, "Asia/Bangkok").format("DD-MM-YYYY hh:mm:ss");
        let snapshot = await sandbox.doc(tier).collection("whitelist").where("email", "==", email).get();
        if (snapshot.empty) {
            let data = {
                name: name,
                email: email,
                pkg_ref: pkg_ref,
                pkg_ref_name: pkg_ref_name,
                pkg_email: pkg_email,
                policy: policy,
                timestamp: now,
                create_time: datetime,
            }
            try {
                let add = await sandbox.doc(tier).collection("whitelist").add(data);
                data["id"] = add.id;
                res.json({"status": 200, result: data});
            } catch (e) {
                console.log(e)
                res.json({"status": 404, result: e});
            }
        } else {
            // console.log("มีเบอร์นี้แล้ว")
            res.json({"status": 204, result: "Email " + email + " นี้อยู่ใน " + tier + " แล้ว"});
        }
    });

    function getWhitelistPKGfromRDS(phoneNumber) {
        return new Promise((resolve, reject) => {
            var con = mysql.createPool({
                connectionLimit: 1000,
                connectTimeout: 60 * 60 * 1000,
                acquireTimeout: 60 * 60 * 1000,
                timeout: 60 * 60 * 1000,
                host: process.env.RDS_HOST_AGS,
                user: process.env.RDS_USERNAME_AGS,
                password: process.env.RDS_PASSWORD_AGS,
                database: 'PPP7'
            });
            const sql = "SELECT phone_like,token_Line FROM PKGemployee WHERE status != 'N' AND phone_like = ?";
            con.query(sql, [phoneNumber], function (err, result) {
                if (err) {
                    console.log("getWhitelistPKGfromRDS error")
                    console.log(err);
                    // $log('kickback : '+ err);
                    return reject({"status": 404, "result": err});
                }
                if (result.length > 0) {
                    return resolve({
                        "status": 200,
                        "tier": "tier1",
                        "level": "2",
                        "token_Line": result[0].token_Line === null ? "" : result[0].token_Line
                    });
                } else {
                    return resolve({"status": 204, "result": "ไม่พบข้อมูล"});
                }
            });
        });
    }

    app.post('/getWhitelistPKG', async function (req, res) {
        let phoneNumber = req.body.phoneNumber;
        console.log("running.. getWhitelistPKG");
        if (phoneNumber != undefined && phoneNumber != "" && phoneNumber != null) {
            let result = await getWhitelistPKGfromRDS(phoneNumber);
            if (result) {
                res.json(result);
            } else {
                res.json(result);
            }
        } else {
            res.json({"status": 400, "result": "phoneNumber undefined."})
        }
    });

    function getTierBUfromRDS(address) {
        return new Promise((resolve, reject) => {
            var con = mysql.createPool({
                connectionLimit: 1000,
                connectTimeout: 60 * 60 * 1000,
                acquireTimeout: 60 * 60 * 1000,
                timeout: 60 * 60 * 1000,
                host: process.env.RDS_HOST_AGS,
                user: process.env.RDS_USERNAME_AGS,
                password: process.env.RDS_PASSWORD_AGS,
                database: 'likecoin'
            });
            const sql = "SELECT wallet_type FROM adminBCT WHERE address like ? and wallet_type in ('Fund','TEAM','invest')";
            con.query(sql, [address], function (err, result) {
                if (err) {
                    console.log("getTierBUfromRDS error")
                    console.log(err);
                    // $log('kickback : '+ err);
                    reject({"status": 404, "result": err});
                }
                if (result.length > 0) {
                    resolve({"status": 200, "tier": result[0].wallet_type});
                } else {
                    resolve({"status": 204, "result": "ไม่พบข้อมูล"});
                }
            });
        });
    }

    app.post('/getTierBUfromRDS', async function (req, res) {
        let address = req.body.address;
        console.log("running.. getTierBUfromRDS");
        if (address != undefined && address != "" && address != null) {
            let result = await getTierBUfromRDS(address);
            if (result) {
                res.json(result);
            } else {
                res.json(result);
            }
        } else {
            res.json({"status": 400, "result": "address undefined."})
        }
    });

    function checkFeeFromAddress(address) {
        return new Promise((resolve, reject) => {
            request({
                method: 'POST',
                uri: 'https://getfeetest.prachakij.com/sendEth',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                form: {
                    address: address
                }
            }, (err, httpResponse, body) => {
                if (err) {
                    console.log(err);
                } else {
                    console.log(body);
                    return resolve(body);
                }
            });
        })
    }

    app.post('/checkFeeFromAddress', async function (req, res) {
        let address = req.body.address;
        console.log("running.. checkFeeFromAddress");
        if (address != undefined && address != "" && address != null) {
            let result = await checkFeeFromAddress(address);
            if (result) {
                res.json(result);
            } else {
                res.json(result);
            }
        } else {
            res.json({"status": 400, "result": "address undefined."})
        }
    });

    async function getTierConfigByPhone(phone) {
        let data = await getWhitelistPKGfromRDS(phone);
        let tier = data.status == 200 ? data.tier : "normal";
        let level = data.status == 200 ? data.level : "1";
        let resArray = [];
        let db = admin.firestore();
        let col = db.collection("tierController");
        let snapshot = await col.doc("controller").collection(tier).get();
        if (snapshot.empty) {
            return {"status": 204, "result": "tier undefined."}
        } else {
            snapshot.docs.forEach(x => {
                let datas = {page: x.id, config: x.data()}
                resArray.push(datas);
            });
            return {"status": 200, "tier": data.tier, "level": level, "result": resArray};
        }
    }

    app.post('/getTierConfigByPhone', async function (req, res) {
        let phoneNumber = req.body.phoneNumber;
        console.log("running.. getTierConfigByPhone");
        if (phoneNumber != undefined && phoneNumber != "" && phoneNumber != null) {
            let result = await getTierConfigByPhone(phoneNumber);
            if (result) {
                res.json(result);
            }
        } else {
            res.json({"status": 400, "result": "phoneNumber undefined."})
        }
    });

    async function tierConfigByPhone(phone) {
        let data = await getWhitelistPKGfromRDS(phone);
        let tier = data.status == 200 ? data.tier : "normal";
        let level = data.status == 200 ? data.level : "1";
        let resArray = [];
        let db = admin.firestore();
        let col = db.collection("tierController");
        let snapshot = await col.doc("controller").collection(tier).get();
        if (snapshot.empty) {
            return {"result": "tier undefined."}
        } else {
            snapshot.docs.forEach(x => {
                let datas = {page: x.id, config: x.data()}
                resArray.push(datas);
            });
            return {"tier": data.tier, "level": level, "result": resArray};
        }
    }

    app.get('/tierConfigByPhone', async function (req, res) {
        let phoneNumber = req.query.phoneNumber;
        console.log("running.. tierConfigByPhone");
        if (phoneNumber != undefined && phoneNumber != "" && phoneNumber != null) {
            let result = await tierConfigByPhone(phoneNumber);
            if (result) {
                res.json(result);
            }
        } else {
            res.json({"result": "phoneNumber undefined."})
        }
    });

    app.post('/getInfoMigrate', auth, function (req, res) {
        const phoneNumber = req.body.phoneNumber;
        console.log("running.. getInfoMigrate");
        if (phoneNumber != undefined && phoneNumber != "" && phoneNumber != null) {
            const db = admin.firestore();
            let resultToFront = {
                'type': 'getInfoMigrate',
                'migratable': false
            }
            var refUser = db.collection("addressDNS").where('phoneNumber', '==', phoneNumber);
            refUser.get().then(snapshot => {
                if (snapshot.empty) {
                    resultToFront['detail'] = 'ไม่พบข้อมูลผู้ใช้งาน';
                    res.json({status: 404, result: resultToFront});
                } else {
                    snapshot.forEach(async doc => {
                        let checkTier = await getWhitelistPKGfromRDS(phoneNumber);
                        if (checkTier.status == 200) {
                            resultToFront['detail'] = 'เป็นลูกค้า tier1';
                            res.json({"status": 400, "result": resultToFront});
                            return resultToFront;
                        } else {
                            let migrated = doc.data().migrated;
                            if (!migrated) {
                                let uid = doc.id;
                                let address = doc.data().address;
                                let displayName = doc.data().name;
                                let phoneNumber = doc.data().phoneNumber;
                                let data = {
                                    'uid': uid,
                                    'address': address,
                                    'displayName': displayName,
                                    'phoneNphoneNumberumber': phoneNumber
                                }
                                resultToFront['data'] = data;
                                resultToFront['migratable'] = true;
                                resultToFront['detail'] = 'พบข้อมูลผู้ใช้งาน';
                                res.json({"status": 200, "result": resultToFront});
                                return resultToFront;
                            } else {
                                resultToFront['detail'] = 'ทำการ migrate แล้ว';
                                resultToFront['migrated'] = migrated;
                                res.json({"status": 400, "result": resultToFront});
                                return resultToFront;
                            }
                        }
                    });
                }
            });
        } else {
            resultToFront['detail'] = 'กรุณากรอกข้อมูลให้ถูกต้อง';
            res.json({status: 404, result: resultToFront});
        }
    });


    app.post('/infoTierLikeWallet', async (req, res)=>  {
        const uid = req.body.uid;
        const address = req.body.address;
        var role = "";
        try {
            var user = await admin.auth().getUser(uid);
            var resInfo =  await getWhitelistPKGfromRDS(user.phoneNumber);

            console.log(resInfo);
            if(resInfo.tier) {
                role = resInfo.tier;
            }else {
                role = "tier2";
            }
            res.json({status: 200, result: role});
        }catch (e) {
            var resInfo =  await getTierBUfromRDS(address);
            if(resInfo.status === 200) {
                role = "tier1";
            }else {
                role = "tier2";
            }
            res.json({status: 200, result: role});
        }

    });


    app.post('/migrate', auth, function (req, res) {
        const phoneNumber = req.body.phoneNumber;
        console.log("running.. migrate");
        if (phoneNumber != undefined && phoneNumber != "" && phoneNumber != null) {
            const db = admin.firestore();
            let resultToFront = {
                'type': 'migrate'
            }
            db.collection("users").where('phone_number', '==', phoneNumber).get().then(snapshot => {
                if (snapshot.empty) {
                    resultToFront['detail'] = 'ไม่พบข้อมูลผู้ใช้งาน';
                    res.json({status: 404, result: resultToFront});
                } else {
                    snapshot.forEach(async doc => {
                        let migrated = doc.data().migrated;
                        if (!migrated) {
                            try {
                                const batch = db.batch();
                                let uid = doc.id;
                                let data = doc.data();
                                let now = new Date().getTime();
                                let datetime = moment.tz(now, "Asia/Bangkok").format("DD-MM-YYYY hh:mm:ss");
                                data['migrated'] = now;
                                data['detail'] = `ทำการ migrate แล้วเมื่อ + ${datetime}`;
                                batch.set(db.collection("migration").doc(uid), data);
                                // batch.update(db.collection("users").doc(uid), {'_token':{},'migrated':now});
                                // batch.update(db.collection("addressDNS").doc(uid), {'migrated':now});
                                await batch.commit();
                                resultToFront['detail'] = 'ทำการ migrate แล้วเมื่อ ' + datetime;
                                resultToFront['migrated'] = now;
                                res.json({status: 200, result: resultToFront});
                            } catch (error) {
                                console.log('Error occurred while performing batch operations:');
                                console.log(error);
                                resultToFront['detail'] = 'ไม่สามารถทำการ migrate ได้';
                                res.json({status: 400, result: resultToFront});
                            }
                        } else {
                            resultToFront['detail'] = 'ทำการ migrate แล้ว';
                            resultToFront['migrated'] = migrated;
                            res.json({status: 400, result: resultToFront});
                            return resultToFront;
                        }
                    });
                }
            });
        } else {
            resultToFront['detail'] = 'กรุณากรอกข้อมูลให้ถูกต้อง';
            res.json({status: 404, result: resultToFront});
        }
    });

}