'use strict';

var admin = require("firebase-admin");
var serviceAccountOld = require("./oldlikepoint.json");

const dotenv = require('dotenv');
dotenv.config();
const express = require('express')
const app = express()
const bodyParser = require('body-parser');
var session = require('express-session')
var cryptico = require('cryptico');
var generator = require('generate-password');
var Promise = require('promise');
var await = require('await')``
var moment = require('moment-timezone');
// var moment = require('moment');
var elasticsearch = require('elasticsearch');
var await = require('await');
var request = require('request');
var bip39 = require('bip39')
var btcLib = require('bitcoinjs-lib');
var bs58check = require('bs58check');
const twilio = require('twilio');
var crypto = require('crypto'),
  algorithm = 'aes-256-gcm',
  password = process.env.ENCRYPTION_KEY;

  //ipfs
const ipfsClient = require('ipfs-http-client')
const ipfsCluster = require('ipfs-cluster-api-remove-name')
const fs = require('fs')
//yep
const algorithmToFront = 'aes-256-cbc';
var algorithmToFrontGCM = 'aes-256-gcm';
var serviceAccount = require("./"+process.env.FIREBASE);
const passwordToFront = process.env.passwordToFront;
const ivToFront = process.env.ivToFront;
// const passwordToFront = process.env.ENCRYPTION_KEY_FRONT;
// const ivToFront = process.env.IV_FRONT; 
const $log = require('log');
const bcrypt = require('bcrypt');
const IV_LENGTH = 16;
const ethers = require('ethers');
const accountSid = process.env.TWILIO_KEY;
const authToken = process.env.TWILIO_SECRET;
const clientTwilio = require('twilio')(accountSid, authToken);
const secrets = require('secret-sharing.js');
const uuidv4 = require('uuid/v4');
const randomstring = require("randomstring");
const uuidAPIKey = require('uuid-apikey');
const aws = require('aws-sdk');
const web3 = require('web3');
const ed25519 = require('ed25519-hd-key')
const mysql      = require('mysql');
const kms = new aws.KMS({
    accessKeyId: '********************', //credentials for your IAM user
    secretAccessKey: 'XtBvGx6pX+UssPhxbCc1xSrITpNf9IPQZuTIn/2a', //credentials for your IAM user
    region: 'ap-southeast-1'
});




//this function check phoneNumber exists from another application
let lc_check_exists = function lc_check_exists(phone_number, apiKey, secretKey) {
	return new Promise((resolve, reject) => {
		console.log('lc_check_exists');
	  if(apiKey != process.env.APIKEY_CONNECT && secretKey != process.env.SECRETKEY_CONNECT) {
	  	reject();
	  }else{
	    admin.auth().getUserByPhoneNumber(phone_number)
	      .then(function(userRecord) {
	        // See the UserRecord reference doc for the contents of userRecord.
	        //console.log('Successfully fetched user data:', userRecord.toJSON());
	        let data = {
	          statusCode:200,
	          result: true
	        }
	        resolve(data);
	      })
	      .catch(function(error) {
	        console.log('Error fetching user data:', error);
	        console.log(error.errorInfo.code);
	        if(error.errorInfo.code = 'auth/user-not-found'){
		        let data = {
		          statusCode:201,
		          result: false
		        }
		        resolve(data);	        	
	        }else{
	        	reject(error.errorInfo.code);
	        }

	      });

	  }		
	});

}

exports.lc_check_exists = lc_check_exists;