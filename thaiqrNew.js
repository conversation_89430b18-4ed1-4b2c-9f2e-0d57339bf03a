'use strict';


var admin = require("firebase-admin");
var serviceAccountOld = require("./oldlikepoint.json");

const dotenv = require('dotenv');
dotenv.config();
const express = require('express')
const app = express()
const bodyParser = require('body-parser');
var session = require('express-session')
var cryptico = require('cryptico');
var generator = require('generate-password');
var Promise = require('promise');
var await = require('await')``
var moment = require('moment-timezone');
// var moment = require('moment');
var elasticsearch = require('elasticsearch');
var await = require('await');
var request = require('request');
var bip39 = require('bip39')
var btcLib = require('bitcoinjs-lib');
var bs58check = require('bs58check');
const twilio = require('twilio');
var jwt = require('jsonwebtoken');
var crypto = require('crypto'),
  algorithm = 'aes-256-gcm',
  password = process.env.ENCRYPTION_KEY;

  //ipfs
const ipfsClient = require('ipfs-http-client')
const ipfsCluster = require('ipfs-cluster-api-remove-name')
const fs = require('fs')
//yep
const algorithmToFront = 'aes-256-cbc';
var algorithmToFrontGCM = 'aes-256-gcm';
var serviceAccount = require("./"+process.env.FIREBASE);
const passwordToFront = process.env.passwordToFront;
const ivToFront = process.env.ivToFront;
// const passwordToFront = process.env.ENCRYPTION_KEY_FRONT;
// const ivToFront = process.env.IV_FRONT; 
const $log = require('log');
const bcrypt = require('bcrypt');
const IV_LENGTH = 16;
const ethers = require('ethers');
const accountSid = process.env.TWILIO_KEY;
const authToken = process.env.TWILIO_SECRET;
const clientTwilio = require('twilio')(accountSid, authToken);
const secrets = require('secret-sharing.js');
const uuidv4 = require('uuid/v4');
const randomstring = require("randomstring");
const uuidAPIKey = require('uuid-apikey');
const aws = require('aws-sdk');
const web3 = require('web3');
const ed25519 = require('ed25519-hd-key')
const mysql      = require('mysql');
const kms = new aws.KMS({
    accessKeyId: '********************', //credentials for your IAM user
    secretAccessKey: 'XtBvGx6pX+UssPhxbCc1xSrITpNf9IPQZuTIn/2a', //credentials for your IAM user
    region: 'ap-southeast-1'
});
const { NonceManager } = require("@ethersproject/experimental");
// import { rateLimiterUsingThirdParty } from './middlewares';
var rateLimiterUsingThirdParty = require('./middlewares');
//console.log(JSON.stringify());

const auth = require('./middlewares/auth.js');

const contractAddressSmartContract = process.env.CONTRACT_LIKE;
const abiSmartContract = process.env.ABI_LIKE;

//delay function
let sleep = (time) => new Promise((resolve) => setTimeout(resolve, time));
// inQuireQR();



//testSSL();
function testSSL(){
console.log('testSSL');
var fs = require('fs')
var path = require('path')
let keyfile = path.join(__dirname, 'kbanktest.key');
let certificateFile = path.join(__dirname, 'kbanktest.crt');

  const options = {
    url: 'https://openapi-test.kasikornbank.com/exercise/ssl',
    agentOptions: {
      cert: fs.readFileSync(certificateFile),
      key: fs.readFileSync(keyfile),
    },
    headers: { 'Content-Type': 'application/json' ,
    			'x-test-mode':true,
    			'Authorization':'Bearer huSyK5xmZv7314rCtA1QtokmPjgt'

},
    body: JSON.stringify({
      partnerId: 'PTR1051673',
      partnerSecret: 'd4bded59200547bc85903574a293831b',
    }),
  };
  request.post(options, function(error, response, body) {
    if (error) {
      console.warn(error);
    } else {
      console.log(body);
    }
  });
}

function encryptKMS(buffer) {
    return new Promise((resolve, reject) => {
        const params = {
            KeyId: '5dde28c8-9a00-4871-bbba-0cde16ac46e4', // The identifier of the CMK to use for encryption. You can use the key ID or Amazon Resource Name (ARN) of the CMK, or the name or ARN of an alias that refers to the CMK.
            Plaintext: buffer// The data to encrypt.
        };
        kms.encrypt(params, (err, data) => {
            if (err) {
                reject(err);
            } else {
                resolve(data.CiphertextBlob);
            }
        });
    });
}

// testkey();
function testkey(){
	const seed = '';
	const type = 'sell-likepoint-AMH';
	const owner = 'FIN';

	     encryptKMS(new Buffer(seed,'utf-8')).then(seedHex => {
	     	const base64Seed = Buffer.from(seedHex).toString('base64');
	     	RetrievePKED2519(seed).then(pk =>{
	     	const address = retrieveAddress(pk);
				let data = [
					base64Seed,
					address.toLowerCase(),
					type,
					owner
				];
				
				insertAdminPK(data).then(result => {
					//success
					res.json({statusCode:200, result:'success', data:data});
				});	
	     	});
	     });		


};

app.post('/addAdminPK', function(req, res){
	const seed = bip39.generateMnemonic();
	const type = req.body.type;
	const owner = req.body.owner;
	const apiKey = req.body.apiKey;
	const secretKey = req.body.secretKey;
	console.log(seed);
	if(apiKey == process.env.apiKeyLIKE && secretKey == process.env.secretKeyLIKE){
	     encryptKMS(new Buffer(seed,'utf-8')).then(seedHex => {
	     	const base64Seed = Buffer.from(seedHex).toString('base64');
	     	RetrievePKED2519(seed).then(pk =>{
	     	const address = retrieveAddress(pk);
				let data = [
					base64Seed,
					address.toLowerCase(),
					type,
					owner
				];
				
				insertAdminPK(data).then(result => {
					//success
					res.json({statusCode:200, result:'success', data:data});
				});	
	     	});
	     });		

	}else{
		res.json({statusCode:200, result: 'Authenticate failed'});
	}

});

//โอนเหรียญไปให้ผู้ซื้อ
function transferBalanceToBuyer(buyer, amount){
	return new Promise((resolve, reject) =>{
		console.log(buyer);
		console.log(amount);

		getAdminPK().then(result =>{
		     	//step decode and retrieve seed
		     	console.log('get adminPK..');

		     	const decodeSeed = Buffer.from(result[0].pk, 'base64');
		     	console.log('decryptKMS...');
		     	decryptKMS(decodeSeed).then(rSeed =>{
					console.log('RetrievePKED2519...');
			     	RetrievePKED2519(rSeed.toString('utf-8')).then(pk =>{
	 			     	const address = retrieveAddress(pk).toLowerCase();
	 			     	console.log('starting transfers....');
	 			     	console.log(address);
	 			     	transfer(buyer, pk, amount*100).then(tx =>{
	 			     		console.log(tx);
	 			     		resolve(tx);
	 			     	}).catch(e =>{
	 			     	
		 			     		console.log(e);
		 			     		reject();	 			     			
	 			     	

	 			     	});
			     	});			     		
		     	});

		});
	});

}
function retrieveAddress(pk){
    let wallet = new ethers.Wallet(pk);
    return wallet.address;
}
let failCountTX = 0;
function transfer(to, pk, amount){
  return new Promise((resolve, reject) =>{
    let sleep = (time) => new Promise((resolve) => setTimeout(resolve, time))
    let url = process.env.NETWORK_RPC;
    let customHttpProvider = new ethers.providers.JsonRpcProvider(url);
    const abi = abiSmartContract;
    const contractAddress = contractAddressSmartContract;
    console.log('connect transfer to user');

    let wallet = new ethers.Wallet(pk, customHttpProvider);
    // let nonce = customHttpProvider.getTransactionCount(wallet.address);
    let contract = new ethers.Contract(contractAddress, abi, customHttpProvider);
    // if(round  === 1) {
    // 	nonce += 1;
    // }
    wallet = new NonceManager(wallet);
    let overrides = {

    // The maximum units of gas for the transaction to use
    gasLimit: 120000,

    // The price (in wei) per unit of gas
    gasPrice: ethers.utils.parseUnits('10', 'gwei'),
    // nonce: nonce

  };
    let contractWithSigner = contract.connect(wallet, overrides);
    console.log(wallet.address);

    try{
        console.log('transfer!');
        // console.log(to);
        console.log(amount.toString());
        // console.log(pk);
        contractWithSigner.transfer(to, ethers.utils.parseEther(amount.toString())).then(tx =>{
          console.log(tx);
          resolve(tx);
        }).catch(e =>{
        	sleep(3000).then(repeat =>{
				if(failCountTX%100 === 0){
					failedTx('transferBalanceToBuyer11', 'repeat auto');
				}
				failCountTX++;
        		return resolve(transfer(to, pk, amount));
        	})	     	
			// if(round == 0) {
	 	// 		return resolve(transfer(buyer, amount, 1));
	 	// 	}else{   
	 	// 	failedTx('transferBalanceToBuyer1', e);	     	
			//     console.log(e);
		 //        reject()
		 //    }
		
        });
    }catch(e) {
    	failedTx('transferBalanceToBuyer2', e);	
      	console.log(e);
       // $log('transfer : '+ to + " amount: "+ amount.toString());
       reject();
      //  sleep(5000).then(() =>{
      //   return resolve(transfer(buyer, amount, 1));
      // });
    };
  
  });


}
function RetrievePKED2519(seed) {
  return new Promise((resolve, reject) => {
    try{

    const hexSeed = bip39.mnemonicToSeedSync(seed).toString('hex');

    // const privateKey = ed25519.derivePath("m/0'/2147483647'/0'/2147483646'/0'", hexSeed);
      let path = "m/44'/60'/0'/0/0";
      let mnemonicWallet = ethers.Wallet.fromMnemonic(seed, path);

      resolve(mnemonicWallet.privateKey);
    } catch(e) {
    	console.log(e);
      reject(e);
    }
       
  });
}
function decryptKMS(buffer) {
    return new Promise((resolve, reject) => {
        const params = {
            CiphertextBlob: buffer// The data to dencrypt.
        };
        kms.decrypt(params, (err, data) => {
            if (err) {
                reject(err);
            } else {
                resolve(data.Plaintext);
            }
        });
    });
}


function getAdminPK(){
	return new Promise((resolve, reject) =>{
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	   
	    con.connect();	
	   	const sql = "SELECT pk, address FROM pk_admin_gateway WHERE owner = 'FIN' and type='sell-likepoint-AMH'";
	    con.query(sql, function (err, result, fields) {
		      if (err) {
		        console.log(err);
		        $log('getAdminPK : '+ err);
		        con.end();
		        reject();
		      }
		      con.end();
			  
			  resolve(result);
			}); 		
	});
};

function getTXuid(parnerTxnUid){
	return new Promise((resolve, reject) =>{
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	   
	    con.connect();	
	   	const sql = "SELECT address, txnAmount FROM invoice_buy_promptpay WHERE partnerTxnUid = ? AND loopPaid = ?";
	    con.query(sql, [parnerTxnUid, 0], function (err, result, fields) {
		      if (err) {
		        console.log(err);
		        $log('getTXuid : '+ err);
		        con.end();
		        reject();
		      }
		      con.end();
			  
			  resolve(result);
			}); 		
	});
};


/**
@step 1 สร้างรายการ
*/
let createOrderBuy = function createOrderBuy(_token, walletNumber, address, amount){
	return new Promise((resolve, reject) =>{
		const db = admin.firestore();
		console.log(_token);
	   admin.auth().verifyIdToken(_token)
	    .then(function (decodedToken){

	      request({
	          method: 'POST',
	          uri: 'https://notify-api.line.me/api/notify',
	          headers: {
	              'Content-Type': 'application/x-www-form-urlencoded'
	          },
	          auth: {
	              'bearer': 'KyLSKrqLIEHnK74LHtyIbz2Y3OU8qQf72Pu54mIrbnQ'
	          },
	          form: {
	              message: "มีรายการซื้อ LIKE ผ่าน OPEN API" + " \n เบอร์โทร : "+ decodedToken.phone_number + "\n จำนวนเงิน : " + amount + " บาท" 
	          }
	      }, (err, httpResponse, body) => {
	          if (err) {
	              console.log(err);
	          } else {

	          }
	      });  	    	
	    	//console.log(decodedToken);
	            const userRef = db.collection('addressDNS').doc(walletNumber == '0' ? decodedToken.uid : decodedToken.uid+walletNumber)
	            .get().then(snapshot => {
	            	try{
						if(address == snapshot.data().address){
		            		console.log(snapshot.data().address);
			            	generateQR(address, 'mainnet', amount, 'LKE', decodedToken.phone_number).then(data =>{
			            		console.log('callback to createOrderBuy');
			            		console.log(data);
			            		let result = {
			            				qrcode:data.body.qrCode.toString(),
			     						partnerTxnUid: data.body.partnerTxnUid.toString(),
			     						requestDt: data.requestDt.toString(),
			     						merchantId: data.merchantId.toString(),
			     						terminalId: data.terminalId.toString(),
			     						QRpartnerTxnUid: data.partnerTxnUid.toString(),
			 							qrType: data.qrType.toString()
			            		}
			            		let backData = [];
			            		backData.push(result);
			            		let callback = {statusCode:200, result: backData};
			     				// res.json();
			     				console.log('callback ');
			     				console.log(callback);
			     				resolve(callback);
			     			}).catch(e=>{
			     				console.log(e);
	            				reject();
			     			});
		            	}
	            	}catch(e){
	            		console.log(e);
	            		reject();
	            	}
	            		
	            });
	          
	    
	    }).catch(e=>{
	    	console.log(e);
	    	reject();
	    });
	});


};

function generateQR(address, message, amount, name ,phone_number){
	return new Promise((resolve, reject) =>{
		const created_time = moment.tz(moment().toISOString(), "Asia/Bangkok").format();
		let test = {
		    requestDt: created_time,
		    txnAmount: amount,
		    create_user:name,
		    phone_number: phone_number,
		    bu:'TCT',
		    type_mode:'TCT',
		    reference1: 'LikeWallet',
		    reference2: address,
		    reference3: 'BUYLIKE',
		    reference4: null,
		    metadata: message   
		  };
		  console.log(test);
		var options = { method: 'POST',
		  url: process.env.URL_OPEN_API+'/requestPayment',
		  headers: {
		    'cache-control': 'no-cache',
		    'Content-Type': 'application/json',
		  },
		  rejectUnauthorized: false,
		  body: {
		    requestDt: created_time,
		    txnAmount: amount,
		    create_user:name,
		    phone_number: phone_number,
		    bu:'TCT',
		    type_mode:'TCT',
		    reference1: 'LikeWallet',
		    reference2: address,
		    reference3: 'BUYLIKE',
		    reference4: null,
		    metadata: message   
		  },
		  json: true
		};

		request(options, (error, response, body) => {
				  if (error) throw new Error(error);
				  console.log(body);
				  console.log(address);
				  console.log(name);
				  console.log(phone_number);
				  console.log(body.partnerTxnUid);
				  console.log(created_time);
				  console.log(process.env.merchantId);
				  console.log(process.env.terminalId);
				  console.log(process.env.qrType);
				  console.log(amount);
				  console.log(process.env.partnerId);
				insertTx(address, name, phone_number, body.partnerTxnUid, created_time, process.env.merchantId, process.env.terminalId, process.env.qrType, amount, 'THB', message, process.env.partnerId, 'BUYLIKE').then(data =>{
					console.log('insert');
			

					updateTx(body, body.partnerTxnUid).then(datax=>{
					
				      // request({
				      //     method: 'POST',
				      //     uri: 'https://notify-api.line.me/api/notify',
				      //     headers: {
				      //         'Content-Type': 'application/x-www-form-urlencoded'
				      //     },
				      //     auth: {
				      //         'bearer': 'KyLSKrqLIEHnK74LHtyIbz2Y3OU8qQf72Pu54mIrbnQ'
				      //     },
				      //     form: {
				      //         message: "รายการ : "+ body.partnerTxnUid + "สร้าง QR code สำเร็จ !.." + "\n address : "+ address
				      //     }
				      // }, (err, httpResponse, body) => {
				      //     if (err) {
				      //         console.log(err);
				      //     } else {

				      //     }
				      // });  	   				
						let newBody = {
						  	body: body,
						  	requestDt: created_time,
						  	merchantId: process.env.merchantId,
						  	terminalId: process.env.terminalId,
						  	qrType: process.env.qrType,
						  	partnerTxnUid: body.partnerTxnUid
						}
							console.log(newBody);
						console.log('updatedTxSuccess');
						try{
							resolve(newBody);		
						}catch(e){
							console.log(e);
							reject();
						}
									

					});
				});
		});	
	});
}

function updateTx(body, partnerTxnUid) {
	return new Promise((resolve, reject) => {
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	   
	    con.connect();
			const sql = "UPDATE  invoice_buy_promptpay SET partnerTxnUid = ?, statusCode = ?, errorCode = ?, errorDesc = ?, accountName = ?, qrCode = ? WHERE partnerTxnUid = ? AND partnerId = ?";
		     con.query(sql,[body.partnerTxnUid, body.statusCode, body.errorCode, body.errorDesc, body.accountName, body.qrCode, partnerTxnUid, body.partnerId], function (err, result, fields) {
		      if (err) {
		        console.log(err);
		        $log('updateTx : '+ err);
		        con.end();
		        reject();
		      	// return resolve(insertTx());
		      }
		      console.log('updateTx');
		      con.end();
			  
			  resolve(true);
			});
	});
}
function getYearID(_year) {
	return new Promise((resolve, reject) => {
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	     
	    con.connect();
    	let _year = moment().format('YYYY');
		
	    const sql = "SELECT * FROM invoice_year WHERE year = ? ORDER BY running DESC LIMIT 1";

	     con.query(sql,[_year], function (err, result, fields) {
	    
	      if (err) {
	        console.log(err);
	        $log('getYearID : '+ err);
	        con.end();
	        sleep(4000).then(() => {
	      		return resolve(getYearID());
	      	});
	      }
	      if(result.length == 0){
	      	con.end();
			createNewYear(_year).then(running_year =>{
				resolve(running_year); 
			});
	      }else{
		    con.end();
			resolve(result[0]['running']);      	
	      }

		});	
	});
}
function createNewYear(_year){
	return new Promise((resolve, reject) =>{
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	     
	    con.connect();
	   	const sql = "INSERT INTO invoice_year SET year = ?";

	     con.query(sql,[_year], function (err, result, fields) {
	    
	      if (err) {
	        console.log(err);
	        $log('createNewYear : '+ err);
	        con.end();
	        sleep(4000).then(() => {
	      		return resolve(createNewYear());
	      	});
	      }
	      resolve(result.insertId);
	  });
	});
}
function insertTx(address, name, phone_number, partnerTxnUid, created_time, merchantId, terminalId, qrType, amount, currency, message, partnerId, ref3){
	return new Promise((resolve, reject) => {
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	   
	    con.connect();
    	let _year = moment().format('YYYY');
		getYearID().then(yearID => {
		    const sql = "INSERT INTO invoice_buy_promptpay (name, phone_number, address, partnerTxnUid, year_id, status, requestDt, merchantId, terminalId, qrType, txnAmount, txnCurrencyCode, reference1, reference2, metadata, partnerId, reference3) VALUES ?";
		     con.query(sql,[[[name, phone_number, address, partnerTxnUid, yearID.toString(), 1, created_time, merchantId, terminalId, qrType, amount, currency, partnerTxnUid, address, message, partnerId, ref3]]], function (err, result, fields) {
		      if (err) {
		        console.log(err);
		        $log('getLastIndex : '+ err);
		        con.end();
		        reject();
		      	// return resolve(insertTx());
		      }
		      con.end();
			  
			  resolve(true);
			});
		});
	});
}

function insertAdminPK(data){
	return new Promise((resolve, reject) =>{
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	   
	    con.connect();	
	    const sql = "INSERT INTO pk_admin_gateway (pk, address, type, owner) VALUES ?";
	    con.query(sql,[[data]], function (err, result, fields) {
		      if (err) {
		        console.log(err);
		        $log('insertAdminPK : '+ err);
		        con.end();
		        reject();
		      }
		      con.end();
			  
			  resolve(true);
			});
	});
}


/// INQUIRY ZONE START ####
//##########################
//##########################

let inquiryBuyAdmin = function inquiryBuyAdmin(partnerTxnUid, origPartnerTxnUid) {
	return new Promise((resolve, reject) => {
		const db = admin.firestore();
		const created_time = moment.tz(moment().toISOString(), "Asia/Bangkok").format();
		const requestDt = created_time;
	  
	    	console.log('pass verify');
	    	try{
				inQuiryQR(partnerTxnUid, requestDt, origPartnerTxnUid).then(data =>{
					console.log(data);
					let callback = {
						result:data
					}
					resolve(callback);
				});
	    	}catch(e) {
	    		reject();
	    	}

	});
};


function checkBuy(partnerTxnUid) {
	return new Promise((resolve, reject) =>{
			var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_OPEN_API
	    });
	   
	    con.connect();
			const sql = "SELECT create_user, partnerTxnUid, paid FROM prachakij_survey WHERE create_user = ? AND partnerTxnUid = ?";
		     con.query(sql,['LKE', partnerTxnUid], function (err, result, fields) {
		     	con.end();
		     	if(result.length > 0){
		     		if(result[0].paid == "PAID"){
			     		let callback = {
		     			statusCode:200,
						body:'',
						tx:''
		     		}

		     		resolve(callback);	     			
		     	}else{
		     		let callback = {
	  					statusCode:205,
	  					reason:'not paid'
	  				}		     		
		     		reject(callback);	
		     	}

		     	}else{
	  				let callback = {
	  					statusCode:205,
	  					reason:'not paid'
	  				}		     		
		     		reject(callback);
		     	}
		     })

	});
}
let inquiryBuy = function inquiryBuy(_token, partnerTxnUid, origPartnerTxnUid) {
	return new Promise((resolve, reject) => {
		const db = admin.firestore();
		const created_time = moment.tz(moment().toISOString(), "Asia/Bangkok").format();
		const requestDt = created_time;

	   admin.auth().verifyIdToken(_token)
	    .then(function (decodedToken){
	    	console.log('pass verify');
			try{
		    	checkBuy(partnerTxnUid).then(data =>{
					let callback = {
						result: data
					}
					resolve(callback);
		    	})
	    	}catch(e){
	    		reject();
	    	};
	   //  	try{
				// inQuiryQR(partnerTxnUid, requestDt, origPartnerTxnUid).then(data =>{
				// 	console.log(data);
				// 	let callback = {
				// 		result:data
				// 	}
				// 	resolve(callback);
				// })
	   //  	}catch(e){
	   //  		reject();
	   //  	};
		}).catch(e =>{
			reject(e);
		});
	});
};



function updatePaid(partnerId, partnerTxnUid, txnStatus) {
	return new Promise((resolve, reject) => {
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	   
	    con.connect();
			const sql = "UPDATE  invoice_buy_promptpay SET loopPaid = ? ,txnStatus = ?, status= ? WHERE partnerTxnUid = ? AND partnerId = ?";
		     con.query(sql,[1, txnStatus, 2, partnerTxnUid, partnerId], function (err, result, fields) {
		      if (err) {
		        console.log(err);
		        $log('updateTx : '+ err);
		        con.end();
		        reject();
		      	// return resolve(insertTx());
		      }
		      con.end();
			  
			  resolve(true);
			});
	});
}

function updateTxid(partnerId, partnerTxnUid, tx) {
	return new Promise((resolve, reject) => {
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	   
	    con.connect();
			const sql = "UPDATE  invoice_buy_promptpay SET loopPaid = ?, txID = ?, status= ? WHERE partnerTxnUid = ? AND partnerId = ?";
		     con.query(sql,[2, tx.hash, 3, partnerTxnUid, partnerId], function (err, result, fields) {
		      if (err) {
		        console.log(err);
		        $log('updateTx : '+ err);
		        con.end();
		        reject();
		      	// return resolve(insertTx());
		      }
		      con.end();
			  
			  resolve(true);
			});
	});
}
		// const created_time = moment.tz(moment().toISOString(), "Asia/Bangkok").format();
		// const requestDt = created_time;
	  
// inQuiryQR('PMS202112300411',requestDt,'PMS202112300411');
function inQuiryQR(partnerTxnUid, requestDt, origPartnerTxnUid){
	return new Promise((resolve, reject) =>{
		console.log('start inQuiryQR');
		var options = {
		  method: 'POST',
		  url: process.env.URL_OPEN_API + '/inquiry',
		  headers: {
		    'cache-control': 'no-cache',
		    'Content-Type': 'application/json',
		  },
		  rejectUnauthorized: false,
		  body: {
		    partnerTxnUid: partnerTxnUid,
		    requestDt: requestDt,
		    origPartnerTxnUid: origPartnerTxnUid
		  },
		  json: true
		};
		console.log(origPartnerTxnUid);
		request(options,  (error, response, body) => {
		  if (error) throw new Error(error);
		  console.log(body);
		  if(body.reason == 'PAID'){		  	
		  	//update status paid
		  		getTXuid(partnerTxnUid).then(txuid =>{
		  			console.log(txuid);
		  			if(txuid.length === 0){
		  				let data ={
		  					statusCode:204,
		  					reason:'This partnerTxnUid is success !'
		  				}
		  				resolve(data);
		  			}else{

		  				console.log('starting updatePaid..');
					updatePaid(process.env.partnerId, partnerTxnUid, body.reason).then(data =>{
					      request({
					          method: 'POST',
					          uri: 'https://notify-api.line.me/api/notify',
					          headers: {
					              'Content-Type': 'application/x-www-form-urlencoded'
					          },
					          auth: {
					              'bearer': 'KyLSKrqLIEHnK74LHtyIbz2Y3OU8qQf72Pu54mIrbnQ'
					          },
					          form: {
					              message: "รายการ : "+ partnerTxnUid + " จ่ายเงินสำเร็จ !!" 
					          }
					      }, (err, httpResponse, body) => {
					          if (err) {
					              console.log(err);
					          } else {

					          }
					      });  							
						console.log('starting transferBalanceToBuyer...');
							transferBalanceToBuyer(txuid[0].address, txuid[0].txnAmount).then(tx =>{
		  											
								console.log('starting updateTxid....');
								checkBalance();
								updateTxid(process.env.partnerId, partnerTxnUid, tx).then(updateTx =>{
			  						request({
								          method: 'POST',
								          uri: 'https://notify-api.line.me/api/notify',
								          headers: {
								              'Content-Type': 'application/x-www-form-urlencoded'
								          },
								          auth: {
								              'bearer': 'KyLSKrqLIEHnK74LHtyIbz2Y3OU8qQf72Pu54mIrbnQ'
								          },
								          form: {
								              message: "รายการ : "+ partnerTxnUid + " โอน LIKE สำเร็จ : " + "https://scan.tomochain.com/txs/"+tx.hash
								          }
								      }, (err, httpResponse, body) => {
								          if (err) {
								              console.log(err);
								          } else {
								          
								          }
								      });  

									let callBack = {
										statusCode:200,
										body:body,
										tx:tx
									}
									resolve(callBack);
								}).catch(e=>{
			  						failedTx(partnerTxnUid, ' updateTxid');								
									reject();
								});;
							}).catch(e=>{
								failedTx(partnerTxnUid, ' transferBalanceToBuyer');	
								reject();
							});
					  	}).catch(e=>{
					  			failedTx(partnerTxnUid, ' updatePaid');	
								reject();
					});;
		  			}

				}).catch(e=>{
					failedTx(partnerTxnUid, ' getTXuid');	
							reject();
						});
		  }else{
	  			resolve(body);
		  }
	
		});
	});
};

function failedTx(partnerTxnUid, e){
	request({
      method: 'POST',
      uri: 'https://notify-api.line.me/api/notify',
      headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
      },
      auth: {
          'bearer': 'KyLSKrqLIEHnK74LHtyIbz2Y3OU8qQf72Pu54mIrbnQ'
      },
      form: {
          message: "*รายการ : "+ partnerTxnUid + " เกิดข้อผิดพลาดโปรดตรวจสอบ*" + e
      }
  }, (err, httpResponse, body) => {
      if (err) {
          console.log(err);
      } else {

      }
  });  		
}

function alertAdmin(){
	request({
      method: 'POST',
      uri: 'https://notify-api.line.me/api/notify',
      headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
      },
      auth: {
          'bearer': 'KmQX4YiQFLUTMYCcdcGCHo9dSo0TSdGdeTCH481WJx0'
      },
      form: {
          message: "Likepoint กระเป๋า Sell Likepoint AMH ต่ำกว่า 5,000,000 Likepoint\n กรุณาเติมเหรียญ"
      }
  }, (err, httpResponse, body) => {
      if (err) {
          console.log(err);
      } else {

      }
  });  		
}

function checkBalance() {
	// ******************************************
	  let url = process.env.NETWORK_RPC;
	let customHttpProvider = new ethers.providers.JsonRpcProvider(url);
    const abi = abiSmartContract;
    const contractAddress = contractAddressSmartContract;
    console.log('connect transfer to user');


    let contract = new ethers.Contract(contractAddress, abi, customHttpProvider);

    contract.balanceOf('******************************************').then(balance =>{
    	console.log(balance/10e17);
    	if((balance/10e17) < 5000000){
    		alertAdmin();
    	}
    })
}
//##########################
//##########################
/// INQUIRY ZONE END


module.exports.createOrderBuy = createOrderBuy;
module.exports.inquiryBuy = inquiryBuy;
module.exports.inquiryBuyAdmin = inquiryBuyAdmin;
module.exports.checkBalance = checkBalance;