'use strict';
var admin = require("firebase-admin");
const dotenv = require('dotenv');
var request = require('request');
dotenv.config();
var serviceAccount = require("./"+process.env.FIREBASE);
const mysql      = require('mysql');
var moment = require('moment-timezone');

let sendNotify = function sendNotify(message, title, topic) {
	return new Promise((resolve, reject)=>{
		sentFCM(message, title, topic).then(data =>{
			resolve();
		}).catch(e =>{
			reject(e);
		});
	});

}

let sendNotifyTier1 = function sendNotifyTier1(message, title, topic, image, activity, url) {
	return new Promise((resolve, reject)=>{
		sentFCMTier1(message, title, topic, image, activity, url).then(data =>{
			resolve();
		}).catch(e =>{
			reject(e);
		});
	});
}


let sendIndividual = function sentIndividual(uid, token, messageSend, topicMessage) {
	return new Promise((resolve, reject) => {
        if(token != undefined){
            var message = {
            notification: {
                title: 'LikeWallet',
                body: messageSend
            },
            data: {
                status: 'true'
            },
            token: token

        };
        let unix = moment().unix()
        let messageAdd = {
        	detail:messageSend,
        	icon:'https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1585550557likeWallet__White.png',
        	title:topicMessage,
        	url:'',
            status:'not',
            timeStamp: unix.toString()

        }
        const db = admin.firestore();
    
        console.log(unix);
        db.collection('notificationByUser').doc(uid).collection('notify').doc(unix.toString()).set(messageAdd);
        // Send a message to the device corresponding to the provided
        // registration token.
        admin.messaging().send(message)
            .then((response) => {
                // Response is a message ID string.
                console.log('Successfully sent message:', response);
                resolve();
            })
            .catch((error) => {
                console.log('Error sending message:', error);
                reject();
            });
     }  	
	})

}

function sentFCM(messaging, title, topic) {
    // This registration token comes from the client FCM SDKs
 	return new Promise((resolve, reject)=>{
 		    console.log(topic);
 		    console.log(messaging);
        var message = {
            notification: {
                title: title,
                body: messaging
            },
            topic: topic
        };
        // Send a message to the device corresponding to the provided
        // registration token.
        admin.messaging().send(message)
            .then((response) => {
                // Response is a message ID string.
                console.log('Successfully sent message:', response);
                lineNotify(messaging, topic).then(data =>{
                	resolve();
                }).catch((error)=>{
                	reject(error);
                })
            })
            .catch((error) => {
            		reject(error);
                console.log('Error sending message:', error);
        });
 	});
}

function sentFCMTier1(messaging, title, topic, image, activity, url) {
    // This registration token comes from the client FCM SDKs
 	return new Promise((resolve, reject)=>{
 		    console.log(topic);
 		    console.log(messaging);
        var message = {
            notification: {
                title:title,
                body: messaging,
				image:image,
            },	
			data: {
				url :url,
				status: 'true',
				activity: activity,
				click_action: "FLUTTER_NOTIFICATION_CLICK"
			},
            topic: topic
        };
        // Send a message to the device corresponding to the provided
        // registration token.
        admin.messaging().send(message)
            .then((response) => {
                // Response is a message ID string.
                console.log('Successfully sent message:', response);
                lineNotify(messaging, topic).then(data =>{
                	resolve();
                }).catch((error)=>{
                	reject(error);
                })
            })
            .catch((error) => {
            		reject(error);
                console.log('Error sending message:', error);
        });
 	});
}

function lineNotify(messaging, topic) {
  return new Promise((resolve, reject) =>{
	  request({
	      method: 'POST',
	      uri: 'https://notify-api.line.me/api/notify',
	      headers: {
	          'Content-Type': 'application/x-www-form-urlencoded'
	      },
	      auth: {
	          'bearer': process.env.TOKEN_LINE_DEV
	      },
	      form: {
	          message: topic + "\n " + messaging + "\n ถูกส่งเรียบร้อย"
	      }
	  }, (err, httpResponse, body) => {
	      if (err) {
	          console.log(err);
	          reject(err);
	      } else {
	      	resolve();
	      }
	  });   
  });
  	
}



let depositBalance = function depositBalance() {
	return new Promise((resolve, reject)=>{
		getDeposit().then(data=>{
			resolve(data);
		}).catch(e=>{
			reject();
		})
	});
}

function getDeposit() {
	return new Promise((resolve, reject)=>{
	    var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	     
	    con.connect();
	    const sql = "SELECT SUM(amount)/10e17 as balance FROM locked_likepoint";
    	con.query(sql, function (err, result, fields) {
	      if (err) {
	        con.end();
	        console.log(err);
	        $log('SELECT: SUM(amount)/10e17'+ err);
	        reject();
	      }

	 		con.end();
				const callback = {
				 	result: result
				}
	 		resolve(callback);	

	    });	    	
	});
}

module.exports.sendNotify = sendNotify;
module.exports.sendNotifyTier1 = sendNotifyTier1;
module.exports.depositBalance = depositBalance;
module.exports.sendIndividual = sendIndividual;