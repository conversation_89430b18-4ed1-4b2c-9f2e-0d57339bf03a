
module.exports = function (
	 app,
	 admin,
	 serviceAccount,
	 dotenv,
	 cryptico,
	 generator,
	 moment,
	 elasticsearch,
	 request,
	 bip39,
	 btcLib,
	 bs58check,
	 twilio,
	 crypto,
	 algorithm,
	 password,
	 algorithmToFront,
	 $log,
	 bcrypt,
	 IV_LENGTH,
	 ethers,
	 accountSid,
	 authToken,
	 clientTwilio,
	 secrets,
	 uuidv4,
	 randomstring,
	 uuidAPIKey,
	 aws,
	 ed25519,
	 mysql,
	 kms,
	 decryptToFrontInternal,
	 encrypt,
	 randomText,
	 recoverySeed,
	 encryptToFront,
	 encryptWithPassword,
	 openpgp,
	 pubkey,
	 secondary,
	 selectMysql,
	 transfer,
	 RetrievePKED2519,
	 retrieveAddress,
	 encryptKMS,
	 decryptKMS,
	 bufferFromBufferString
	){


      let sleep = (time) => new Promise((resolve) => setTimeout(resolve, time))
// generateQR('******************************************', 'test', 100, 'LKE', '+***********');

// inQuireQR();
function inQuireQR(partnerTxnUid, partnerId, partnerSecret, requestDt, merchantId, terminalId, qrType, origPartnerTxnUid){
	return new Promise((resolve, reject) =>{
		var options = {
		  method: 'POST',
		  url: 'https://APIPORTAL.kasikornbank.com:12002/pos/inquire_payment/v2',
		  headers: {
		    'cache-control': 'no-cache',
		    'Content-Type': 'application/json',
		  },
		  body: {
		    partnerTxnUid: partnerTxnUid,
		    partnerId:partnerId,
		    partnerSecret: partnerSecret,
		    requestDt: requestDt,
		    merchantId: merchantId,
		    terminalId: terminalId,
		    qrType: qrType,
		    origPartnerTxnUid: origPartnerTxnUid
		  },
		  json: true
		};

		request(options,  (error, response, body) => {
		  if (error) throw new Error(error);
		  console.log(body);
		  if(body.txnStatus == 'PAID'){
		  	//update status paid
				updatePaid(partnerId, partnerTxnUid, body.txnStatus).then(data =>{
					getTXuid(partnerTxnUid).then(txuid =>{
						transferBalanceToBuyer(txuid[0].address, txuid[0].txnAmount).then(tx =>{
							let callBack = {
								body:body,
								tx:tx
							}
							resolve(callBack);
						});
					});
					
				})
		  }else{
	  			resolve(body);
		  }
	
		});
	});

};
app.post('/addAdminPK', function(req, res){
	const seed = bip39.generateMnemonic();
	const type = req.body.type;
	const owner = req.body.owner;
	const apiKey = req.body.apiKey;
	const secretKey = req.body.secretKey;
	console.log(seed);
	if(apiKey == process.env.apiKeyLIKE && secretKey == process.env.secretKeyLIKE){
	     encryptKMS(new Buffer(seed,'utf-8')).then(seedHex => {
	     	const base64Seed = Buffer.from(seedHex).toString('base64');
	     	RetrievePKED2519(seed).then(pk =>{
	     	const address = retrieveAddress(pk);
				let data = [
					base64Seed,
					address.toLowerCase(),
					type,
					owner
				];
				
				insertAdminPK(data).then(result => {
					//success
					res.json({statusCode:200, result:'success', data:data});
				});	
	     	});
	       //  decryptKMS(data).then(data2 => {
	       //    console.log(data2.toString('utf-8'));
	       //    res.json({result: data2.toString('utf-8')});
	       // });
	     });		

	}else{
		res.json({statusCode:200, result: 'Authenticate failed'});
	}


	

});
// transferBalanceToBuyer();
// transferBalanceToBuyer('******************************************', '95');
function transferBalanceToBuyer(buyer, amount){
	return new Promise((resolve, reject) =>{
		getAdminPK().then(result =>{
			console.log(result[0].pk);

		     	//step decode and retrieve seed
		     	const decodeSeed = Buffer.from(result[0].pk, 'base64');
		     	decryptKMS(decodeSeed).then(rSeed =>{
			     	RetrievePKED2519(rSeed.toString('utf-8')).then(pk =>{
	 			     	const address = retrieveAddress(pk).toLowerCase();
	 			     	console.log(address);
	 			     	transfer(buyer, pk, amount*100).then(tx =>{
	 			     		console.log(tx);
	 			     		resolve(tx);
	 			     	}).catch(e =>{
	 			     		reject();
	 			     	});
			     	});			     		
		     	});

		});
	});

}
// testSSL();
function testSSL(){
console.log('testSSL');
var fs = require('fs')
var path = require('path')
let keyfile = path.join(__dirname, 'kbanktest.key');
let certificateFile = path.join(__dirname, 'kbanktest.crt');

  const options = {
    url: 'https://openapi-test.kasikornbank.com/exercise/ssl',
    agentOptions: {
      cert: fs.readFileSync(certificateFile),
      key: fs.readFileSync(keyfile),
    },
    headers: { 'Content-Type': 'application/json' ,
    			'x-test-mode':true,
    			'Authorization':'Bearer huSyK5xmZv7314rCtA1QtokmPjgt'

},
    body: JSON.stringify({
      partnerId: 'PTR1051673',
      partnerSecret: 'd4bded59200547bc85903574a293831b',
    }),
  };
  request.post(options, function(error, response, body) {
    if (error) {
      console.warn(error);
    } else {
      console.log(body);
    }
  });
}


function callBack({url, body}){
	// https://openapi-sandbox.kasikornbank.com/v1/qrpayment/request
	return new Promise((resolve, reject) =>{
		var fs = require('fs')
		var path = require('path')
		let keyfile = path.join(__dirname, 'kbanktest.key');
		let certificateFile = path.join(__dirname, 'kbanktest.crt');

		  const options = {
		    url: url,
		    agentOptions: {
		      cert: fs.readFileSync(certificateFile),
		      key: fs.readFileSync(keyfile),
		    },
		    headers: { 'Content-Type': 'application/json' ,
		    			// 'x-test-mode':true,
		    			'Authorization':'Bearer '+ Buffer.from('8ZLgG5y9vn7yoMcx6tg0ENd9fIDf09tg:oFPS8cFmUKE0acM3').toString('base64')

		},
		    body: JSON.stringify(body),
		  };
		  request.post(options, function(error, response, body) {
		    if (error) {

		      console.warn(error);
		      reject(error);
		    } else {
		      console.log(body);
		      resolve(body);
		    }
		  });
	});

}

// testAuth();
function testAuth(){
	// https://openapi-sandbox.kasikornbank.com/oauth/token

	callBack({
		url: 'https://openapi-sandbox.kasikornbank.com/oauth/token',
		// body: body
	}).then(value =>{
		console.log(value);
	}).catch(e => {
		console.log(e);
	});
}
app.post('/createOrderBuy', function(req, res){
	console.log('createOrderBuy');
	const _token = req.body._token;
	const db = admin.firestore();
	const walletNumber = req.body.walletNumber;
	const address = req.body.address;
	const amount = req.body.amount;

   admin.auth().verifyIdToken(_token)
    .then(function (decodedToken){
    	//console.log(decodedToken);
            const userRef = db.collection('addressDNS').doc(walletNumber == '0' ? decodedToken.uid : decodedToken.uid+walletNumber)
            .get().then(snapshot => {
            	if(address == snapshot.data().address){
	            	generateQR(address, 'test', amount, 'LKE', decodedToken.phone_number).then(data =>{
	            		let result = {
	            				qrcode:data.body.qrCode.toString(),
	     						partnerTxnUid: data.body.partnerTxnUid.toString(),
	     						requestDt: data.requestDt.toString(),
	     						merchantId: data.merchantId.toString(),
	     						terminalId: data.terminalId.toString(),
	     						QRpartnerTxnUid: data.partnerTxnUid.toString(),
	 							qrType: data.qrType.toString()
	            		}
	            		let backData = [];
	            		backData.push(result);
	     				res.json({statusCode:200, result: backData});
	     			});
            	}	
            });
          
    
    });


});
app.post('/inquiryBuy', function(req, res){
	const _token = req.body._token;
	const db = admin.firestore();
	const partnerId = process.env.K_PARTNER_QR;
	const partnerSecret = process.env.K_SECRET_QR;
	const partnerTxnUid = req.body.partnerTxnUid;
	const requestDt = req.body.requestDt;
	const merchantId = req.body.merchantId;
	const terminalId = req.body.terminalId;	
	const qrType = req.body.qrType;	
	const origPartnerTxnUid = req.body.origPartnerTxnUid;	

   admin.auth().verifyIdToken(_token)
    .then(function (decodedToken){
		inQuireQR(partnerTxnUid, partnerId, partnerSecret, requestDt, merchantId, terminalId, qrType, origPartnerTxnUid).then(data =>{
			console.log(data);
			//test success case
			// data.txnStatus = 'PAID';
			res.json({statusCode:200, result: data.body, tx: data.tx});
		});
	});
});



//this function generate running number example fill 2 is will be change to 0002
function generateNumberZero(currentNumber){
	return new Promise((resolve, reject) => {
		let len = currentNumber.length;
		let code = currentNumber;
		console.log(len);
		for(let i=len;i<4;i++){
			code = '0'+code;
		}
		if(code.length == 4){
			resolve(code);
		}else{
			reject(false);
		}
	});
}



module.exports.transferBalanceToBuyer = transferBalanceToBuyer;
module.exports.inQuireQR = inQuireQR;
module.exports.generateQR = generateQR;
module.exports.createNewYear = createNewYear;
module.exports.getYearID = getYearID;
module.exports.getLastIndex = getLastIndex;
module.exports.getYearInvoice = getYearInvoice;
module.exports.generateTxID = generateTxID;
module.exports.insertTx = insertTx;
module.exports.updateTx = updateTx;
module.exports.updatePaid = updatePaid;
module.exports.generateNumberZero = generateNumberZero;

}



