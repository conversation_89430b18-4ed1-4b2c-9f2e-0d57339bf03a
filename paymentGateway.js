'strict'

const Utils = require('./utils');
const Private = require('./migrateUserToBCT');
const ethers = require('ethers');
const request = require('request');


const unlockWalletPrivateKey = function(uid) {
	return new Promise((resolve, reject) =>{
		Private.decryptMnemonicByUid(uid).then(seed=>{
			Utils.RetrievePKED2519(seed).then(pk =>{
				// const addressETH = Utils.retrieveAddress(pk);
				resolve(pk);
			})	
		});
	});
}

function retrieveAddress(pk){
    let wallet = new ethers.Wallet(pk);
    return wallet.address;
}
function notifyError(operations, tx){

        request({
            method: 'POST',
            uri: 'https://notify-api.line.me/api/notify',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            auth: {
                'bearer': 'iqQdc7zuV5XkMpZcZ7z4ISkT5L0x7PF2ahYTA4h4psz'
            },
            form: {
                message: "callback error operation id" + operations +"\n tx" + tx
            }
        }, (err, httpResponse, body) => {
            if (err) {
                console.log(err);
            } else {

            }
        });       
}
function callback(_id, operations, tx, phoneNumber) {
	return new Promise((resolve, reject) => {
			request.post({ 
				url: 'https://hcspayment.likewallet.io/callback', form: { 
				tx: tx,
				operations: operations,
				_id: _id,
				phoneNumber: phoneNumber
			}}, async function (err, httpResponse, body) {
				let json = JSON.parse(body);
				if(json.statusCode === 200) {
					resolve(true);
				}else{
					notifyError(operations, tx);
					resolve(true);
				}

			});  			
	})
};

const transfer = function(uid, address, amount, _id, operations, phoneNumber) {
	return new Promise((resolve, reject) => {
		console.log('get id ' + _id );
		console.log('get operations ' + operations );
				unlockWalletPrivateKey(uid).then(pk=>{
					const ownerAddr = retrieveAddress(pk);
						getBalance(ownerAddr).then(balance =>{
							if(balance >= amount) {
								Utils.transfer(address, pk, amount).then(tx => {
									console.log('tx' + tx);
									console.log('tx . hash' + tx.hash);
									callback(_id, operations, tx.hash, phoneNumber);
									resolve(true);
									// operations
								}).catch(e =>{
									resolve(true);
								})
							}else{
								resolve("insufficient balance")
							}
							}).catch(e =>{
								resolve(e);
						});
					

				});				

	})
}


const getBalance = function(address) {
	return new Promise((resolve, reject) =>{
	    var contractAddress = process.env.CONTRACT_LIKE;
	    var abi = JSON.parse(process.env.ABI_LIKE);
		var provider = new ethers.providers.JsonRpcProvider(process.env.NETWORK_RPC);
		let contract = new ethers.Contract(contractAddress, abi, provider);

		contract.balanceOf(address).then(balance => {
			resolve(balance/10e17);
		}).catch(e=>{
	       	reject(e);			
		});		
	});
}

module.exports = {
	unlockWalletPrivateKey,
	transfer
}