const bip39 = require('bip39');
const { ethers } = require('ethers');
const fs = require('fs');
const crypto = require('crypto');

// ฟังก์ชันสำหรับเข้ารหัส mnemonic
function encryptMnemonic(mnemonic, password) {
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync(password, 'salt', 32);
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(algorithm, key, iv);

    let encrypted = cipher.update(mnemonic, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return {
        encrypted: encrypted,
        iv: iv.toString('hex')
    };
}

// ฟังก์ชันสำหรับสร้าง wallet ใหม่
function createNewWallet(password = 'default-password') {
    console.log('=== Creating New Wallet ===');

    // สร้าง mnemonic ใหม่
    const mnemonic = bip39.generateMnemonic();
    console.log('Generated Mnemonic:', mnemonic);

    // สร้าง wallet จาก mnemonic
    const path = "m/44'/60'/0'/0/0"; // Ethereum derivation path
    const walletMnemonic = ethers.Wallet.fromMnemonic(mnemonic, path);

    console.log('Wallet Address:', walletMnemonic.address);
    console.log('Private Key:', walletMnemonic.privateKey);
    console.log('Public Key:', walletMnemonic.publicKey);

    // เข้ารหัส mnemonic
    const encryptedData = encryptMnemonic(mnemonic, password);

    // สร้างข้อมูล wallet
    const walletData = {
        address: walletMnemonic.address,
        publicKey: walletMnemonic.publicKey,
        encryptedMnemonic: encryptedData.encrypted,
        iv: encryptedData.iv,
        createdAt: new Date().toISOString(),
        derivationPath: path
    };

    // บันทึกลงไฟล์
    const filename = `wallet-${walletMnemonic.address.toLowerCase()}.json`;
    fs.writeFileSync(filename, JSON.stringify(walletData, null, 2));
    console.log('Wallet data saved to:', filename);

    return walletData;
}

// ทดสอบการสร้าง wallet
const newWallet = createNewWallet('my-secure-password');

// ทดสอบการเชื่อมต่อกับ provider (ถ้ามี)
if (process.env.NETWORK_RPC) {
    const provider = new ethers.providers.JsonRpcProvider(process.env.NETWORK_RPC);
    const wallet = ethers.Wallet.fromMnemonic(
        bip39.generateMnemonic(), // ใช้ mnemonic ใหม่สำหรับทดสอบ
        "m/44'/60'/0'/0/0"
    ).connect(provider);

    console.log('Connected to provider:', process.env.NETWORK_RPC);

    // ตรวจสอบ balance
    wallet.getBalance().then(balance => {
        console.log('Balance:', ethers.utils.formatEther(balance), 'ETH');
    }).catch(err => {
        console.log('Error getting balance:', err.message);
    });
}
