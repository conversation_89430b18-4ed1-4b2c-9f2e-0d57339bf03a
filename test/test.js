var assert = require('assert');
let chai = require('chai');
let chaiHttp = require('chai-http');
let server = require('../likewallet');
let should = chai.should();
chai.use(chaiHttp);
let expect = require('chai').expect;

  //###UNIT_TEST CASE
  // encrypt: encrypt,
  // decrypt: decrypt,
  // encryptToFront: encryptToFront,
  // encryptToFrontInternal: encryptToFrontInternal,
  // decryptToFrontInternal: decryptToFrontInternal,
  // generateRefCode: generateRefCode,
  // returnJSON: returnJSON,
  // encryptWithPassword: encryptWithPassword,
  // decryptWithPassword: decryptWithPassword,
  // randomText: randomText,
  // recoverySeed: recoverySeed,
  // checkOTP: checkOTP,
  // RetrievePK: RetrievePK,
  // RetrievePKED2519: RetrievePKED2519,
  // verifyUser: verifyUser,
  // verifyUserOTPFirebase: verifyUserOTPFirebase,
  // sendSMSTwilio: sendSMSTwilio,
  // sendSMSTwilioByCheck: sendSMSTwilioByCheck,
  // encryptKMS: encryptKMS,
  // decryptKMS: decryptKMS,
  // generateAPIKeyTest: generateAPIKeyTest,
  // createMerchant: createMerchant,
  // getMode: getMode,
  // getAPIkeyFromPublicKey: getAPIkeyFromPublicKey,
  // getPublickeyFromAPIKey: getPublickeyFromAPIKey,
  // getOwnerFromAPIKey: getOwnerFromAPIKey,
  // transfer: transfer,
  // transferToMerchant: transferToMerchant,
  // selectMysql: selectMysql,
  // insertMysql: insertMysql,
  // getDigit: getDigit
  //###UNIT_TEST CASE

  describe('encrypt data ', function() {
    describe('encrypt()', function() {
      it('should return 5fe046256799', function() {
        let en = server.encrypt("ManZer");
        en.should.have.property("content").eql("5fe046256799");
        en.should.have.property("tag").eql(server.bufferFromBufferString('<Buffer 37 89 9c 44 65 ca 01 45 03 bf 43 db b4 c7 5b d6>'));
      });
    });
  });  

  describe('decrypt data ', function() {
    describe('decrypt()', function() {
      it('should return 5fe046256799', function() {

        let text = {
          content: '5fe046256799',
          tag: server.bufferFromBufferString('<Buffer 37 89 9c 44 65 ca 01 45 03 bf 43 db b4 c7 5b d6>')
        }
        let de = server.decrypt(text);
        assert.equal(de, 'ManZer');
      });
    });
  });  

  describe('encrypt data to front end', function() {
    describe('EncryptToFront()', function() {
      it('should return KRlH67vQ9T0On3D+Sy6tbw==', function() {
        let en = server.encryptToFront('ManZer', 'mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm', '****************');
        assert.equal(en, "KRlH67vQ9T0On3D+Sy6tbw==");
      });
    });
  });

 describe('encrypt data', function() {
    describe('encryptToFrontInternal()', function() {
      it('should return FY89GPCC', function() {
        let en = server.encryptToFrontInternal('ManZer');
        assert.equal(en[0], "FY89GPCC");
      });
    });
  });

 describe('decrypt data to string', function() {
    describe('decryptToFrontInternal()', function() {
      it('should return ManZer', function() {
        let en = server.decryptToFrontInternal('FY89GPCC', server.bufferFromBufferString('<Buffer 38 e5 28 9e 86 c9 46 51 60 95 10 3a fd b5 d0 22>'));
        assert.equal(en, 'ManZer')
      });
    });
  });

 describe('generate referrer code', function() {
    describe('generateRefCode()', function() {
      it('should return string 10 character', function() {
        let en = server.generateRefCode();
        en.should.have.length(10);
      });
    });
  }); 

 describe('return json response', function() {
    describe('returnJSON()', function() {
      it('should return json format', function() {
        server.app.get('/', function (req, res){
          let en = server.returnJSON(200, res, 'data');
          expect(en).to.be.a.jsonFile();
        })
      });
    });
  });
 // console.log(encryptWithPassword('ManZer', 'man232msmd2mdmgksmcsdwmsd4m2m322'));
 describe('encrypt data with password', function() {
    describe('encryptWithPassword()', function() {
      it('should return f5afa0fafe73 / <Buffer a4 1c fc 5c e3 62 56 c9 f5 0c 5c f8 0b 10 14 51>', function() {
        let en = server.encryptWithPassword('ManZer', 'man232msmd2mdmgksmcsdwmsd4m2m322');
        en.should.have.property("content").eql("f5afa0fafe73");
        en.should.have.property("tag").eql(server.bufferFromBufferString('<Buffer a4 1c fc 5c e3 62 56 c9 f5 0c 5c f8 0b 10 14 51>'));
      });
    });
  });

 describe('decrypt data with password', function() {
    describe('decryptWithPassword()', function() {
      it('should return ManZer', function() {
        let text = {
          content: 'f5afa0fafe73',
          tag: server.bufferFromBufferString('<Buffer a4 1c fc 5c e3 62 56 c9 f5 0c 5c f8 0b 10 14 51>')
        }
        let en = server.decryptWithPassword(text, 'man232msmd2mdmgksmcsdwmsd4m2m322');
        assert.equal(en, 'ManZer');
      });
    });
  });


 describe('this function random text equal length', function() {
    describe('randomText()', function() {
      it('should return string as long equal num length', async function() {
        let en = await server.randomText(12);
        en.should.have.length(12);
      });
    });
  });

 describe('this recoverySeed from user secret !', function() {
    describe('recoverySeed()', function() {
      it('should return mnemonic seed 12 words', async function() {
        let userEncrypt = {
          content: 'b6d125dc32fca1e3930df0083f9bcc96997b36d84874297771b81cd60343cddc4add79bf2e52badef4999747c824793632d4d59ff1a331127de4d6ca9282fe65f343a38374bdb1e23d606a8d1fc0e11b6e305e3365896136d574d79a4ef99719045448ab7139e949d302b183ef3d94148ebf187f1b76f0904e84e4e2a81af91dca3cb8639a5fc08dd6124a2a30a6173ef865df48b2ea409a72070412cc3329b4e9fa1332601bcf18dc8ce302ef38ab5de29f31b4314c1edaedfd5903e320398b198eca42e84b4714eef94f2d655425ece005fad7bfd0d616ddb21623717b386b10721905c33f5dce889e0adcc3470cf9d5b6da6e3e78570ae0657b49962f9fa02d6647f9880570ee5f84e76ace39d19c0edd524b44b985f57ce79685ce7c86a14a4f3d76685c0c5788603e47fc4a768ee447980716de473b4a0bb58dd176f559d79f08455b189d4d8383bdd8820d51710866c5e4be49d4904859311d66a175b5ecc180e92ff2146b206154cee73c5b6a1f9da704c648e851c9356a63da122be39d682dec3fd9c47c625c395f4cfadd9a4fca99a1f9e761c2ff6eb30668c5b8f2fadf06835f1f1c16b0c7faad637ecaeeeb85d2cbfd6f762ad688b15933aeec12d5e7628b1e0db23784e96d7eab766e948178df6fb5ef5bb826cd23e0dee793ba68a7fa9173fd34f6b592271c8f7be1280b9265ce74ff044e795e1f5d4f230cddd0f3acb8b7336f69873d871f3e4b7a22c6f636a776c396a9bbe4f4f8a770cf5b7cbf84b4a3641295f2f7043934454ba0c116c9a99cc9ebddafbefc962cf4cd66ce3f63',
          tag: server.bufferFromBufferString('<Buffer 95 44 d7 0f 6a a7 88 07 e2 54 a4 cf fc d6 d5 29>')
        
        }
        let en = await server.recoverySeed(
          'WAqf9dVswkN9IcH15N3D6D6IUIB3',
          '1Grm2S4H3?575TDRjkvDSWmsSyuwzbEO',
          '801d237639c1937fa959f08b1750141c69f7d278ea1f82e75664421eb3e34554c6ae3646f5733002f66654a7a901eafc575d875dfc3cd80b462c6e936ea02616bc4545fbec8d89015320fa35355722daa26ba78d6abded1e6d0eaffc776fc2a71959d6798249c0a0b1ed6ab10e1332e817065da5bc14d30dac2545cdd8ecf2a672d56334865cb5c42c1c82c1e9a4a901812e14a5ee6840297b31270220a3d45fe607c5d5b29b1b3883effcbd3e4044a25c13f7fccef42e8792f70835dc18a23e1248d86fa29e8843fb9bc70b6df4c16f25d0f776a14471260d53d352e269ae1fe174ba5b71736c231b3c6e5a930f4d63aef315d5b6423ec605435b1db58f314e0d95fce4978ca4171acae18e92f75692becdde1ce7a08694303b3f00f833a744ac5',
          userEncrypt,
          server.bufferFromBufferString('<Buffer e6 66 0c 69 4a c0 06 e5 ad e8 8b 17 e7 a9 c5 89>'),
          'no_number'
          );
          assert.equal(en, 'crater hire admit salmon december dove trial pet waste item you round');

      });
    });
  });

 describe('this retrieve private key !', function() {
    describe('RetrievePK()', function() {
      it('should return private key', async function() {
        let en = await server.RetrievePK('crater hire admit salmon december dove trial pet waste item you round');
        assert.equal(en, '0xc50f47c4b25a47a5c3b82175b3da849f394c73a03de8d9d40cd7f560521ff5b6');
      });
    });
  });

 describe('this retrieve private ED2519 key !', function() {
    describe('RetrievePKED2519()', function() {
      it('should return private key ED2519', async function() {
        let en = await server.RetrievePKED2519('crater hire admit salmon december dove trial pet waste item you round');
        assert.equal(en, '0xfabeffd43ea1bbd8e7ae98a47ef1073ee80677fce555093e9b99d76b135de721');
      });
    });
  });

 describe('this verifyUser !', function() {
    describe('verifyUser()', function() {
      it('should return seed', async function() {
         this.timeout(4000);
        let docID = 'OTP66804230118';
        const generate = server.bip39.generateMnemonic();
        const seed = server.encrypt(generate);
        var pw = seed.content;
        var pwHex = server.secrets.str2hex(pw);  
   
        let db = server.admin.firestore();
        db.collection('middle')
        .doc(docID)
        .set({
          'verify':false,
          'expire':9580885818,
          'codeVerify':100100
        });
        let curPhone = '+66804230118';
        let phone_number = curPhone.split('+')[1];
        let data = {
          curPhone: curPhone,
          phone_number: phone_number,
          pass: '1',
          codeVerify: '100100',
          docID: docID,
          db: db,
          generate: generate,
          seed: seed,
          pw: pw,
          pwHex: pwHex,
          countPassword: 1,
          curPassword: 31,
          register: 'false',
          wallet_number: 'no_number',
          keyEncrypt: 'mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm',
          ivEncrypt: '****************',
          selfCode: 'rqgwNxt7Ah',
          refCode: 'no'
        } 
        let en = await server.verifyUser(data);
        en.should.have.property("statusCode").eql(200);
        en.should.have.property("data").property('username').length(33);
        en.should.have.property("data").property('password').length(30);
        en.should.have.property("seed").eql('hFrvX8M1AGteH2RL0Sbq4ZKzPsbANKx2og9xnvi1qzom7RiC9O80HDz5hHQKXfX59mHw+aEqp2qF15IdE6dTdtKHKGkgDF0Ho9MgNNSl2Xk=');

      });
    });
  });



 // describe('this get year invoice', function() {
 //    describe('getYearInvoice()', function() {
 //      it('should return current year', async function() {
 //        let en = await server.thaiqr.transferBalanceToBuyer();
 //        en.should.have.length(66);
 //      });
 //    });
 //  });




  // describe('/GET paymentOTP', () => {
  //     it('it should GET true result', (done) => {
  //       chai.request(server)
  //           .get('/paymentOTP')
  //           .end((err, res) => {
  //               expect(res.body.result).to.equals(true);
  //             done();
  //           });
  //     });
  // });




 // describe('/POST book', () => {
 //      it('it should not POST a book without pages field', (done) => {
 //          let book = {
 //              title: "The Lord of the Rings",
 //              author: "J.R.R. Tolkien",
 //              year: 1954
 //          }
 //        chai.request(server)
 //            .post('/book')
 //            .send(book)
 //            .end((err, res) => {
 //                  res.should.have.status(200);
 //                  res.body.should.be.a('object');
 //                  res.body.should.have.property('errors');
 //                  res.body.errors.should.have.property('pages');
 //                  res.body.errors.pages.should.have.property('kind').eql('required');
 //              done();
 //            });
 //      });

 //  });
