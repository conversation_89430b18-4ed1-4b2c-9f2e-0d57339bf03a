npm verb cli /Users/<USER>/.nvm/versions/node/v16.20.2/bin/node /Users/<USER>/.nvm/versions/node/v16.20.2/bin/npm
npm info using npm@8.19.4
npm info using node@v16.20.2
npm timing npm:load:whichnode Completed in 0ms
npm timing config:load:defaults Completed in 1ms
npm timing config:load:file:/Users/<USER>/.nvm/versions/node/v16.20.2/lib/node_modules/npm/npmrc Completed in 1ms
npm timing config:load:builtin Completed in 1ms
npm timing config:load:cli Completed in 2ms
npm timing config:load:env Completed in 0ms
npm timing config:load:file:/Users/<USER>/Documents/back-endNewLikewallet/.npmrc Completed in 0ms
npm timing config:load:project Completed in 3ms
npm timing config:load:file:/Users/<USER>/.npmrc Completed in 0ms
npm timing config:load:user Completed in 0ms
npm timing config:load:file:/Users/<USER>/.nvm/versions/node/v16.20.2/etc/npmrc Completed in 0ms
npm timing config:load:global Completed in 0ms
npm timing config:load:validate Completed in 1ms
npm timing config:load:credentials Completed in 1ms
npm timing config:load:setEnvs Completed in 0ms
npm timing config:load Completed in 10ms
npm timing npm:load:configload Completed in 10ms
npm timing npm:load:mkdirpcache Completed in 1ms
npm timing npm:load:mkdirplogs Completed in 0ms
npm verb title npm install
npm verb argv "install" "--loglevel" "verbose"
npm timing npm:load:setTitle Completed in 23ms
npm timing config:load:flatten Completed in 5ms
npm timing npm:load:display Completed in 8ms
npm verb logfile logs-max:10 dir:/Users/<USER>/.npm/_logs
npm verb logfile /Users/<USER>/.npm/_logs/2025-07-15T03_40_11_169Z-debug-0.log
npm timing npm:load:logFile Completed in 5ms
npm timing npm:load:timers Completed in 1ms
npm timing npm:load:configScope Completed in 0ms
npm timing npm:load Completed in 49ms
npm timing arborist:ctor Completed in 1ms
npm timing arborist:ctor Completed in 0ms
npm timing idealTree:init Completed in 21ms
npm timing idealTree:userRequests Completed in 0ms
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fexperimental 20ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@vonage%2fserver-sdk 8ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/atob 7ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/await 6ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/aws-sdk 15ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bcrypt 7ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bip39 6ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bitcoinjs-lib 6ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bitcore-mnemonic 12ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bl 7ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/body-parser 5ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bs58check 5ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/chai 4ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/chai-http 6ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/cors 7ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/create-hash 7ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/cryptico 6ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/crypto 8ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/crypto-js 8ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/date-and-time 7ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/dotenv 5ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ed25519 8ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ed25519-hd-key 4ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/elasticsearch 5ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/errorhandler 4ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ethereum-encryption 4ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ethers 7ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/express 5ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/express-rate-limit 5ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/express-session 17ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/firebase-admin 7ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/generate-password 6ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ipfs-api 5ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ipfs-cluster-api-remove-name 6ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ipfs-http-client 7ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/jsonwebtoken 5ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/kue 6ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/log 4ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/moment 7ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/moment-timezone 7ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/mosca 6ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/mqtt 7ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/mysql 5ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/node-2fa 7ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/nodemailer 7ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/openpgp 9ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/promise 8ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/randomstring 4ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/request 5ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/secret-sharing.js 5ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/serverless 23ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/serverless-http 5ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/stream-chat 10ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/twilio 6ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/uuid 6ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/uuid-apikey 9ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/web3 9ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/mocha 8ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/rewire 8ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/supertest 7ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/url 924ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/node-addon-api 918ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fweb 932ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/util 926ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/jmespath 922ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/sax 930ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/scrypt-js 937ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bech32 973ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@mapbox%2fnode-pre-gyp 979ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bitcoin-ops 975ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/buffer 995ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bip66 997ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@noble%2fhashes 1002ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/xml2js 1009ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bip174 1002ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/querystring 1011ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/merkle-lib 1003ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/query-string 1024ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bip32 1021ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/buffer 1013ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/typeforce 1022ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/inherits 1018ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/events 1037ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/tiny-secp256k1 1025ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/create-hmac 1028ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/pushdata-bitcoin 1027ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/readable-stream 1024ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/unorm 1029ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/qs 1026ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/debug 1025ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/randombytes 1035ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/raw-body 1024ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/varuint-bitcoin 1037ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bytes 1031ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/iconv-lite 1028ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/unpipe 1032ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bitcore-lib 1039ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/depd 1037ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/type-is 1034ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/on-finished 1029ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/wif 1053ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/content-type 1029ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/http-errors 1035ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/loupe 1027ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bs58 1030ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/assertion-error 1026ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/check-error 1027ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/get-func-name 1028ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/pathval 1029ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/deep-eql 1030ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/methods 1026ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/inherits 1023ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/qs 1029ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/safe-buffer 1036ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/is-ip 1032ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/type-detect 1034ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/destroy 1048ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ripemd160 1026ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/vary 1031ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@types%2fchai 1032ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/cipher-base 1031ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@types%2fsuperagent 1034ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/sha.js 1031ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ieee754 1075ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/superagent 1038ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/agentkeepalive 1028ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/md5.js 1039ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/cookiejar 1045ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/chalk 1032ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/object-assign 1045ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bitcore-lib 1032ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bindings 1043ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/accepts 1036ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/escape-html 1036ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/randombytes 1033ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/nan 1046ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/charset 1054ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/babel-runtime 1037ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/tweetnacl 1047ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/secp256k1 1036ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fweb 1036ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/create-hmac 1050ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/aes 1043ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ethereumjs-util 1042ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/js-sha3 1044ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fbasex 1040ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/lodash 1049ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fbytes 1042ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2frlp 1045ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fabi 1047ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fsha2 1050ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fhash 1051ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fwallet 1048ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fnetworks 1047ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fpbkdf2 1051ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2flogger 1052ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2frandom 1054ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fbase64 1056ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2funits 1058ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fstrings 1054ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2faddress 1055ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fcontracts 1053ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fconstants 1056ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fabstract-signer 1048ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fhdnode 1063ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fbignumber 1058ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fproviders 1058ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/vary 1050ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2ftransactions 1053ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fsolidity 1068ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fkeccak256 1067ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/etag 1058ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/send 1058ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fjson-wallets 1062ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fsigning-key 1068ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/safe-buffer 1056ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/accepts 1061ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/cookie 1063ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/methods 1062ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fproperties 1073ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/statuses 1062ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/content-type 1059ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/utils-merge 1061ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fwordlists 1076ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/encodeurl 1063ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/setprototypeof 1060ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/array-flatten 1060ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/parseurl 1066ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/finalhandler 1063ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/content-disposition 1060ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/range-parser 1064ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/proxy-addr 1068ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/serve-static 1066ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/path-to-regexp 1066ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/on-headers 1062ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@ethersproject%2fabstract-provider 1077ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/cookie 1064ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/merge-descriptors 1067ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/cookie-signature 1058ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/depd 1067ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/cookie-signature 1069ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/dicer 1058ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/uid-safe 1067ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/fresh 1080ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/debug 1053ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/jwks-rsa 1061ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/concat-stream 1057ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/glob 1054ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@firebase%2fdatabase-compat 1068ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/node-forge 1094ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/flatmap 1088ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/detect-node 1089ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@types%2fnode 1102ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/cids 1107ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@firebase%2fdatabase-types 1122ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/end-of-stream 1163ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/async 1168ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@google-cloud%2fstorage 1173ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ipfs-unixfs 1173ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/is-pull-stream 1173ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/lodash 1172ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/@google-cloud%2ffirestore 1186ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ipld-dag-pb 1181ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/is-stream 1180ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/once 1177ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/big.js 1191ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ipfs-block 1185ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ipld-dag-cbor 1185ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/multiaddr 1182ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/libp2p-crypto 1185ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/is-ipfs 1189ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bs58 1198ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/multihashes 1187ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/readable-stream 1176ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ndjson 1189ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/peer-id 1189ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/peer-info 1189ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/through2 1179ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/pull-pushable 1185ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/lru-cache 1197ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/pull-stream-to-stream 1188ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/async 1184ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/pull-defer 1193ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/streamifier 1189ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/buffer 1182ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/pump 1195ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/tar-stream 1193ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/stream-to-pull-stream 1195ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/qs 1197ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/concat-stream 1194ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/pull-to-stream 1192ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/cids 1190ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/multiaddr 1195ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/qs 1196ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ipfs-block 1193ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ipld-dag-cbor 1193ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/stream-http 1206ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ipld-dag-pb 1195ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/multibase 1221ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/abort-controller 1202ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/bignumber.js 1203ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/it-tar 1197ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ipld-raw 1199ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/iso-stream-http 1207ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/iterable-ndjson 1198ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ky-universal 1194ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/it-to-stream 1200ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ky 1199ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ipfs-utils 1205ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/multiaddr 1197ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/lodash.once 1193ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/merge-options 1201ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/form-data 1212ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/jws 1197ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/multiaddr-to-uri 1203ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/multicodec 1203ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/lodash.isnumber 1199ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/lodash.includes 1199ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/multibase 1205ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/promisify-es6 1232ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/lodash.isboolean 1200ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/lodash.isplainobject 1198ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/lodash 1196ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/stylus 1197ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/nib 1202ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/ms 1209ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/yargs 1202ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/stream-to-it 1216ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/parse-duration 1217ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/lodash.isstring 1213ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/node-redis-warlock 1207ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/lodash.isinteger 1212ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/uni-global 1204ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/event-emitter 1204ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/reds 1208ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/d 1208ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/redis 1212ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/debug 1203ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/type 1213ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/pug 1218ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/duration 1213ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/es5-ext 1215ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/glob 1214ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/sprintf-kit 1217ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/diff 1217ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/clone 1207ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/brfs 1210ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/commander 1212ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/jade 1223ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/mkdirp 1219ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/commander 1211ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/st 1221ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/steed 1219ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/memdown 1217ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/qlobber 1219ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/levelup 1222ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/pino 1227ms (cache hit)
