
module.exports = function (
	 app,
	 admin,
	 serviceAccount,
	 dotenv,
	 cryptico,
	 generator,
	 moment,
	 elasticsearch,
	 request,
	 bip39,
	 btcLib,
	 bs58check,
	 twilio,
	 crypto,
	 algorithm,
	 password,
	 algorithmToFront,
	 $log,
	 bcrypt,
	 IV_LENGTH,
	 ethers,
	 accountSid,
	 authToken,
	 clientTwilio,
	 secrets,
	 uuidv4,
	 randomstring,
	 uuidAPIKey,
	 aws,
	 ed25519,
	 mysql,
	 kms,
	 decryptToFrontInternal,
	 encrypt,
	 randomText,
	 recoverySeed,
	 encryptToFront,
	 encryptWithPassword,
	 openpgp,
	 pubkey,
	 secondary,
	 selectMysql,
	 transfer,
	 RetrievePKED2519,
	 retrieveAddress,
	 encryptKMS,
	 decryptKMS,
	 bufferFromBufferString
	){


      let sleep = (time) => new Promise((resolve) => setTimeout(resolve, time))
// generateQR('******************************************', 'test', 100, 'LKE', '+***********');

// inQuireQR();
function inQuireQR(partnerTxnUid, partnerId, partnerSecret, requestDt, merchantId, terminalId, qrType, origPartnerTxnUid){
	return new Promise((resolve, reject) =>{
		var options = {
		  method: 'POST',
		  url: 'https://APIPORTAL.kasikornbank.com:12002/pos/inquire_payment/v2',
		  headers: {
		    'cache-control': 'no-cache',
		    'Content-Type': 'application/json',
		  },
		  body: {
		    partnerTxnUid: partnerTxnUid,
		    partnerId:partnerId,
		    partnerSecret: partnerSecret,
		    requestDt: requestDt,
		    merchantId: merchantId,
		    terminalId: terminalId,
		    qrType: qrType,
		    origPartnerTxnUid: origPartnerTxnUid
		  },
		  json: true
		};

		request(options,  (error, response, body) => {
		  if (error) throw new Error(error);
		  console.log(body);
		  if(body.txnStatus == 'PAID'){
		  	//update status paid
				updatePaid(partnerId, partnerTxnUid, body.txnStatus).then(data =>{
					getTXuid(partnerTxnUid).then(txuid =>{
						transferBalanceToBuyer(txuid[0].address, txuid[0].txnAmount).then(tx =>{
							let callBack = {
								body:body,
								tx:tx
							}
							resolve(callBack);
						});
					});
					
				})
		  }else{
	  			resolve(body);
		  }
	
		});
	});

};
app.post('/addAdminPK', function(req, res){
	const seed = bip39.generateMnemonic();
	const type = req.body.type;
	const owner = req.body.owner;
	const apiKey = req.body.apiKey;
	const secretKey = req.body.secretKey;
	console.log(seed);
	if(apiKey == process.env.apiKeyLIKE && secretKey == process.env.secretKeyLIKE){
	     encryptKMS(new Buffer(seed,'utf-8')).then(seedHex => {
	     	const base64Seed = Buffer.from(seedHex).toString('base64');
	     	RetrievePKED2519(seed).then(pk =>{
	     	const address = retrieveAddress(pk);
				let data = [
					base64Seed,
					address.toLowerCase(),
					type,
					owner
				];
				
				insertAdminPK(data).then(result => {
					//success
					res.json({statusCode:200, result:'success', data:data});
				});	
	     	});
	       //  decryptKMS(data).then(data2 => {
	       //    console.log(data2.toString('utf-8'));
	       //    res.json({result: data2.toString('utf-8')});
	       // });
	     });		

	}else{
		res.json({statusCode:200, result: 'Authenticate failed'});
	}


	

});
// transferBalanceToBuyer();
// transferBalanceToBuyer('******************************************', '95');
function transferBalanceToBuyer(buyer, amount){
	return new Promise((resolve, reject) =>{
		getAdminPK().then(result =>{
			console.log(result[0].pk);

		     	//step decode and retrieve seed
		     	const decodeSeed = Buffer.from(result[0].pk, 'base64');
		     	decryptKMS(decodeSeed).then(rSeed =>{
			     	RetrievePKED2519(rSeed.toString('utf-8')).then(pk =>{
	 			     	const address = retrieveAddress(pk).toLowerCase();
	 			     	console.log(address);
	 			     	transfer(buyer, pk, amount*100).then(tx =>{
	 			     		console.log(tx);
	 			     		resolve(tx);
	 			     	}).catch(e =>{
	 			     		reject();
	 			     	});
			     	});			     		
		     	});

		});
	});

}
// testSSL();
function testSSL(){
console.log('testSSL');
var fs = require('fs')
var path = require('path')
let keyfile = path.join(__dirname, 'kbanktest.key');
let certificateFile = path.join(__dirname, 'kbanktest.crt');

  const options = {
    url: 'https://openapi-test.kasikornbank.com/exercise/ssl',
    agentOptions: {
      cert: fs.readFileSync(certificateFile),
      key: fs.readFileSync(keyfile),
    },
    headers: { 'Content-Type': 'application/json' ,
    			'x-test-mode':true,
    			'Authorization':'Bearer qPV8XnIFwOEg9Ov9CAAOSmNxDkGc'

},
    body: JSON.stringify({
      partnerId: 'PTR1727698',
      partnerSecret: '9b681427a5de45039264c62728bef510',
    }),
  };
  request.post(options, function(error, response, body) {
    if (error) {
      console.warn(error);
    } else {
      console.log(body);
    }
  });
}


function callBack({url, body}){
	// https://openapi-sandbox.kasikornbank.com/v1/qrpayment/request
	return new Promise((resolve, reject) =>{
		var fs = require('fs')
		var path = require('path')
		let keyfile = path.join(__dirname, 'kbanktest.key');
		let certificateFile = path.join(__dirname, 'kbanktest.crt');

		  const options = {
		    url: url,
		    agentOptions: {
		      cert: fs.readFileSync(certificateFile),
		      key: fs.readFileSync(keyfile),
		    },
		    headers: { 'Content-Type': 'application/json' ,
		    			// 'x-test-mode':true,
		    			'Authorization':'Bearer '+ Buffer.from('8ZLgG5y9vn7yoMcx6tg0ENd9fIDf09tg:oFPS8cFmUKE0acM3').toString('base64')

		},
		    body: JSON.stringify(body),
		  };
		  request.post(options, function(error, response, body) {
		    if (error) {

		      console.warn(error);
		      reject(error);
		    } else {
		      console.log(body);
		      resolve(body);
		    }
		  });
	});

}

// testAuth();
function testAuth(){
	// https://openapi-sandbox.kasikornbank.com/oauth/token

	callBack({
		url: 'https://openapi-sandbox.kasikornbank.com/oauth/token',
		// body: body
	}).then(value =>{
		console.log(value);
	}).catch(e => {
		console.log(e);
	});
}
app.post('/createOrderBuy', function(req, res){
	console.log('createOrderBuy);
	const _token = req.body._token;
	const db = admin.firestore();
	const walletNumber = req.body.walletNumber;
	const address = req.body.address;
	const amount = req.body.amount;

   admin.auth().verifyIdToken(_token)
    .then(function (decodedToken){
    	//console.log(decodedToken);
            const userRef = db.collection('addressDNS').doc(walletNumber == '0' ? decodedToken.uid : decodedToken.uid+walletNumber)
            .get().then(snapshot => {
            	if(address == snapshot.data().address){
	            	generateQR(address, 'test', amount, 'LKE', decodedToken.phone_number).then(data =>{
	            		let result = {
	            				qrcode:data.body.qrCode.toString(),
	     						partnerTxnUid: data.body.partnerTxnUid.toString(),
	     						requestDt: data.requestDt.toString(),
	     						merchantId: data.merchantId.toString(),
	     						terminalId: data.terminalId.toString(),
	     						QRpartnerTxnUid: data.partnerTxnUid.toString(),
	 							qrType: data.qrType.toString()
	            		}
	            		let backData = [];
	            		backData.push(result);
	     				res.json({statusCode:200, result: backData});
	     			});
            	}	
            });
          
    
    });


});
app.post('/inquiryBuy', function(req, res){
	const _token = req.body._token;
	const db = admin.firestore();
	const partnerId = process.env.K_PARTNER_QR;
	const partnerSecret = process.env.K_SECRET_QR;
	const partnerTxnUid = req.body.partnerTxnUid;
	const requestDt = req.body.requestDt;
	const merchantId = req.body.merchantId;
	const terminalId = req.body.terminalId;	
	const qrType = req.body.qrType;	
	const origPartnerTxnUid = req.body.origPartnerTxnUid;	

   admin.auth().verifyIdToken(_token)
    .then(function (decodedToken){
		inQuireQR(partnerTxnUid, partnerId, partnerSecret, requestDt, merchantId, terminalId, qrType, origPartnerTxnUid).then(data =>{
			console.log(data);
			//test success case
			// data.txnStatus = 'PAID';
			res.json({statusCode:200, result: data.body, tx: data.tx});
		});
	});
});

app.post('/callbackFromBank', function(req, res){

	const statusCode = req.body.statusCode;
	const messageTH = req.body.messageTH;
	const messageEN = req.body.messageEN;
	const accountId = req.body.accountId;
	const accountName = req.body.accountName;
	const partnerId = req.body.partnerId;
	const requestDate = req.body.requestDate;
	const txnId = req.body.txnId;
	const merchantId = req.body.merchantId;
	const terminalId = req.body.terminalId;
	const amt = req.body.amt;
	const currencyCode = req.body.currencyCode;
	const ref1 = req.body.ref1;
	const ref2 = req.body.ref2;
	const ref3 = req.body.ref3;
	const ref4 = req.body.ref4;
	const metadata = req.body.metadata;
	const partnerTxnUid = req.body.paymentCallBack.partnerTxnUid;
	const statusCode = req.body.paymentCallBack.statusCode;
	const errorCode = req.body.paymentCallBack.errorCode;
	const txnAmount = req.body.paymentCallBack.txnAmount;
	const txnCurrencyCode = req.body.paymentCallBack.txnCurrencyCode;
	const loyaltyId = req.body.paymentCallBack.loyaltyId;
	const additionalInfo = req.body.paymentCallBack.additionalInfo;

	//update status paid when webhook connect


});
//this function generate invoice and QR for promptpay  QR API
function generateQR(address, message, amount, name ,phone_number){
	return new Promise((resolve, reject) =>{

		const created_time = moment.tz(moment().toISOString(), "Asia/Bangkok").format();
		merchantId = 'KB739783318670';
		terminalId = '********';
		qrType = '3';


		generateTxID(address, name, phone_number).then(invoice=>{
		var options = { method: 'POST',
		  url: 'https://APIPORTAL.kasikornbank.com:12002/pos/qr_request',
		  headers: {
		    'cache-control': 'no-cache',
		    'Content-Type': 'application/json',
		  },
		  body: {
		    partnerTxnUid: invoice.toString(),
		    partnerId: process.env.K_PARTNER_QR,
		    partnerSecret: process.env.K_SECRET_QR,
		    requestDt: created_time,
		    merchantId: merchantId,
		    terminalId: terminalId,
		    qrType: qrType,
		    txnAmount: amount,
		    txnCurrencyCode: 'THB',
		    reference1: invoice.toString(),
		    reference2: address,
		    reference3: 'BUYLIKE',
		    reference4: null,
		    metadata: message   
		  },
		  json: true
		};
		insertTx(address, name, phone_number, invoice.toString(), created_time, merchantId, terminalId, qrType, amount, 'THB', message, process.env.K_PARTNER_QR, 'BUYLIKE').then(data =>{
			request(options, (error, response, body) => {
			  if (error) throw new Error(error);
			  // res.json({sttusCode:200, result: response});
			  console.log(body);
			  updateTx(body, invoice.toString());
			  let newBody = {
			  	body: body,
			  	requestDt: created_time,
			  	merchantId: merchantId,
			  	terminalId: terminalId,
			  	qrType: qrType,
			  	partnerTxnUid: invoice.toString()
			  }
			  resolve(newBody);
			});	
		});
	
	});
	});
}

//this function create new year if next year
function createNewYear(_year){
	return new Promise((resolve, reject) =>{
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	     
	    con.connect();
	   	const sql = "INSERT INTO invoice_year SET year = ?";

	     con.query(sql,[_year], function (err, result, fields) {
	    
	      if (err) {
	        console.log(err);
	        $log('createNewYear : '+ err);
	        con.end();
	        sleep(4000).then(() => {
	      		return resolve(createNewYear());
	      	});
	      }
	      resolve(result.insertId);
	  });
	});
}
//this function get id of year
function getYearID(_year) {
	return new Promise((resolve, reject) => {
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	     
	    con.connect();
    	let _year = moment().format('YYYY');
		
	    const sql = "SELECT * FROM invoice_year WHERE year = ? ORDER BY running DESC LIMIT 1";

	     con.query(sql,[_year], function (err, result, fields) {
	    
	      if (err) {
	        console.log(err);
	        $log('getYearID : '+ err);
	        con.end();
	        sleep(4000).then(() => {
	      		return resolve(getYearID());
	      	});
	      }
	      if(result.length == 0){
	      	con.end();
			createNewYear(_year).then(running_year =>{
				resolve(running_year); 
			});
	      }else{
		    con.end();
			resolve(result[0]['running']);      	
	      }

		});	
	});
}
// generateTxID('******************************************').then(data=>{console.log(data);});
//this function get last number index 
function getLastIndex(address) {
	return new Promise((resolve, reject) => {
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	     
	    con.connect();
    	let _year = moment().format('YYYY');
		getYearID().then(yearID => {
			// console.log(yearID);
			// console.log(address);
		    const sql = "SELECT running FROM invoice_buy_promptpay WHERE address = ? and year_id = ?";

		     con.query(sql,[address, yearID], function (err, result, fields) {
		    
		      if (err) {
		        console.log(err);
		        $log('getLastIndex : '+ err);
		        con.end();
		         sleep(4000).then(() => {
		      		return resolve(getLastIndex());
		      	});
		      }
		      con.end();
		      // console.log(result.length);
				if(result.length == 0){
					resolve(1);
				}else{
					console.log('len: '+result.length);
					resolve(result.length+1);
				}

			  
			});
		});

	});
};

//this function get year of invoice
function getYearInvoice() {
	return new Promise((resolve, reject) =>{
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	     
	    con.connect();

	    const sql = "SELECT * FROM invoice_year WHERE status = 1";

	     con.query(sql, function (err, result, fields) {
	    
	      if (err) {
	        console.log(err);
	        $log('getYearInvoice : '+ err);
	        con.end();
	        sleep(4000).then(() => {
	      		return resolve(getYearInvoice());
	      	});
	      }
			con.end();
		  resolve(result[0]['year']);
		});
	});
		
}

//this function generate invoice
function generateTxID(address, name, phone_number){
	return new Promise((resolve, reject)=>{
		console.log(address);
		let firstAddress = address.substring(2,5).toUpperCase();
		let lastAddress = address.substring(address.length - 3).toUpperCase();
		console.log(firstAddress);
		// let partnerTxnUid = 'LKE2020CE140001';
		let _currentYear = moment().format('YYYY');
		_currentYear = _currentYear.substring(_currentYear.length - 2);
		let partnerTxnUid = 'LKE';
			getLastIndex(address).then(lastIndex =>{
				generateNumberZero(lastIndex.toString()).then(_index => {
					partnerTxnUid = partnerTxnUid+_currentYear+firstAddress+lastAddress+_index;
					console.log(partnerTxnUid);
					try{
						resolve(partnerTxnUid);
					}catch(e){
						console.log(e);
					}
					
				});
			});

		
	});
}
function getTXuid(parnerTxnUid){
	return new Promise((resolve, reject) =>{
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	   
	    con.connect();	
	   	const sql = "SELECT address, txnAmount FROM invoice_buy_promptpay WHERE partnerTxnUid = ?";
	    con.query(sql, [[parnerTxnUid]], function (err, result, fields) {
		      if (err) {
		        console.log(err);
		        $log('getTXuid : '+ err);
		        con.end();
		        reject();
		      }
		      con.end();
			  
			  resolve(result);
			}); 		
	});
};
function getAdminPK(){
	return new Promise((resolve, reject) =>{
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	   
	    con.connect();	
	   	const sql = "SELECT pk, address FROM pk_admin_gateway WHERE owner = '21CT' and type='test'";
	    con.query(sql, function (err, result, fields) {
		      if (err) {
		        console.log(err);
		        $log('getAdminPK : '+ err);
		        con.end();
		        reject();
		      }
		      con.end();
			  
			  resolve(result);
			}); 		
	});
};
function insertAdminPK(data){
	return new Promise((resolve, reject) =>{
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	   
	    con.connect();	
	    const sql = "INSERT INTO pk_admin_gateway (pk, address, type, owner) VALUES ?";
	    con.query(sql,[[data]], function (err, result, fields) {
		      if (err) {
		        console.log(err);
		        $log('insertAdminPK : '+ err);
		        con.end();
		        reject();
		      }
		      con.end();
			  
			  resolve(true);
			});
	});
}
function insertTx(address, name, phone_number, partnerTxnUid, created_time, merchantId, terminalId, qrType, amount, currency, message, partnerId, ref3){
	return new Promise((resolve, reject) => {
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	   
	    con.connect();
    	let _year = moment().format('YYYY');
		getYearID().then(yearID => {
		    const sql = "INSERT INTO invoice_buy_promptpay (name, phone_number, address, partnerTxnUid, year_id, status, requestDt, merchantId, terminalId, qrType, txnAmount, txnCurrencyCode, reference1, reference2, metadata, partnerId, reference3) VALUES ?";
		     con.query(sql,[[[name, phone_number, address, partnerTxnUid, yearID.toString(), 1, created_time, merchantId, terminalId, qrType, amount, currency, partnerTxnUid, address, message, partnerId, ref3]]], function (err, result, fields) {
		      if (err) {
		        console.log(err);
		        $log('getLastIndex : '+ err);
		        con.end();
		        reject();
		      	// return resolve(insertTx());
		      }
		      con.end();
			  
			  resolve(true);
			});
		});
	});
}


function updateTx(body, partnerTxnUid) {
	return new Promise((resolve, reject) => {
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	   
	    con.connect();
			const sql = "UPDATE  invoice_buy_promptpay SET partnerTxnUid = ?, statusCode = ?, errorCode = ?, errorDesc = ?, accountName = ?, qrCode = ? WHERE partnerTxnUid = ? AND partnerId = ?";
		     con.query(sql,[body.partnerTxnUid, body.statusCode, body.errorCode, body.errorDesc, body.accountName, body.qrCode, partnerTxnUid, body.partnerId], function (err, result, fields) {
		      if (err) {
		        console.log(err);
		        $log('updateTx : '+ err);
		        con.end();
		        reject();
		      	// return resolve(insertTx());
		      }
		      con.end();
			  
			  resolve(true);
			});
	});
}
function updatePaid(partnerId, partnerTxnUid, txnStatus) {
	return new Promise((resolve, reject) => {
		var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	   
	    con.connect();
			const sql = "UPDATE  invoice_buy_promptpay SET txnStatus = ?, status= ? WHERE partnerTxnUid = ? AND partnerId = ?";
		     con.query(sql,[txnStatus, 2, partnerTxnUid, partnerId], function (err, result, fields) {
		      if (err) {
		        console.log(err);
		        $log('updateTx : '+ err);
		        con.end();
		        reject();
		      	// return resolve(insertTx());
		      }
		      con.end();
			  
			  resolve(true);
			});
	});
}
//this function generate running number example fill 2 is will be change to 0002
function generateNumberZero(currentNumber){
	return new Promise((resolve, reject) => {
		let len = currentNumber.length;
		let code = currentNumber;
		console.log(len);
		for(let i=len;i<4;i++){
			code = '0'+code;
		}
		if(code.length == 4){
			resolve(code);
		}else{
			reject(false);
		}
	});
}



module.exports.transferBalanceToBuyer = transferBalanceToBuyer;
module.exports.inQuireQR = inQuireQR;
module.exports.generateQR = generateQR;
module.exports.createNewYear = createNewYear;
module.exports.getYearID = getYearID;
module.exports.getLastIndex = getLastIndex;
module.exports.getYearInvoice = getYearInvoice;
module.exports.generateTxID = generateTxID;
module.exports.insertTx = insertTx;
module.exports.updateTx = updateTx;
module.exports.updatePaid = updatePaid;
module.exports.generateNumberZero = generateNumberZero;

}



