'use strict';


var admin = require("firebase-admin");
const mysql      = require('mysql');
var serviceAccountOld = require("./oldlikepoint.json");

const dotenv = require('dotenv');
dotenv.config();
var Promise = require('promise');
var moment = require('moment-timezone');
var elasticsearch = require('elasticsearch');
var await = require('await');
var request = require('request');
var serviceAccount = require("./"+process.env.FIREBASE);
const $log = require('log');


module.exports.getHistoryTransaction = function({phoneNumber, limit = 10}) {
	return new Promise((resolve, reject) => {
	 const db = admin.firestore();
	 const userRef = db.collection('addressDNS').where('phoneNumber', '==', phoneNumber);
	 userRef.get().then(snapshot => {
	    snapshot.forEach(doc => {
	                // console.log(doc.id, '=>', doc.data());
	    var con = mysql.createConnection({
	      host     : process.env.RDS_HOST,
	      user     : process.env.RDS_USERNAME,
	      password : process.env.RDS_PASSWORD,
	      database : process.env.RDS_DATABASE
	    });
	     
	    con.connect();
	    const sql = "SELECT amount, tx, createTime, chain FROM logTransaction WHERE toAddress = ? OR fromAddress = ? ORDER BY running DESC LIMIT ?";
	    con.query(sql, [doc.data().address, doc.data().address, limit], function (err, result, fields) {
	      if (err) {
	        con.end();
	        console.log(err);
	        $log('getHistoryTransaction : '+ err);
	        reject();
	      }
	      resolve(result);
	      });  
	    });
	 });
	});
}