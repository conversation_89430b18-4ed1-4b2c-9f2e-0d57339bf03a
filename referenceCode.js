'use strict';


var admin = require("firebase-admin");
var serviceAccountOld = require("./oldlikepoint.json");

const dotenv = require('dotenv');
dotenv.config();
const express = require('express')
const app = express()
const bodyParser = require('body-parser');
var session = require('express-session')
var cryptico = require('cryptico');
var generator = require('generate-password');
var Promise = require('promise');
var await = require('await')``
var moment = require('moment-timezone');
// var moment = require('moment');
var elasticsearch = require('elasticsearch');
var await = require('await');
var request = require('request');
var bip39 = require('bip39')
var btcLib = require('bitcoinjs-lib');
var bs58check = require('bs58check');
const twilio = require('twilio');
var jwt = require('jsonwebtoken');
var crypto = require('crypto'),
  algorithm = 'aes-256-gcm',
  password = process.env.ENCRYPTION_KEY;

  //ipfs
const ipfsClient = require('ipfs-http-client')
const ipfsCluster = require('ipfs-cluster-api-remove-name')
const fs = require('fs')
//yep
const algorithmToFront = 'aes-256-cbc';
var algorithmToFrontGCM = 'aes-256-gcm';
var serviceAccount = require("./"+process.env.FIREBASE);
const passwordToFront = process.env.passwordToFront;
const ivToFront = process.env.ivToFront;
// const passwordToFront = process.env.ENCRYPTION_KEY_FRONT;
// const ivToFront = process.env.IV_FRONT; 
const $log = require('log');
const bcrypt = require('bcrypt');
const IV_LENGTH = 16;
const ethers = require('ethers');
const accountSid = process.env.TWILIO_KEY;
const authToken = process.env.TWILIO_SECRET;
const clientTwilio = require('twilio')(accountSid, authToken);
const secrets = require('secret-sharing.js');
const uuidv4 = require('uuid/v4');
const randomstring = require("randomstring");
const uuidAPIKey = require('uuid-apikey');
const aws = require('aws-sdk');
const web3 = require('web3');
const ed25519 = require('ed25519-hd-key')
const mysql      = require('mysql');
const kms = new aws.KMS({
    accessKeyId: '********************', //credentials for your IAM user
    secretAccessKey: 'XtBvGx6pX+UssPhxbCc1xSrITpNf9IPQZuTIn/2a', //credentials for your IAM user
    region: 'ap-southeast-1'
});

// import { rateLimiterUsingThirdParty } from './middlewares';
var rateLimiterUsingThirdParty = require('./middlewares');
//console.log(JSON.stringify());

const auth = require('./middlewares/auth.js');

const contractAddressSmartContract = process.env.CONTRACT_LIKE;
const abiSmartContract = process.env.ABI_LIKE;

//delay function
let sleep = (time) => new Promise((resolve) => setTimeout(resolve, time));
// inQuireQR();


// ค้นหา refCode บันทึก address ของคนแนะนำ เข้่าสถานะ pending ก่อนจ่าย likepoint
async function pendingRefCode(registUid,refCode){
	let now = new Date().getTime();
	let datetime = moment.tz(now, "Asia/Bangkok").format("YYYY-MM-DD HH:mm:ss");
	let logsData = {
		"refCode":refCode,
		"timestamp": now,
		"datetime": datetime,
		"registUid": registUid,
	}
	// เก็บ logs
	updateRefCollection("logsRef", registUid, logsData);
    let uid = await getUidFromRefCode(refCode);
    // console.log(uid);
    if(uid){
		let address = await getAddressFromUid(uid);
		// console.log(address);
		let data = {
			"uid": uid,
			"refCode":refCode,
			"address": address,
			"timestamp": now,
			"datetime": datetime,
			"registUid": registUid,
		}
		let updated = await updateRefCollection("pending", uid+"-"+data.timestamp, data);
			return updated;
    }else{
		return false;
	}
}
// หา uid จาก refCode
async function getUidFromRefCode(refCode){
	const db = admin.firestore();
	let users = db.collection("users");
    let snapshot = await users.where('selfCode', '==', refCode).get();
    if(snapshot.empty){
        console.log('getUidFromRefCode.. No matching documents.');
        return false;
    }else{
        let uid = "";
        snapshot.forEach((doc) => {
            uid = doc.id;
        });
        return uid;
    }
}
// หา address จาก uid
async function getAddressFromUid(uid){
	const db = admin.firestore();
	let addressDNS = db.collection("addressDNS");
    let snapshot = await addressDNS.doc(uid).get();
    if(!snapshot.exists){
        console.log('getAddressFromUid.. No matching documents.');
        return false;
    }else{
        let address = snapshot._fieldsProto.address.stringValue;
        return address;
    }
}
// update pending refCode firebase
async function updateRefCollection(col,id,data){
	const db = admin.firestore();
	let pending = db.collection("logs").doc("rewardsReference").collection(col);
	let add = await pending.doc(id).set(data);
    if(add){
        return true;
    }else{
		console.log("resend collection '"+col+"' add to firebase!")
		await sleep(2000);
        return updateRefCollection(col,id,data);
    }
}

// end refCode function


module.exports.pendingRefCode = pendingRefCode;